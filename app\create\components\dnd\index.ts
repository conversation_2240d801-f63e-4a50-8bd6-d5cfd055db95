// 拖拽系统组件统一导出

export { DragDropProvider, DragTypes, DragPreview } from './DragDropProvider'
export { DraggableMediaItem } from './DraggableMediaItem'
export { DraggableTimelineClip } from './DraggableTimelineClip'
export { TimelineDropZone, TrackDropZoneLabel } from './TimelineDropZone'

// 类型导出
export type { DragItem, DropResult, DragType } from './DragDropProvider'

// 拖拽相关的工具函数
export const dragUtils = {
  // 计算拖拽预览位置
  calculateDropPosition: (
    clientOffset: { x: number; y: number },
    containerBounds: DOMRect,
    pixelsPerSecond: number,
    trackHeight: number,
    rulerHeight: number
  ) => {
    const relativeX = clientOffset.x - containerBounds.left
    const relativeY = clientOffset.y - containerBounds.top
    
    const time = Math.max(0, relativeX / pixelsPerSecond)
    const track = Math.max(0, Math.floor((relativeY - rulerHeight) / trackHeight))
    
    return { time, track }
  },

  // 检查拖拽兼容性
  isCompatibleDrop: (
    dragType: string,
    dropZone: 'timeline' | 'library' | 'trash',
    targetTrackType?: 'video' | 'audio' | 'subtitle'
  ): boolean => {
    if (dropZone === 'trash') return true
    if (dropZone === 'library') return false
    
    // 时间轴放置规则
    if (!targetTrackType) return true
    
    const dragClipType = dragType.includes('VIDEO') ? 'video' :
                        dragType.includes('AUDIO') ? 'audio' :
                        dragType.includes('SUBTITLE') ? 'subtitle' : 'unknown'
    
    return dragClipType === targetTrackType || dragClipType === 'unknown'
  },

  // 计算吸附时间
  snapToGrid: (
    time: number,
    gridInterval: number = 1,
    snapThreshold: number = 0.1
  ): number => {
    const snappedTime = Math.round(time / gridInterval) * gridInterval
    return Math.abs(time - snappedTime) <= snapThreshold ? snappedTime : time
  },

  // 检查时间重叠
  checkTimeOverlap: (
    startTime1: number,
    endTime1: number,
    startTime2: number,
    endTime2: number
  ): boolean => {
    return !(endTime1 <= startTime2 || startTime1 >= endTime2)
  },

  // 查找可用的放置位置
  findAvailablePosition: (
    clips: Array<{ startTime: number; endTime: number; track: number }>,
    duration: number,
    preferredTime: number,
    preferredTrack: number,
    maxTracks: number = 10
  ): { time: number; track: number } | null => {
    // 首先尝试首选位置
    const hasOverlap = clips.some(clip => 
      clip.track === preferredTrack &&
      dragUtils.checkTimeOverlap(preferredTime, preferredTime + duration, clip.startTime, clip.endTime)
    )
    
    if (!hasOverlap) {
      return { time: preferredTime, track: preferredTrack }
    }
    
    // 在首选轨道上寻找空隙
    const trackClips = clips
      .filter(c => c.track === preferredTrack)
      .sort((a, b) => a.startTime - b.startTime)
    
    // 检查开头是否有空间
    if (trackClips.length === 0 || trackClips[0].startTime >= duration) {
      return { time: 0, track: preferredTrack }
    }
    
    // 检查片段之间的空隙
    for (let i = 0; i < trackClips.length - 1; i++) {
      const gapStart = trackClips[i].endTime
      const gapEnd = trackClips[i + 1].startTime
      
      if (gapEnd - gapStart >= duration) {
        const time = Math.max(gapStart, preferredTime)
        if (time + duration <= gapEnd) {
          return { time, track: preferredTrack }
        }
      }
    }
    
    // 检查末尾是否有空间
    const lastClip = trackClips[trackClips.length - 1]
    const endTime = Math.max(lastClip.endTime, preferredTime)
    
    // 尝试其他轨道
    for (let track = 0; track < maxTracks; track++) {
      if (track === preferredTrack) continue
      
      const hasOverlapInTrack = clips.some(clip => 
        clip.track === track &&
        dragUtils.checkTimeOverlap(preferredTime, preferredTime + duration, clip.startTime, clip.endTime)
      )
      
      if (!hasOverlapInTrack) {
        return { time: preferredTime, track }
      }
    }
    
    return null
  }
}

// 拖拽状态管理 Hook
import { useState, useCallback } from 'react'

export const useDragState = () => {
  const [dragState, setDragState] = useState<{
    isDragging: boolean
    draggedItem: DragItem | null
    previewPosition: { x: number; y: number; track: number; time: number } | null
  }>({
    isDragging: false,
    draggedItem: null,
    previewPosition: null
  })

  const startDrag = useCallback((item: DragItem) => {
    setDragState({
      isDragging: true,
      draggedItem: item,
      previewPosition: null
    })
  }, [])

  const updatePreview = useCallback((x: number, y: number, track: number, time: number) => {
    setDragState(prev => ({
      ...prev,
      previewPosition: { x, y, track, time }
    }))
  }, [])

  const endDrag = useCallback(() => {
    setDragState({
      isDragging: false,
      draggedItem: null,
      previewPosition: null
    })
  }, [])

  return {
    ...dragState,
    startDrag,
    updatePreview,
    endDrag
  }
}
