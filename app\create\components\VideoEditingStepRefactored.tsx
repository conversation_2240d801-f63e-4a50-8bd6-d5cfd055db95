"use client"

import React, { useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Video,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Upload,
  Download,
  Save,
  Eye,
  Sparkles,
  RefreshCw,
  Music,
  Type
} from "lucide-react"

// 导入新的状态管理和组件
import { useVideoEditor, VideoClip } from '../stores'
import { Timeline } from './timeline'
import { DragDropProvider, DraggableMediaItem } from './dnd'

// SWR 相关导入保持不变
import useSWR from "swr"

// 保持原有的 SWR fetcher 函数
const fetchVideoTaskStatus = async (key: string) => {
  const [, projectId, userId, shotIdsStr] = key.split('|')
  const shotIds = shotIdsStr.split(',')

  const response = await fetch('/api/video-records/cached', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      projectId,
      userId,
      shotIds
    })
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  return response.json()
}

const fetchImageTaskStatus = async (key: string) => {
  const [, projectId, userId, shotNumbersStr] = key.split('|')
  const shotNumbers = shotNumbersStr.split(',').map(num => parseInt(num))

  const response = await fetch('/api/image-records/cached', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      projectId,
      userId,
      shotNumbers
    })
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  return response.json()
}

// 保持原有的 SWR hook
const useCombinedTaskStatus = (projectId?: string, userId?: string, shotsWithImages?: any[]) => {
  const shotIds = React.useMemo(() => {
    return shotsWithImages?.map(shot => shot.shotId).filter(Boolean) as string[] || []
  }, [shotsWithImages])

  const shotNumbers = React.useMemo(() => {
    return shotsWithImages?.map(shot => shot.shotNumber).filter(Boolean) as number[] || []
  }, [shotsWithImages])

  const videoCacheKey = projectId && userId && shotIds?.length
    ? `video-tasks|${projectId}|${userId}|${shotIds.join(',')}`
    : null

  const imageCacheKey = projectId && userId && shotNumbers?.length
    ? `image-tasks|${projectId}|${userId}|${shotNumbers.join(',')}`
    : null

  const { data: videoTaskData, error: videoError, isLoading: isVideoLoading, mutate: mutateVideo } = useSWR(
    videoCacheKey,
    fetchVideoTaskStatus,
    {
      dedupingInterval: 5 * 60 * 1000,
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      refreshInterval: 2 * 60 * 1000,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
      refreshWhenHidden: false,
      refreshWhenOffline: false,
    }
  )

  const { data: imageTaskData, error: imageError, isLoading: isImageLoading, mutate: mutateImage } = useSWR(
    imageCacheKey,
    fetchImageTaskStatus,
    {
      dedupingInterval: 5 * 60 * 1000,
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      refreshInterval: 2 * 60 * 1000,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
      refreshWhenHidden: false,
      refreshWhenOffline: false,
    }
  )

  const refreshTaskStatus = useCallback(() => {
    const promises = []
    if (mutateVideo) promises.push(mutateVideo())
    if (mutateImage) promises.push(mutateImage())
    return Promise.all(promises)
  }, [mutateVideo, mutateImage])

  return {
    videoTaskData,
    imageTaskData,
    isVideoLoading,
    isImageLoading,
    videoError,
    imageError,
    refreshTaskStatus,
    mutateVideo,
    mutateImage
  }
}

interface VideoEditingStepRefactoredProps {
  projectId?: string
  userId?: string
  scriptData?: any
  shotsWithImages?: any[]
}

export function VideoEditingStepRefactored({
  projectId,
  userId,
  scriptData,
  shotsWithImages,
}: VideoEditingStepRefactoredProps) {
  // 使用新的状态管理
  const {
    videoClips,
    timeline,
    ui,
    actions,
    setCacheData,
    setCacheLoading,
    setCacheError
  } = useVideoEditor()

  // 使用原有的 SWR 缓存逻辑
  const {
    videoTaskData,
    imageTaskData,
    isVideoLoading,
    isImageLoading,
    videoError,
    imageError,
    refreshTaskStatus
  } = useCombinedTaskStatus(projectId, userId, shotsWithImages)

  // 同步缓存数据到新的状态管理
  useEffect(() => {
    setCacheData(videoTaskData, imageTaskData)
    setCacheLoading(isVideoLoading, isImageLoading)
    setCacheError(videoError, imageError)
  }, [videoTaskData, imageTaskData, isVideoLoading, isImageLoading, videoError, imageError, setCacheData, setCacheLoading, setCacheError])

  // 智能生成视频片段（保持原有逻辑）
  const generateSmartVideoClips = useCallback((): VideoClip[] => {
    if (!shotsWithImages || shotsWithImages.length === 0) {
      console.log('没有场景数据可用于生成视频片段')
      return []
    }

    console.log('🎬 [SMART-CLIPS] 开始智能生成视频片段')

    const newVideoClips: VideoClip[] = shotsWithImages.map((shot, index) => {
      const duration = 5
      const startTime = index * duration

      // 检查视频缓存
      let hasVideoCache = false
      let videoUrl: string | undefined
      let videoTaskStatus: string | undefined

      if (videoTaskData?.tasks && shot.shotId) {
        const videoTasks = videoTaskData.tasks[shot.shotId]
        if (videoTasks && videoTasks.length > 0) {
          const latestVideoTask = videoTasks[0]
          videoTaskStatus = latestVideoTask.status

          if (latestVideoTask.status === 'completed' && latestVideoTask.generated_videos) {
            hasVideoCache = true
            const firstVideo = latestVideoTask.generated_videos[0]
            if (firstVideo) {
              videoUrl = firstVideo.video_url
            }
          }
        }
      }

      // 检查图片缓存
      let hasImageCache = false
      let thumbnailUrl = '/placeholder.svg'
      let imageTaskStatus: string | undefined

      if (imageTaskData?.tasks && shot.shotNumber) {
        const imageTasks = imageTaskData.tasks[shot.shotNumber]
        if (imageTasks && imageTasks.length > 0) {
          const latestImageTask = imageTasks[0]
          imageTaskStatus = latestImageTask.status

          if (latestImageTask.status === 'completed' && latestImageTask.generated_images) {
            hasImageCache = true
            const firstImage = latestImageTask.generated_images[0]
            if (firstImage) {
              thumbnailUrl = firstImage.cdn_url || firstImage.image_url
            }
          }
        }
      }

      // 如果没有缓存数据，使用原始的shotsWithImages数据
      if (!hasVideoCache && !hasImageCache) {
        const primaryImage = shot.images?.find((img: any) => img.isPrimary) || shot.images?.[0]
        if (primaryImage) {
          thumbnailUrl = primaryImage.url
          hasImageCache = true
        }
        videoUrl = shot.videoUrl
      }

      // 确定缓存源
      let cacheSource: 'video' | 'image' | 'none' = 'none'
      if (hasVideoCache) {
        cacheSource = 'video'
      } else if (hasImageCache) {
        cacheSource = 'image'
      }

      return {
        id: `smart-clip-${shot.shotId || shot.shotNumber || index}`,
        name: `镜头 ${shot.shotNumber || index + 1}`,
        duration: duration,
        thumbnail: thumbnailUrl,
        videoUrl: videoUrl,
        startTime: startTime,
        endTime: startTime + duration,
        track: 0,
        sceneData: shot,
        hasVideoCache,
        hasImageCache,
        videoTaskStatus,
        imageTaskStatus,
        cacheSource,
        lastCacheCheck: Date.now()
      }
    })

    console.log(`🎬 [SMART-CLIPS] 智能生成完成，共 ${newVideoClips.length} 个片段`)
    return newVideoClips
  }, [shotsWithImages, videoTaskData, imageTaskData])

  // 智能剪辑处理
  const handleSmartEditing = useCallback(() => {
    console.log('🎬 [SMART-EDITING] 开始智能剪辑')
    
    const newVideoClips = generateSmartVideoClips()
    
    // 使用批量操作来添加所有片段
    actions.startBatch()
    newVideoClips.forEach(clip => {
      actions.addVideoClip(clip)
    })
    actions.endBatch()

    console.log('🎬 [SMART-EDITING] 智能剪辑完成，生成了', newVideoClips.length, '个视频片段')
  }, [generateSmartVideoClips, actions])

  // 初始化时自动生成片段
  useEffect(() => {
    if (shotsWithImages && shotsWithImages.length > 0 && !isVideoLoading && !isImageLoading && videoClips.length === 0) {
      handleSmartEditing()
    }
  }, [shotsWithImages, isVideoLoading, isImageLoading, videoClips.length, handleSmartEditing])

  // 片段选择处理
  const handleClipSelect = useCallback((clipId: string) => {
    console.log('选择片段:', clipId)
  }, [])

  // 时间变化处理
  const handleTimeChange = useCallback((time: number) => {
    console.log('时间变化:', time)
  }, [])

  return (
    <DragDropProvider>
      <div className="h-full flex bg-gradient-to-br from-slate-50/50 to-blue-50/30">
      {/* 左侧媒体库 */}
      <div className="w-80 bg-white/80 backdrop-blur-sm border-r border-slate-200/60 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center">
              <Video className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-slate-800">媒体库</h2>
              <p className="text-sm text-slate-600">拖拽素材到时间轴</p>
            </div>
          </div>

          {/* 智能缓存状态总览 */}
          {shotsWithImages && shotsWithImages.length > 0 && (
            <div className="mb-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center">
                    {isVideoLoading || isImageLoading ? (
                      <RefreshCw className="w-3 h-3 text-indigo-600 animate-spin" />
                    ) : (videoTaskData || imageTaskData) ? (
                      <span className="text-xs">🎬</span>
                    ) : (
                      <span className="text-xs">📝</span>
                    )}
                  </div>
                  <span className="text-sm font-semibold text-indigo-800">
                    {isVideoLoading || isImageLoading ? '正在加载缓存...' : '智能缓存状态'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {(videoTaskData?.cached || imageTaskData?.cached) && (
                    <Badge className="bg-green-100 text-green-800 border-green-200 text-xs px-2 py-1">
                      来自缓存
                    </Badge>
                  )}
                  {(videoError || imageError) && (
                    <Badge className="bg-red-100 text-red-800 border-red-200 text-xs px-2 py-1">
                      加载失败
                    </Badge>
                  )}
                </div>
              </div>
              {(videoTaskData || imageTaskData) && (
                <div className="mt-2 text-xs text-indigo-600">
                  视频: {videoTaskData?.totalCompletedVideos || 0} 个 |
                  图片: {imageTaskData?.totalCompletedImages || 0} 张
                </div>
              )}
            </div>
          )}

          {/* 视频素材列表 */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-semibold text-slate-700">视频素材</h3>
              <Badge variant="secondary" className="text-xs">
                {videoClips.length} 个
              </Badge>
            </div>
            <div className="space-y-3">
              {videoClips.map((clip) => (
                <DraggableMediaItem
                  key={clip.id}
                  clip={clip}
                  onSelect={handleClipSelect}
                  isSelected={timeline.selectedClips.includes(clip.id)}
                />
              ))}
            </div>
          </div>

          {/* 音频素材 */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-semibold text-slate-700">音频素材</h3>
              <Button size="sm" variant="outline" className="h-7 px-2 text-xs">
                <Upload className="w-3 h-3 mr-1" />
                上传
              </Button>
            </div>
            <div className="p-4 border-2 border-dashed border-slate-300 rounded-lg text-center">
              <Music className="w-8 h-8 text-slate-400 mx-auto mb-2" />
              <p className="text-xs text-slate-500">拖拽音频文件到此处</p>
            </div>
          </div>

          {/* 字幕素材 */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-semibold text-slate-700">字幕素材</h3>
              <Button size="sm" variant="outline" className="h-7 px-2 text-xs">
                <Type className="w-3 h-3 mr-1" />
                添加
              </Button>
            </div>
            <div className="p-4 border-2 border-dashed border-slate-300 rounded-lg text-center">
              <Type className="w-8 h-8 text-slate-400 mx-auto mb-2" />
              <p className="text-xs text-slate-500">点击添加字幕</p>
            </div>
          </div>
        </div>
      </div>

      {/* 中央内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部工具栏 */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <h1 className="text-xl font-bold text-slate-800">智能剪辑</h1>
              <Badge className="bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border-emerald-200">
                AI内容生成项目
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshTaskStatus}
                disabled={isVideoLoading || isImageLoading}
              >
                <RefreshCw className={`w-4 h-4 mr-1 ${(isVideoLoading || isImageLoading) ? 'animate-spin' : ''}`} />
                刷新缓存
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={videoClips.length === 0}
              >
                <Eye className="w-4 h-4 mr-1" />
                {timeline.isPlaying ? '暂停预览' : '开始预览'}
              </Button>
              <Button
                size="sm"
                className="bg-emerald-500 hover:bg-emerald-600"
                onClick={handleSmartEditing}
              >
                <Sparkles className="w-4 h-4 mr-1" />
                智能剪辑
              </Button>
            </div>
          </div>
        </div>

        {/* 预览区域 */}
        <div className="flex-1 p-6 pb-0">
          <Card className="h-full">
            <CardContent className="p-6 h-full flex flex-col">
              <div className="flex-1 bg-black rounded-lg flex items-center justify-center mb-4 relative overflow-hidden">
                <div className="text-white text-center">
                  <div className="text-6xl mb-4">🎬</div>
                  <p className="text-xl mb-2">视频预览区域</p>
                  <p className="text-sm opacity-70">使用新的时间轴系统</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 新的时间轴编辑器 */}
        <div className="p-6 pt-0">
          <Timeline
            onClipSelect={handleClipSelect}
            onTimeChange={handleTimeChange}
          />
        </div>
      </div>

      {/* 右侧属性面板 */}
      <div className="w-80 bg-white/80 backdrop-blur-sm border-l border-slate-200/60 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-slate-800">属性面板</h2>
              <p className="text-sm text-slate-600">编辑参数设置</p>
            </div>
          </div>

          {/* 项目统计 */}
          <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl">
            <h3 className="text-sm font-semibold text-blue-800 mb-3">项目统计</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-blue-600">视频片段:</span>
                <span className="font-medium">{videoClips.length} 个</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-600">总时长:</span>
                <span className="font-medium">{Math.floor(timeline.duration / 60)}:{Math.floor(timeline.duration % 60).toString().padStart(2, '0')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-600">缩放级别:</span>
                <span className="font-medium">{Math.round(timeline.zoomLevel * 100)}%</span>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Button className="w-full bg-emerald-500 hover:bg-emerald-600">
              <Sparkles className="w-4 h-4 mr-2" />
              应用AI增强
            </Button>
            <Button variant="outline" className="w-full">
              <Save className="w-4 h-4 mr-2" />
              保存项目
            </Button>
            <Button variant="outline" className="w-full">
              <Download className="w-4 h-4 mr-2" />
              导出视频
            </Button>
          </div>
        </div>
      </div>
      </div>
    </DragDropProvider>
  )
}
