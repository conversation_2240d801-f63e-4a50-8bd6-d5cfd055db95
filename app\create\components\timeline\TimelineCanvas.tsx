"use client"

import React, { useRef, useEffect, useCallback } from 'react'
import { Stage, Layer, Rect, Line, Text, Group } from 'react-konva'
import Konva from 'konva'
import { useVideoEditor, VideoClip, AudioClip, SubtitleClip } from '../../stores'
import { DraggableTimelineClip, TimelineDropZone, DragItem } from '../dnd'

interface TimelineCanvasProps {
  width: number
  height: number
  onTimeChange?: (time: number) => void
  onClipSelect?: (clipId: string) => void
  onClipMove?: (clipId: string, newStartTime: number, newTrack: number) => void
}

export const TimelineCanvas: React.FC<TimelineCanvasProps> = ({
  width,
  height,
  onTimeChange,
  onClipSelect,
  onClipMove
}) => {
  const stageRef = useRef<Konva.Stage>(null)
  const {
    videoClips,
    audioClips,
    subtitleClips,
    timeline,
    viewport,
    ui,
    timelineUtils,
    setCurrentTime,
    selectClips
  } = useVideoEditor()

  // 轨道配置
  const TRACK_HEIGHT = 60
  const TRACK_PADDING = 4
  const RULER_HEIGHT = 40
  const GRID_COLOR = '#e5e7eb'
  const RULER_COLOR = '#374151'
  const PLAYHEAD_COLOR = '#ef4444'

  // 计算轨道数量
  const maxVideoTrack = Math.max(0, ...videoClips.map(c => c.track))
  const maxAudioTrack = Math.max(0, ...audioClips.map(c => c.track))
  const maxSubtitleTrack = Math.max(0, ...subtitleClips.map(c => c.track))
  const totalTracks = Math.max(maxVideoTrack + 1, maxAudioTrack + 1, maxSubtitleTrack + 1, 3)

  // 绘制网格
  const renderGrid = useCallback(() => {
    if (!ui.showGrid) return []

    const elements = []
    const { pixelsPerSecond } = viewport
    const { visibleTimeRange } = viewport

    // 计算时间刻度
    const { major, minor } = timelineUtils.calculateTimeMarks(timeline.duration)

    // 绘制垂直网格线（时间刻度）
    major.forEach((time, index) => {
      if (time >= visibleTimeRange.start && time <= visibleTimeRange.end) {
        const x = timelineUtils.timeToPixels(time - visibleTimeRange.start)
        elements.push(
          <Line
            key={`major-${index}`}
            points={[x, RULER_HEIGHT, x, height]}
            stroke={GRID_COLOR}
            strokeWidth={1}
            opacity={0.8}
          />
        )
      }
    })

    minor.forEach((time, index) => {
      if (time >= visibleTimeRange.start && time <= visibleTimeRange.end) {
        const x = timelineUtils.timeToPixels(time - visibleTimeRange.start)
        elements.push(
          <Line
            key={`minor-${index}`}
            points={[x, RULER_HEIGHT, x, height]}
            stroke={GRID_COLOR}
            strokeWidth={0.5}
            opacity={0.4}
          />
        )
      }
    })

    // 绘制水平网格线（轨道分隔）
    for (let i = 0; i <= totalTracks; i++) {
      const y = RULER_HEIGHT + i * TRACK_HEIGHT
      elements.push(
        <Line
          key={`track-${i}`}
          points={[0, y, width, y]}
          stroke={GRID_COLOR}
          strokeWidth={1}
          opacity={0.6}
        />
      )
    }

    return elements
  }, [ui.showGrid, viewport, timelineUtils, timeline.duration, width, height, totalTracks])

  // 绘制时间标尺
  const renderRuler = useCallback(() => {
    const elements = []
    const { visibleTimeRange } = viewport
    const { major } = timelineUtils.calculateTimeMarks(timeline.duration)

    // 标尺背景
    elements.push(
      <Rect
        key="ruler-bg"
        x={0}
        y={0}
        width={width}
        height={RULER_HEIGHT}
        fill="#f9fafb"
        stroke={GRID_COLOR}
        strokeWidth={1}
      />
    )

    // 时间刻度标签
    major.forEach((time, index) => {
      if (time >= visibleTimeRange.start && time <= visibleTimeRange.end) {
        const x = timelineUtils.timeToPixels(time - visibleTimeRange.start)
        const timeText = timelineUtils.formatTime(time)
        
        elements.push(
          <Text
            key={`time-${index}`}
            x={x + 4}
            y={8}
            text={timeText}
            fontSize={12}
            fontFamily="monospace"
            fill={RULER_COLOR}
          />
        )
      }
    })

    return elements
  }, [viewport, timelineUtils, timeline.duration, width])

  // 绘制播放头
  const renderPlayhead = useCallback(() => {
    const { visibleTimeRange } = viewport
    const currentTime = timeline.currentTime

    if (currentTime < visibleTimeRange.start || currentTime > visibleTimeRange.end) {
      return []
    }

    const x = timelineUtils.timeToPixels(currentTime - visibleTimeRange.start)

    return [
      <Line
        key="playhead"
        points={[x, 0, x, height]}
        stroke={PLAYHEAD_COLOR}
        strokeWidth={2}
        opacity={0.8}
      />,
      <Rect
        key="playhead-handle"
        x={x - 6}
        y={0}
        width={12}
        height={RULER_HEIGHT}
        fill={PLAYHEAD_COLOR}
        cornerRadius={2}
        draggable
        onDragMove={(e) => {
          const newX = e.target.x() + 6
          const newTime = timelineUtils.pixelsToTime(newX) + visibleTimeRange.start
          const clampedTime = Math.max(0, Math.min(newTime, timeline.duration))
          setCurrentTime(clampedTime)
          onTimeChange?.(clampedTime)
        }}
        onDragEnd={(e) => {
          e.target.x(x - 6) // 重置位置，因为时间由状态管理
        }}
      />
    ]
  }, [viewport, timeline, timelineUtils, height, setCurrentTime, onTimeChange])

  // 绘制视频片段
  const renderVideoClips = useCallback(() => {
    const { visibleTimeRange } = viewport
    
    return videoClips
      .filter(clip => !(clip.endTime < visibleTimeRange.start || clip.startTime > visibleTimeRange.end))
      .map((clip) => {
        const x = timelineUtils.timeToPixels(Math.max(clip.startTime, visibleTimeRange.start) - visibleTimeRange.start)
        const width = timelineUtils.timeToPixels(
          Math.min(clip.endTime, visibleTimeRange.end) - Math.max(clip.startTime, visibleTimeRange.start)
        )
        const y = RULER_HEIGHT + clip.track * TRACK_HEIGHT + TRACK_PADDING
        const clipHeight = TRACK_HEIGHT - TRACK_PADDING * 2
        
        const isSelected = timeline.selectedClips.includes(clip.id)
        
        return (
          <Group key={clip.id}>
            {/* 片段背景 */}
            <Rect
              x={x}
              y={y}
              width={width}
              height={clipHeight}
              fill={isSelected ? '#3b82f6' : '#10b981'}
              stroke={isSelected ? '#1d4ed8' : '#059669'}
              strokeWidth={isSelected ? 2 : 1}
              cornerRadius={4}
              draggable
              onClick={() => {
                selectClips([clip.id])
                onClipSelect?.(clip.id)
              }}
              onDragMove={(e) => {
                const newX = e.target.x()
                const newTime = timelineUtils.pixelsToTime(newX) + visibleTimeRange.start
                const snappedTime = timelineUtils.snapTimeToGrid(newTime)
                
                // 计算新轨道
                const newY = e.target.y()
                const newTrack = Math.max(0, Math.floor((newY - RULER_HEIGHT) / TRACK_HEIGHT))
                
                // 更新预览位置
                e.target.x(timelineUtils.timeToPixels(snappedTime - visibleTimeRange.start))
                e.target.y(RULER_HEIGHT + newTrack * TRACK_HEIGHT + TRACK_PADDING)
              }}
              onDragEnd={(e) => {
                const newX = e.target.x()
                const newTime = timelineUtils.pixelsToTime(newX) + visibleTimeRange.start
                const snappedTime = timelineUtils.snapTimeToGrid(newTime)
                
                const newY = e.target.y()
                const newTrack = Math.max(0, Math.floor((newY - RULER_HEIGHT) / TRACK_HEIGHT))
                
                onClipMove?.(clip.id, snappedTime, newTrack)
                
                // 重置位置，让状态管理处理
                e.target.x(x)
                e.target.y(y)
              }}
            />
            
            {/* 缩略图 */}
            {ui.showThumbnails && clip.thumbnail && (
              <Rect
                x={x + 2}
                y={y + 2}
                width={Math.min(width - 4, clipHeight - 4)}
                height={clipHeight - 4}
                fillPatternImage={(() => {
                  const img = new Image()
                  img.src = clip.thumbnail
                  return img
                })()}
                cornerRadius={2}
              />
            )}
            
            {/* 片段标题 */}
            <Text
              x={x + 8}
              y={y + clipHeight / 2 - 6}
              text={clip.name}
              fontSize={11}
              fontFamily="Arial"
              fill="white"
              width={width - 16}
              ellipsis={true}
            />
            
            {/* 缓存状态指示器 */}
            <Rect
              x={x + width - 20}
              y={y + 4}
              width={16}
              height={12}
              fill={
                clip.cacheSource === 'video' ? '#3b82f6' :
                clip.cacheSource === 'image' ? '#10b981' : '#6b7280'
              }
              cornerRadius={2}
            />
            <Text
              x={x + width - 18}
              y={y + 6}
              text={
                clip.cacheSource === 'video' ? '🎥' :
                clip.cacheSource === 'image' ? '🖼️' : '📝'
              }
              fontSize={8}
              fill="white"
            />
          </Group>
        )
      })
  }, [videoClips, viewport, timelineUtils, timeline.selectedClips, ui.showThumbnails, selectClips, onClipSelect, onClipMove])

  // 绘制音频片段
  const renderAudioClips = useCallback(() => {
    const { visibleTimeRange } = viewport
    
    return audioClips
      .filter(clip => !(clip.endTime < visibleTimeRange.start || clip.startTime > visibleTimeRange.start))
      .map((clip) => {
        const x = timelineUtils.timeToPixels(Math.max(clip.startTime, visibleTimeRange.start) - visibleTimeRange.start)
        const width = timelineUtils.timeToPixels(
          Math.min(clip.endTime, visibleTimeRange.end) - Math.max(clip.startTime, visibleTimeRange.start)
        )
        const y = RULER_HEIGHT + (totalTracks + clip.track) * TRACK_HEIGHT + TRACK_PADDING
        const clipHeight = TRACK_HEIGHT - TRACK_PADDING * 2
        
        const isSelected = timeline.selectedClips.includes(clip.id)
        
        return (
          <Group key={clip.id}>
            {/* 片段背景 */}
            <Rect
              x={x}
              y={y}
              width={width}
              height={clipHeight}
              fill={isSelected ? '#8b5cf6' : '#6366f1'}
              stroke={isSelected ? '#7c3aed' : '#4f46e5'}
              strokeWidth={isSelected ? 2 : 1}
              cornerRadius={4}
              draggable
              onClick={() => {
                selectClips([clip.id])
                onClipSelect?.(clip.id)
              }}
            />
            
            {/* 波形显示占位 */}
            {ui.showWaveforms && (
              <Rect
                x={x + 4}
                y={y + clipHeight / 2 - 2}
                width={width - 8}
                height={4}
                fill="#a855f7"
                opacity={0.6}
              />
            )}
            
            {/* 片段标题 */}
            <Text
              x={x + 8}
              y={y + 8}
              text={clip.name}
              fontSize={10}
              fontFamily="Arial"
              fill="white"
              width={width - 16}
              ellipsis={true}
            />
          </Group>
        )
      })
  }, [audioClips, viewport, timelineUtils, timeline.selectedClips, ui.showWaveforms, totalTracks, selectClips, onClipSelect])

  // 处理画布点击
  const handleStageClick = useCallback((e: Konva.KonvaEventObject<MouseEvent>) => {
    const pos = e.target.getStage()?.getPointerPosition()
    if (!pos) return

    // 如果点击的是空白区域，清除选择
    if (e.target === e.target.getStage()) {
      selectClips([])
    }

    // 如果点击的是标尺区域，移动播放头
    if (pos.y <= RULER_HEIGHT) {
      const clickTime = timelineUtils.pixelsToTime(pos.x) + viewport.visibleTimeRange.start
      const clampedTime = Math.max(0, Math.min(clickTime, timeline.duration))
      setCurrentTime(clampedTime)
      onTimeChange?.(clampedTime)
    }
  }, [timelineUtils, viewport, timeline.duration, setCurrentTime, onTimeChange, selectClips])

  // 处理滚轮缩放
  const handleWheel = useCallback((e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault()

    const stage = e.target.getStage()
    if (!stage) return

    const pointer = stage.getPointerPosition()
    if (!pointer) return

    // 检查是否按住 Ctrl 键进行缩放
    if (e.evt.ctrlKey) {
      const scaleBy = 1.1
      const oldScale = timeline.zoomLevel
      const newScale = e.evt.deltaY > 0 ? oldScale / scaleBy : oldScale * scaleBy

      // 限制缩放范围
      const clampedScale = Math.max(0.1, Math.min(newScale, 10))

      // 更新缩放级别（这会触发视口更新）
      console.log('缩放到:', clampedScale)
    } else {
      // 水平滚动
      const scrollSpeed = 50
      const newScrollX = timeline.scrollPosition + (e.evt.deltaY > 0 ? scrollSpeed : -scrollSpeed)
      const clampedScrollX = Math.max(0, newScrollX)

      console.log('滚动到:', clampedScrollX)
    }
  }, [timeline.zoomLevel, timeline.scrollPosition])

  return (
    <div className="timeline-canvas border border-gray-200 rounded-lg overflow-hidden">
      <Stage
        ref={stageRef}
        width={width}
        height={height}
        onClick={handleStageClick}
        onWheel={handleWheel}
      >
        <Layer>
          {/* 背景 */}
          <Rect
            x={0}
            y={0}
            width={width}
            height={height}
            fill="#ffffff"
          />
          
          {/* 网格 */}
          {renderGrid()}
          
          {/* 时间标尺 */}
          {renderRuler()}
          
          {/* 视频片段 */}
          {renderVideoClips()}
          
          {/* 音频片段 */}
          {renderAudioClips()}
          
          {/* 播放头 */}
          {renderPlayhead()}
        </Layer>
      </Stage>
    </div>
  )
}
