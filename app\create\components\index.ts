// 视频编辑器组件统一导出

// 主编辑器组件
export { VideoEditor } from './VideoEditor'
export { VideoEditingStepRefactored } from './VideoEditingStepRefactored'
export { IntegratedVideoEditor } from './IntegratedVideoEditor'

// 状态管理
export * from '../stores'

// 时间轴系统
export * from './timeline'

// 拖拽系统
export * from './dnd'

// 音频系统
export * from './audio'

// 字幕系统
export * from './subtitle'

// 视频处理系统
export * from './ffmpeg'

// 工具函数集合
export const editorUtils = {
  // 时间格式化
  formatTime: (seconds: number, format: 'simple' | 'full' | 'precise' = 'simple'): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)

    switch (format) {
      case 'full':
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      case 'precise':
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
      case 'simple':
      default:
        return hours > 0 
          ? `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
          : `${minutes}:${secs.toString().padStart(2, '0')}`
    }
  },

  // 文件大小格式化
  formatFileSize: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`
  },

  // 生成唯一ID
  generateId: (prefix: string = 'item'): string => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  },

  // 颜色工具
  colorUtils: {
    // 十六进制转RGB
    hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null
    },

    // RGB转十六进制
    rgbToHex: (r: number, g: number, b: number): string => {
      return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
    },

    // 获取对比色
    getContrastColor: (hex: string): string => {
      const rgb = editorUtils.colorUtils.hexToRgb(hex)
      if (!rgb) return '#000000'
      
      const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000
      return brightness > 128 ? '#000000' : '#ffffff'
    }
  },

  // 媒体工具
  mediaUtils: {
    // 检测文件类型
    getFileType: (filename: string): 'video' | 'audio' | 'image' | 'subtitle' | 'unknown' => {
      const extension = filename.toLowerCase().split('.').pop()
      
      const videoExts = ['mp4', 'avi', 'mov', 'webm', 'mkv', 'flv', '3gp', 'm4v']
      const audioExts = ['mp3', 'wav', 'aac', 'ogg', 'flac', 'm4a', 'wma']
      const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
      const subtitleExts = ['srt', 'vtt', 'ass', 'ssa', 'sub']
      
      if (extension && videoExts.includes(extension)) return 'video'
      if (extension && audioExts.includes(extension)) return 'audio'
      if (extension && imageExts.includes(extension)) return 'image'
      if (extension && subtitleExts.includes(extension)) return 'subtitle'
      
      return 'unknown'
    },

    // 创建缩略图
    createThumbnail: (file: File, maxWidth: number = 320, maxHeight: number = 240): Promise<string> => {
      return new Promise((resolve, reject) => {
        if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
          reject(new Error('不支持的文件类型'))
          return
        }

        if (file.type.startsWith('image/')) {
          const reader = new FileReader()
          reader.onload = (e) => {
            const img = new Image()
            img.onload = () => {
              const canvas = document.createElement('canvas')
              const ctx = canvas.getContext('2d')
              
              const { width, height } = editorUtils.mediaUtils.calculateAspectRatio(
                img.width, img.height, maxWidth, maxHeight
              )
              
              canvas.width = width
              canvas.height = height
              
              ctx?.drawImage(img, 0, 0, width, height)
              resolve(canvas.toDataURL('image/jpeg', 0.8))
            }
            img.src = e.target?.result as string
          }
          reader.readAsDataURL(file)
        } else {
          // 视频缩略图需要更复杂的处理
          const video = document.createElement('video')
          video.preload = 'metadata'
          video.onloadedmetadata = () => {
            video.currentTime = Math.min(1, video.duration / 2)
          }
          video.onseeked = () => {
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')
            
            const { width, height } = editorUtils.mediaUtils.calculateAspectRatio(
              video.videoWidth, video.videoHeight, maxWidth, maxHeight
            )
            
            canvas.width = width
            canvas.height = height
            
            ctx?.drawImage(video, 0, 0, width, height)
            resolve(canvas.toDataURL('image/jpeg', 0.8))
          }
          video.src = URL.createObjectURL(file)
        }
      })
    },

    // 计算宽高比
    calculateAspectRatio: (
      originalWidth: number, 
      originalHeight: number, 
      maxWidth: number, 
      maxHeight: number
    ): { width: number; height: number } => {
      const aspectRatio = originalWidth / originalHeight
      
      let width = maxWidth
      let height = maxWidth / aspectRatio
      
      if (height > maxHeight) {
        height = maxHeight
        width = maxHeight * aspectRatio
      }
      
      return { width: Math.round(width), height: Math.round(height) }
    }
  },

  // 键盘快捷键
  keyboardUtils: {
    // 检查快捷键组合
    isShortcut: (event: KeyboardEvent, shortcut: string): boolean => {
      const keys = shortcut.toLowerCase().split('+')
      const modifiers = {
        ctrl: event.ctrlKey || event.metaKey,
        shift: event.shiftKey,
        alt: event.altKey
      }
      
      const hasCtrl = keys.includes('ctrl') ? modifiers.ctrl : !modifiers.ctrl
      const hasShift = keys.includes('shift') ? modifiers.shift : !modifiers.shift
      const hasAlt = keys.includes('alt') ? modifiers.alt : !modifiers.alt
      
      const mainKey = keys.find(key => !['ctrl', 'shift', 'alt'].includes(key))
      const hasMainKey = mainKey ? event.key.toLowerCase() === mainKey : true
      
      return hasCtrl && hasShift && hasAlt && hasMainKey
    },

    // 常用快捷键映射
    shortcuts: {
      'ctrl+z': '撤销',
      'ctrl+y': '重做',
      'ctrl+shift+z': '重做',
      'ctrl+c': '复制',
      'ctrl+v': '粘贴',
      'ctrl+x': '剪切',
      'ctrl+s': '保存',
      'space': '播放/暂停',
      'ctrl+shift+e': '导出',
      'delete': '删除',
      'backspace': '删除',
      'ctrl+a': '全选',
      'escape': '取消选择'
    }
  },

  // 性能工具
  performanceUtils: {
    // 防抖函数
    debounce: <T extends (...args: any[]) => any>(
      func: T,
      wait: number
    ): ((...args: Parameters<T>) => void) => {
      let timeout: NodeJS.Timeout
      return (...args: Parameters<T>) => {
        clearTimeout(timeout)
        timeout = setTimeout(() => func(...args), wait)
      }
    },

    // 节流函数
    throttle: <T extends (...args: any[]) => any>(
      func: T,
      limit: number
    ): ((...args: Parameters<T>) => void) => {
      let inThrottle: boolean
      return (...args: Parameters<T>) => {
        if (!inThrottle) {
          func(...args)
          inThrottle = true
          setTimeout(() => inThrottle = false, limit)
        }
      }
    },

    // 内存使用监控
    getMemoryUsage: (): { used: number; total: number } | null => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        return {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize
        }
      }
      return null
    }
  },

  // 验证工具
  validationUtils: {
    // 验证时间范围
    isValidTimeRange: (startTime: number, endTime: number): boolean => {
      return startTime >= 0 && endTime > startTime && endTime - startTime >= 0.1
    },

    // 验证分辨率
    isValidResolution: (width: number, height: number): boolean => {
      return width > 0 && height > 0 && width <= 7680 && height <= 4320
    },

    // 验证帧率
    isValidFrameRate: (fps: number): boolean => {
      return fps > 0 && fps <= 120
    },

    // 验证颜色值
    isValidColor: (color: string): boolean => {
      const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
      const rgbPattern = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/
      const rgbaPattern = /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/
      
      return hexPattern.test(color) || rgbPattern.test(color) || rgbaPattern.test(color)
    }
  }
}
