/* 无障碍访问样式 */

/* 高对比度模式 */
.high-contrast {
  --background: #000000;
  --foreground: #ffffff;
  --primary: #ffffff;
  --primary-foreground: #000000;
  --secondary: #333333;
  --secondary-foreground: #ffffff;
  --muted: #222222;
  --muted-foreground: #cccccc;
  --accent: #ffffff;
  --accent-foreground: #000000;
  --destructive: #ff0000;
  --destructive-foreground: #ffffff;
  --border: #ffffff;
  --input: #333333;
  --ring: #ffffff;
}

.high-contrast * {
  border-color: var(--border) !important;
  background-color: var(--background) !important;
  color: var(--foreground) !important;
}

.high-contrast button {
  background-color: var(--primary) !important;
  color: var(--primary-foreground) !important;
  border: 2px solid var(--border) !important;
}

.high-contrast button:hover {
  background-color: var(--secondary) !important;
  color: var(--secondary-foreground) !important;
}

.high-contrast input,
.high-contrast textarea,
.high-contrast select {
  background-color: var(--input) !important;
  color: var(--foreground) !important;
  border: 2px solid var(--border) !important;
}

/* 大字体模式 */
.large-text {
  font-size: 1.2em;
}

.large-text h1 { font-size: 2.4rem; }
.large-text h2 { font-size: 2rem; }
.large-text h3 { font-size: 1.6rem; }
.large-text h4 { font-size: 1.4rem; }
.large-text h5 { font-size: 1.2rem; }
.large-text h6 { font-size: 1.1rem; }

.large-text .text-xs { font-size: 0.875rem; }
.large-text .text-sm { font-size: 1rem; }
.large-text .text-base { font-size: 1.2rem; }
.large-text .text-lg { font-size: 1.4rem; }
.large-text .text-xl { font-size: 1.6rem; }

.large-text button,
.large-text input,
.large-text textarea,
.large-text select {
  font-size: 1.1em;
  padding: 0.75rem 1rem;
}

/* 减少动画模式 */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* 键盘导航增强 */
.keyboard-navigation *:focus {
  outline: 3px solid #0066cc !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3) !important;
}

.keyboard-navigation button:focus,
.keyboard-navigation input:focus,
.keyboard-navigation textarea:focus,
.keyboard-navigation select:focus,
.keyboard-navigation [tabindex]:focus {
  outline: 3px solid #0066cc !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3) !important;
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 9999;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .responsive-layout.mobile {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }

  .responsive-layout.mobile .timeline-container {
    overflow-x: auto;
    overflow-y: hidden;
  }

  .responsive-layout.mobile .control-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 1rem;
    z-index: 50;
  }

  .responsive-layout.mobile .preview-area {
    height: 40vh;
    min-height: 200px;
  }

  /* 移动端触摸优化 */
  .responsive-layout.mobile button {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem;
  }

  .responsive-layout.mobile input,
  .responsive-layout.mobile textarea,
  .responsive-layout.mobile select {
    min-height: 44px;
    font-size: 16px; /* 防止 iOS 缩放 */
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .responsive-layout.tablet {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }

  .responsive-layout.tablet .side-panel {
    width: 300px;
    transition: width 0.3s ease;
  }

  .responsive-layout.tablet .side-panel.collapsed {
    width: 60px;
  }

  .responsive-layout.tablet .timeline-container {
    height: 30vh;
    min-height: 200px;
  }
}

@media (min-width: 1025px) {
  .responsive-layout.desktop {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }

  .responsive-layout.desktop .side-panel {
    width: 320px;
    min-width: 280px;
    max-width: 400px;
    resize: horizontal;
    overflow: auto;
  }

  .responsive-layout.desktop .timeline-container {
    height: 35vh;
    min-height: 250px;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  abbr[title]:after {
    content: " (" attr(title) ")";
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  img {
    max-width: 100% !important;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #fafafa;
    --primary: #fafafa;
    --primary-foreground: #0a0a0a;
    --secondary: #262626;
    --secondary-foreground: #fafafa;
    --muted: #171717;
    --muted-foreground: #a3a3a3;
    --accent: #262626;
    --accent-foreground: #fafafa;
    --destructive: #dc2626;
    --destructive-foreground: #fafafa;
    --border: #262626;
    --input: #262626;
    --ring: #d4d4d8;
  }
}

/* 动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 对比度偏好设置 */
@media (prefers-contrast: high) {
  :root {
    --border: #ffffff;
    --input: #000000;
    --ring: #ffffff;
  }

  * {
    border-color: var(--border) !important;
  }

  button {
    border: 2px solid var(--border) !important;
  }
}

/* 透明度偏好设置 */
@media (prefers-reduced-transparency: reduce) {
  * {
    backdrop-filter: none !important;
    background-color: solid !important;
  }
}

/* 工具提示无障碍增强 */
[role="tooltip"] {
  position: absolute;
  z-index: 9999;
  background: #333;
  color: #fff;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  max-width: 200px;
  word-wrap: break-word;
}

/* 模态框无障碍增强 */
[role="dialog"] {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  background: white;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

[role="dialog"]:focus {
  outline: none;
}

/* 进度条无障碍增强 */
[role="progressbar"] {
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

[role="progressbar"]::after {
  content: attr(aria-valuenow) "%";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  font-weight: bold;
  color: #374151;
}

/* 标签页无障碍增强 */
[role="tablist"] {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

[role="tab"] {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

[role="tab"][aria-selected="true"] {
  border-bottom-color: #3b82f6;
  color: #3b82f6;
  font-weight: 600;
}

[role="tab"]:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

[role="tabpanel"] {
  padding: 1rem;
}

[role="tabpanel"]:focus {
  outline: none;
}
