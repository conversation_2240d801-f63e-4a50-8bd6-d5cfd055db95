// 音频组件统一导出

export { WaveformVisualization } from './WaveformVisualization'
export { AudioTrack } from './AudioTrack'
export { AudioEditor } from './AudioEditor'

// 类型导出
export type { WaveSurfer } from './WaveformVisualization'

// 音频处理工具函数
export const audioUtils = {
  // 格式化时间显示
  formatTime: (seconds: number, showMilliseconds = false): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)

    if (hours > 0) {
      return showMilliseconds 
        ? `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
        : `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    } else {
      return showMilliseconds
        ? `${minutes}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
        : `${minutes}:${secs.toString().padStart(2, '0')}`
    }
  },

  // 解析时间字符串
  parseTime: (timeString: string): number => {
    const parts = timeString.split(':')
    let seconds = 0

    if (parts.length === 3) {
      // HH:MM:SS 或 HH:MM:SS.mmm
      seconds += parseInt(parts[0]) * 3600
      seconds += parseInt(parts[1]) * 60
      
      const secParts = parts[2].split('.')
      seconds += parseInt(secParts[0])
      if (secParts[1]) {
        seconds += parseInt(secParts[1]) / 1000
      }
    } else if (parts.length === 2) {
      // MM:SS 或 MM:SS.mmm
      seconds += parseInt(parts[0]) * 60
      
      const secParts = parts[1].split('.')
      seconds += parseInt(secParts[0])
      if (secParts[1]) {
        seconds += parseInt(secParts[1]) / 1000
      }
    }

    return seconds
  },

  // 计算音频片段重叠
  calculateOverlap: (
    clip1: { startTime: number; endTime: number },
    clip2: { startTime: number; endTime: number }
  ): number => {
    const overlapStart = Math.max(clip1.startTime, clip2.startTime)
    const overlapEnd = Math.min(clip1.endTime, clip2.endTime)
    return Math.max(0, overlapEnd - overlapStart)
  },

  // 检查音频片段是否重叠
  hasOverlap: (
    clip1: { startTime: number; endTime: number },
    clip2: { startTime: number; endTime: number }
  ): boolean => {
    return audioUtils.calculateOverlap(clip1, clip2) > 0
  },

  // 生成简单的波形数据（用于预览）
  generateSimpleWaveform: (length: number, amplitude = 1): number[] => {
    const waveform = []
    for (let i = 0; i < length; i++) {
      // 生成随机波形数据
      const frequency = 0.1 + Math.random() * 0.3
      const noise = (Math.random() - 0.5) * 0.3
      const value = Math.sin(i * frequency) * amplitude + noise
      waveform.push(Math.max(-1, Math.min(1, value)))
    }
    return waveform
  },

  // 音量转换（线性到对数）
  linearToDb: (linear: number): number => {
    return linear > 0 ? 20 * Math.log10(linear) : -Infinity
  },

  // 音量转换（对数到线性）
  dbToLinear: (db: number): number => {
    return db === -Infinity ? 0 : Math.pow(10, db / 20)
  },

  // 计算RMS音量
  calculateRMS: (samples: number[]): number => {
    if (samples.length === 0) return 0
    
    const sumSquares = samples.reduce((sum, sample) => sum + sample * sample, 0)
    return Math.sqrt(sumSquares / samples.length)
  },

  // 应用淡入效果
  applyFadeIn: (samples: number[], fadeInDuration: number, sampleRate: number): number[] => {
    const fadeInSamples = Math.floor(fadeInDuration * sampleRate)
    const result = [...samples]
    
    for (let i = 0; i < Math.min(fadeInSamples, samples.length); i++) {
      const factor = i / fadeInSamples
      result[i] *= factor
    }
    
    return result
  },

  // 应用淡出效果
  applyFadeOut: (samples: number[], fadeOutDuration: number, sampleRate: number): number[] => {
    const fadeOutSamples = Math.floor(fadeOutDuration * sampleRate)
    const result = [...samples]
    const startIndex = Math.max(0, samples.length - fadeOutSamples)
    
    for (let i = startIndex; i < samples.length; i++) {
      const factor = (samples.length - i) / fadeOutSamples
      result[i] *= factor
    }
    
    return result
  },

  // 混合两个音频片段
  mixAudio: (samples1: number[], samples2: number[], mixRatio = 0.5): number[] => {
    const maxLength = Math.max(samples1.length, samples2.length)
    const result = new Array(maxLength).fill(0)
    
    for (let i = 0; i < maxLength; i++) {
      const sample1 = i < samples1.length ? samples1[i] : 0
      const sample2 = i < samples2.length ? samples2[i] : 0
      result[i] = sample1 * (1 - mixRatio) + sample2 * mixRatio
    }
    
    return result
  },

  // 检测音频格式
  detectAudioFormat: (filename: string): string => {
    const extension = filename.toLowerCase().split('.').pop()
    const formatMap: { [key: string]: string } = {
      'mp3': 'MP3',
      'wav': 'WAV',
      'aac': 'AAC',
      'ogg': 'OGG',
      'flac': 'FLAC',
      'm4a': 'M4A',
      'wma': 'WMA'
    }
    return formatMap[extension || ''] || 'Unknown'
  },

  // 估算音频文件大小
  estimateFileSize: (duration: number, sampleRate: number, bitDepth: number, channels: number): number => {
    // 计算未压缩大小（字节）
    return Math.floor((duration * sampleRate * bitDepth * channels) / 8)
  },

  // 格式化文件大小
  formatFileSize: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`
  }
}
