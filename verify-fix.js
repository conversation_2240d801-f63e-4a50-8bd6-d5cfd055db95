#!/usr/bin/env node

/**
 * Verification script to check if the React import duplication issue is fixed
 */

const fs = require('fs');
const path = require('path');

function checkReactImports(filePath) {
  console.log(`🔍 Checking ${filePath}...`);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    const reactImports = [];
    
    lines.forEach((line, index) => {
      if (line.includes('import') && line.includes('React')) {
        reactImports.push({
          line: index + 1,
          content: line.trim()
        });
      }
    });
    
    console.log(`📊 Found ${reactImports.length} React import(s):`);
    reactImports.forEach(imp => {
      console.log(`  Line ${imp.line}: ${imp.content}`);
    });
    
    if (reactImports.length > 1) {
      console.log('❌ Multiple React imports detected!');
      return false;
    } else if (reactImports.length === 1) {
      console.log('✅ Single React import found - OK');
      return true;
    } else {
      console.log('⚠️ No React imports found');
      return true;
    }
    
  } catch (error) {
    console.error(`❌ Error reading file: ${error.message}`);
    return false;
  }
}

function main() {
  console.log('🎯 React Import Duplication Fix Verification');
  console.log('===========================================');
  
  const filesToCheck = [
    'app/create/components/ImageGenerationStep.tsx',
    'app/create/components/VideoGenerationStep.tsx'
  ];
  
  let allGood = true;
  
  filesToCheck.forEach(file => {
    const fullPath = path.resolve(file);
    if (fs.existsSync(fullPath)) {
      const result = checkReactImports(fullPath);
      allGood = allGood && result;
      console.log('');
    } else {
      console.log(`⚠️ File not found: ${file}`);
      console.log('');
    }
  });
  
  if (allGood) {
    console.log('🎉 All files passed the React import check!');
    process.exit(0);
  } else {
    console.log('❌ Some files have React import issues');
    process.exit(1);
  }
}

main();
