// FFmpeg.wasm 视频处理工具类

import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'

export interface VideoProcessingOptions {
  width?: number
  height?: number
  fps?: number
  bitrate?: string
  format?: string
  quality?: 'low' | 'medium' | 'high' | 'ultra'
  startTime?: number
  duration?: number
}

export interface AudioProcessingOptions {
  sampleRate?: number
  channels?: number
  bitrate?: string
  format?: string
  volume?: number
  fadeIn?: number
  fadeOut?: number
}

export interface SubtitleOptions {
  fontFamily?: string
  fontSize?: number
  color?: string
  backgroundColor?: string
  position?: { x: number; y: number }
  style?: 'normal' | 'bold' | 'italic'
}

export class FFmpegProcessor {
  private ffmpeg: FFmpeg
  private isLoaded: boolean = false
  private loadingPromise: Promise<void> | null = null

  constructor() {
    this.ffmpeg = new FFmpeg()
  }

  // 初始化 FFmpeg
  async initialize(): Promise<void> {
    if (this.isLoaded) return
    if (this.loadingPromise) return this.loadingPromise

    this.loadingPromise = this.loadFFmpeg()
    await this.loadingPromise
    this.isLoaded = true
  }

  private async loadFFmpeg(): Promise<void> {
    const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd'
    
    this.ffmpeg.on('log', ({ message }) => {
      console.log('[FFmpeg]', message)
    })

    this.ffmpeg.on('progress', ({ progress, time }) => {
      console.log(`[FFmpeg] Progress: ${Math.round(progress * 100)}% (${time}ms)`)
    })

    await this.ffmpeg.load({
      coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
      wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
    })
  }

  // 视频格式转换
  async convertVideo(
    inputFile: File | Uint8Array,
    outputFormat: string,
    options: VideoProcessingOptions = {}
  ): Promise<Uint8Array> {
    await this.initialize()

    const inputName = 'input.mp4'
    const outputName = `output.${outputFormat}`

    // 写入输入文件
    if (inputFile instanceof File) {
      await this.ffmpeg.writeFile(inputName, await fetchFile(inputFile))
    } else {
      await this.ffmpeg.writeFile(inputName, inputFile)
    }

    // 构建 FFmpeg 命令
    const args = ['-i', inputName]

    // 视频编码选项
    if (options.width && options.height) {
      args.push('-s', `${options.width}x${options.height}`)
    }

    if (options.fps) {
      args.push('-r', options.fps.toString())
    }

    if (options.bitrate) {
      args.push('-b:v', options.bitrate)
    }

    // 质量设置
    if (options.quality) {
      const qualityMap = {
        low: ['-crf', '28'],
        medium: ['-crf', '23'],
        high: ['-crf', '18'],
        ultra: ['-crf', '15']
      }
      args.push(...qualityMap[options.quality])
    }

    // 时间裁剪
    if (options.startTime !== undefined) {
      args.push('-ss', options.startTime.toString())
    }

    if (options.duration !== undefined) {
      args.push('-t', options.duration.toString())
    }

    args.push(outputName)

    // 执行转换
    await this.ffmpeg.exec(args)

    // 读取输出文件
    const data = await this.ffmpeg.readFile(outputName)
    
    // 清理文件
    await this.ffmpeg.deleteFile(inputName)
    await this.ffmpeg.deleteFile(outputName)

    return data as Uint8Array
  }

  // 视频剪切
  async trimVideo(
    inputFile: File | Uint8Array,
    startTime: number,
    duration: number,
    outputFormat: string = 'mp4'
  ): Promise<Uint8Array> {
    return this.convertVideo(inputFile, outputFormat, {
      startTime,
      duration
    })
  }

  // 视频合并
  async mergeVideos(
    videoFiles: (File | Uint8Array)[],
    outputFormat: string = 'mp4'
  ): Promise<Uint8Array> {
    await this.initialize()

    const inputNames: string[] = []
    const outputName = `merged.${outputFormat}`

    // 写入所有输入文件
    for (let i = 0; i < videoFiles.length; i++) {
      const inputName = `input${i}.mp4`
      inputNames.push(inputName)

      if (videoFiles[i] instanceof File) {
        await this.ffmpeg.writeFile(inputName, await fetchFile(videoFiles[i] as File))
      } else {
        await this.ffmpeg.writeFile(inputName, videoFiles[i] as Uint8Array)
      }
    }

    // 创建合并列表文件
    const concatList = inputNames.map(name => `file '${name}'`).join('\n')
    await this.ffmpeg.writeFile('concat.txt', new TextEncoder().encode(concatList))

    // 执行合并
    await this.ffmpeg.exec([
      '-f', 'concat',
      '-safe', '0',
      '-i', 'concat.txt',
      '-c', 'copy',
      outputName
    ])

    // 读取输出文件
    const data = await this.ffmpeg.readFile(outputName)

    // 清理文件
    for (const inputName of inputNames) {
      await this.ffmpeg.deleteFile(inputName)
    }
    await this.ffmpeg.deleteFile('concat.txt')
    await this.ffmpeg.deleteFile(outputName)

    return data as Uint8Array
  }

  // 添加字幕到视频
  async addSubtitlesToVideo(
    videoFile: File | Uint8Array,
    subtitleFile: File | string,
    options: SubtitleOptions = {},
    outputFormat: string = 'mp4'
  ): Promise<Uint8Array> {
    await this.initialize()

    const videoName = 'input.mp4'
    const subtitleName = 'subtitles.srt'
    const outputName = `output.${outputFormat}`

    // 写入视频文件
    if (videoFile instanceof File) {
      await this.ffmpeg.writeFile(videoName, await fetchFile(videoFile))
    } else {
      await this.ffmpeg.writeFile(videoName, videoFile)
    }

    // 写入字幕文件
    if (subtitleFile instanceof File) {
      await this.ffmpeg.writeFile(subtitleName, await fetchFile(subtitleFile))
    } else {
      await this.ffmpeg.writeFile(subtitleName, new TextEncoder().encode(subtitleFile))
    }

    // 构建字幕滤镜
    let subtitleFilter = `subtitles=${subtitleName}`
    
    if (options.fontFamily) {
      subtitleFilter += `:force_style='FontName=${options.fontFamily}'`
    }
    
    if (options.fontSize) {
      subtitleFilter += `:force_style='FontSize=${options.fontSize}'`
    }
    
    if (options.color) {
      subtitleFilter += `:force_style='PrimaryColour=${this.colorToFFmpegFormat(options.color)}'`
    }

    // 执行添加字幕
    await this.ffmpeg.exec([
      '-i', videoName,
      '-vf', subtitleFilter,
      '-c:a', 'copy',
      outputName
    ])

    // 读取输出文件
    const data = await this.ffmpeg.readFile(outputName)

    // 清理文件
    await this.ffmpeg.deleteFile(videoName)
    await this.ffmpeg.deleteFile(subtitleName)
    await this.ffmpeg.deleteFile(outputName)

    return data as Uint8Array
  }

  // 音频处理
  async processAudio(
    audioFile: File | Uint8Array,
    options: AudioProcessingOptions = {},
    outputFormat: string = 'mp3'
  ): Promise<Uint8Array> {
    await this.initialize()

    const inputName = 'input.mp3'
    const outputName = `output.${outputFormat}`

    // 写入输入文件
    if (audioFile instanceof File) {
      await this.ffmpeg.writeFile(inputName, await fetchFile(audioFile))
    } else {
      await this.ffmpeg.writeFile(inputName, audioFile)
    }

    // 构建 FFmpeg 命令
    const args = ['-i', inputName]

    // 音频编码选项
    if (options.sampleRate) {
      args.push('-ar', options.sampleRate.toString())
    }

    if (options.channels) {
      args.push('-ac', options.channels.toString())
    }

    if (options.bitrate) {
      args.push('-b:a', options.bitrate)
    }

    // 音量调整
    if (options.volume !== undefined && options.volume !== 1) {
      args.push('-af', `volume=${options.volume}`)
    }

    // 淡入淡出效果
    if (options.fadeIn || options.fadeOut) {
      const filters = []
      if (options.fadeIn) {
        filters.push(`afade=t=in:ss=0:d=${options.fadeIn}`)
      }
      if (options.fadeOut) {
        // 需要知道音频总长度来设置淡出
        filters.push(`afade=t=out:st=${options.fadeOut}:d=1`)
      }
      if (filters.length > 0) {
        args.push('-af', filters.join(','))
      }
    }

    args.push(outputName)

    // 执行处理
    await this.ffmpeg.exec(args)

    // 读取输出文件
    const data = await this.ffmpeg.readFile(outputName)

    // 清理文件
    await this.ffmpeg.deleteFile(inputName)
    await this.ffmpeg.deleteFile(outputName)

    return data as Uint8Array
  }

  // 提取视频缩略图
  async extractThumbnail(
    videoFile: File | Uint8Array,
    timePosition: number = 1,
    width: number = 320,
    height: number = 240
  ): Promise<Uint8Array> {
    await this.initialize()

    const inputName = 'input.mp4'
    const outputName = 'thumbnail.jpg'

    // 写入视频文件
    if (videoFile instanceof File) {
      await this.ffmpeg.writeFile(inputName, await fetchFile(videoFile))
    } else {
      await this.ffmpeg.writeFile(inputName, videoFile)
    }

    // 提取缩略图
    await this.ffmpeg.exec([
      '-i', inputName,
      '-ss', timePosition.toString(),
      '-vframes', '1',
      '-s', `${width}x${height}`,
      outputName
    ])

    // 读取输出文件
    const data = await this.ffmpeg.readFile(outputName)

    // 清理文件
    await this.ffmpeg.deleteFile(inputName)
    await this.ffmpeg.deleteFile(outputName)

    return data as Uint8Array
  }

  // 获取视频信息
  async getVideoInfo(videoFile: File | Uint8Array): Promise<{
    duration: number
    width: number
    height: number
    fps: number
    bitrate: number
  }> {
    await this.initialize()

    const inputName = 'input.mp4'

    // 写入视频文件
    if (videoFile instanceof File) {
      await this.ffmpeg.writeFile(inputName, await fetchFile(videoFile))
    } else {
      await this.ffmpeg.writeFile(inputName, videoFile)
    }

    // 获取视频信息
    await this.ffmpeg.exec(['-i', inputName, '-f', 'null', '-'])

    // 清理文件
    await this.ffmpeg.deleteFile(inputName)

    // 注意：实际实现中需要解析 FFmpeg 的输出来获取视频信息
    // 这里返回默认值作为示例
    return {
      duration: 10,
      width: 1920,
      height: 1080,
      fps: 30,
      bitrate: 5000
    }
  }

  // 颜色格式转换（CSS 颜色到 FFmpeg 格式）
  private colorToFFmpegFormat(color: string): string {
    // 简化实现，实际需要更完整的颜色转换
    if (color.startsWith('#')) {
      return `&H${color.substring(1).toUpperCase()}&`
    }
    return color
  }

  // 清理资源
  async cleanup(): Promise<void> {
    if (this.isLoaded) {
      // FFmpeg.wasm 没有显式的清理方法，但可以重新创建实例
      this.ffmpeg = new FFmpeg()
      this.isLoaded = false
      this.loadingPromise = null
    }
  }

  // 检查是否已加载
  isReady(): boolean {
    return this.isLoaded
  }
}

// 单例实例
export const ffmpegProcessor = new FFmpegProcessor()
