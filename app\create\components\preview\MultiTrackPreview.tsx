"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import {
  Play,
  Pause,
  Square,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  Maximize,
  Settings,
  RefreshCw
} from "lucide-react"
import { useVideoEditor } from '../../stores'
import { SubtitleRenderer } from '../subtitle'

interface MultiTrackPreviewProps {
  width?: number
  height?: number
  onTimeUpdate?: (time: number) => void
  onPlayStateChange?: (isPlaying: boolean) => void
  className?: string
}

export const MultiTrackPreview: React.FC<MultiTrackPreviewProps> = ({
  width = 1920,
  height = 1080,
  onTimeUpdate,
  onPlayStateChange,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const videoRefs = useRef<{ [key: string]: HTMLVideoElement }>({})
  const audioRefs = useRef<{ [key: string]: HTMLAudioElement }>({})
  const animationFrameRef = useRef<number>()
  const startTimeRef = useRef<number>(0)
  const pausedTimeRef = useRef<number>(0)

  const {
    videoClips,
    audioClips,
    subtitleClips,
    timeline,
    setCurrentTime,
    setPlaying
  } = useVideoEditor()

  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTimeState] = useState(0)
  const [volume, setVolume] = useState([80])
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [loadingClips, setLoadingClips] = useState<Set<string>>(new Set())

  // 获取当前时间应该显示的片段
  const getActiveClips = useCallback((time: number) => {
    const activeVideoClips = videoClips.filter(clip => 
      time >= clip.startTime && time <= clip.endTime
    )
    const activeAudioClips = audioClips.filter(clip => 
      time >= clip.startTime && time <= clip.endTime
    )
    const activeSubtitleClips = subtitleClips.filter(clip => 
      time >= clip.startTime && time <= clip.endTime
    )

    return { activeVideoClips, activeAudioClips, activeSubtitleClips }
  }, [videoClips, audioClips, subtitleClips])

  // 预加载媒体元素
  const preloadMedia = useCallback(async () => {
    const newLoadingClips = new Set<string>()

    // 预加载视频
    for (const clip of videoClips) {
      if (clip.videoUrl && !videoRefs.current[clip.id]) {
        newLoadingClips.add(clip.id)
        const video = document.createElement('video')
        video.src = clip.videoUrl
        video.preload = 'metadata'
        video.muted = true // 视频静音，音频由音频轨道处理
        video.crossOrigin = 'anonymous'
        
        video.addEventListener('loadedmetadata', () => {
          newLoadingClips.delete(clip.id)
          setLoadingClips(prev => {
            const next = new Set(prev)
            next.delete(clip.id)
            return next
          })
        })

        video.addEventListener('error', () => {
          console.error(`视频加载失败: ${clip.videoUrl}`)
          newLoadingClips.delete(clip.id)
          setLoadingClips(prev => {
            const next = new Set(prev)
            next.delete(clip.id)
            return next
          })
        })

        videoRefs.current[clip.id] = video
      }
    }

    // 预加载音频
    for (const clip of audioClips) {
      if (clip.audioUrl && !audioRefs.current[clip.id]) {
        newLoadingClips.add(clip.id)
        const audio = document.createElement('audio')
        audio.src = clip.audioUrl
        audio.preload = 'metadata'
        audio.crossOrigin = 'anonymous'
        
        audio.addEventListener('loadedmetadata', () => {
          newLoadingClips.delete(clip.id)
          setLoadingClips(prev => {
            const next = new Set(prev)
            next.delete(clip.id)
            return next
          })
        })

        audio.addEventListener('error', () => {
          console.error(`音频加载失败: ${clip.audioUrl}`)
          newLoadingClips.delete(clip.id)
          setLoadingClips(prev => {
            const next = new Set(prev)
            next.delete(clip.id)
            return next
          })
        })

        audioRefs.current[clip.id] = audio
      }
    }

    setLoadingClips(prev => new Set([...prev, ...newLoadingClips]))
  }, [videoClips, audioClips])

  // 渲染当前帧
  const renderFrame = useCallback((time: number) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 清空画布
    ctx.fillStyle = '#000000'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    const { activeVideoClips } = getActiveClips(time)

    // 按轨道顺序渲染视频片段
    const sortedVideoClips = activeVideoClips.sort((a, b) => a.track - b.track)

    for (const clip of sortedVideoClips) {
      const video = videoRefs.current[clip.id]
      if (!video || video.readyState < 2) continue

      // 计算片段内的相对时间
      const relativeTime = time - clip.startTime
      const videoTime = Math.max(0, Math.min(relativeTime, clip.duration))
      
      // 设置视频时间
      if (Math.abs(video.currentTime - videoTime) > 0.1) {
        video.currentTime = videoTime
      }

      try {
        // 计算渲染位置和大小
        const aspectRatio = video.videoWidth / video.videoHeight
        const canvasAspectRatio = canvas.width / canvas.height

        let renderWidth = canvas.width
        let renderHeight = canvas.height
        let renderX = 0
        let renderY = 0

        if (aspectRatio > canvasAspectRatio) {
          // 视频更宽，以高度为准
          renderHeight = canvas.height
          renderWidth = renderHeight * aspectRatio
          renderX = (canvas.width - renderWidth) / 2
        } else {
          // 视频更高，以宽度为准
          renderWidth = canvas.width
          renderHeight = renderWidth / aspectRatio
          renderY = (canvas.height - renderHeight) / 2
        }

        // 绘制视频帧
        ctx.drawImage(video, renderX, renderY, renderWidth, renderHeight)
      } catch (error) {
        console.error('视频渲染错误:', error)
      }
    }
  }, [getActiveClips])

  // 同步音频播放
  const syncAudio = useCallback((time: number, playing: boolean) => {
    const { activeAudioClips } = getActiveClips(time)
    const volumeLevel = isMuted ? 0 : volume[0] / 100

    // 停止所有音频
    Object.values(audioRefs.current).forEach(audio => {
      if (!audio.paused) {
        audio.pause()
      }
    })

    if (!playing) return

    // 播放当前时间的音频片段
    for (const clip of activeAudioClips) {
      const audio = audioRefs.current[clip.id]
      if (!audio || audio.readyState < 2) continue

      const relativeTime = time - clip.startTime
      const audioTime = Math.max(0, Math.min(relativeTime, clip.duration))
      
      audio.currentTime = audioTime
      audio.volume = volumeLevel * (clip.volume / 100)
      
      audio.play().catch(error => {
        console.error('音频播放错误:', error)
      })
    }
  }, [getActiveClips, volume, isMuted])

  // 动画循环
  const animate = useCallback(() => {
    if (!isPlaying) return

    const now = performance.now()
    const elapsed = (now - startTimeRef.current) / 1000
    const newTime = pausedTimeRef.current + elapsed

    if (newTime >= timeline.duration) {
      // 播放结束
      setIsPlaying(false)
      setPlaying(false)
      setCurrentTime(timeline.duration)
      setCurrentTimeState(timeline.duration)
      onTimeUpdate?.(timeline.duration)
      onPlayStateChange?.(false)
      return
    }

    setCurrentTime(newTime)
    setCurrentTimeState(newTime)
    onTimeUpdate?.(newTime)

    renderFrame(newTime)
    syncAudio(newTime, true)

    animationFrameRef.current = requestAnimationFrame(animate)
  }, [isPlaying, timeline.duration, renderFrame, syncAudio, setCurrentTime, setPlaying, onTimeUpdate, onPlayStateChange])

  // 播放控制
  const handlePlay = useCallback(() => {
    if (loadingClips.size > 0) {
      console.log('等待媒体加载完成...')
      return
    }

    startTimeRef.current = performance.now()
    pausedTimeRef.current = currentTime
    setIsPlaying(true)
    setPlaying(true)
    onPlayStateChange?.(true)
    
    animationFrameRef.current = requestAnimationFrame(animate)
  }, [currentTime, animate, setPlaying, onPlayStateChange, loadingClips.size])

  const handlePause = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
    }
    
    pausedTimeRef.current = currentTime
    setIsPlaying(false)
    setPlaying(false)
    onPlayStateChange?.(false)
    
    // 暂停所有音频
    Object.values(audioRefs.current).forEach(audio => {
      if (!audio.paused) {
        audio.pause()
      }
    })
  }, [currentTime, setPlaying, onPlayStateChange])

  const handleStop = useCallback(() => {
    handlePause()
    const newTime = 0
    setCurrentTime(newTime)
    setCurrentTimeState(newTime)
    pausedTimeRef.current = newTime
    onTimeUpdate?.(newTime)
    renderFrame(newTime)
  }, [handlePause, setCurrentTime, onTimeUpdate, renderFrame])

  const handleSeek = useCallback((time: number) => {
    const clampedTime = Math.max(0, Math.min(time, timeline.duration))
    setCurrentTime(clampedTime)
    setCurrentTimeState(clampedTime)
    pausedTimeRef.current = clampedTime
    onTimeUpdate?.(clampedTime)
    
    renderFrame(clampedTime)
    if (isPlaying) {
      syncAudio(clampedTime, true)
      startTimeRef.current = performance.now()
    }
  }, [timeline.duration, setCurrentTime, onTimeUpdate, renderFrame, isPlaying, syncAudio])

  // 全屏控制
  const handleFullscreen = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    if (!isFullscreen) {
      if (canvas.requestFullscreen) {
        canvas.requestFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    }
  }, [isFullscreen])

  // 初始化
  useEffect(() => {
    preloadMedia()
  }, [preloadMedia])

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  // 清理资源
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
      
      // 清理媒体元素
      Object.values(videoRefs.current).forEach(video => {
        video.src = ''
        video.load()
      })
      Object.values(audioRefs.current).forEach(audio => {
        audio.src = ''
        audio.load()
      })
    }
  }, [])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <Card className={`multi-track-preview ${className}`}>
      <CardContent className="p-0 relative">
        {/* 预览画布 */}
        <div className="relative bg-black rounded-t-lg overflow-hidden">
          <canvas
            ref={canvasRef}
            width={width}
            height={height}
            className="w-full h-auto max-h-[60vh] object-contain"
          />
          
          {/* 字幕渲染层 */}
          <div className="absolute inset-0">
            <SubtitleRenderer
              subtitles={subtitleClips}
              currentTime={currentTime}
              videoWidth={width}
              videoHeight={height}
            />
          </div>

          {/* 加载指示器 */}
          {loadingClips.size > 0 && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <div className="text-white text-center">
                <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2" />
                <p>加载媒体文件中... ({loadingClips.size} 个)</p>
              </div>
            </div>
          )}

          {/* 全屏按钮 */}
          <Button
            variant="secondary"
            size="sm"
            className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white border-white/20"
            onClick={handleFullscreen}
          >
            <Maximize className="w-4 h-4" />
          </Button>
        </div>

        {/* 控制面板 */}
        <div className="p-4 bg-gray-50 rounded-b-lg">
          <div className="flex items-center justify-between mb-4">
            {/* 播放控制 */}
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSeek(Math.max(0, currentTime - 10))}
                className="w-8 h-8 p-0"
              >
                <SkipBack className="w-4 h-4" />
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={isPlaying ? handlePause : handlePlay}
                disabled={loadingClips.size > 0}
                className="w-10 h-10 p-0"
              >
                {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleStop}
                className="w-8 h-8 p-0"
              >
                <Square className="w-4 h-4" />
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSeek(Math.min(timeline.duration, currentTime + 10))}
                className="w-8 h-8 p-0"
              >
                <SkipForward className="w-4 h-4" />
              </Button>
            </div>

            {/* 时间显示 */}
            <div className="flex items-center space-x-2 text-sm">
              <span className="font-mono">{formatTime(currentTime)}</span>
              <span className="text-gray-400">/</span>
              <span className="font-mono">{formatTime(timeline.duration)}</span>
            </div>

            {/* 音量控制 */}
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsMuted(!isMuted)}
                className="w-8 h-8 p-0"
              >
                {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
              </Button>

              <div className="w-20">
                <Slider
                  value={volume}
                  onValueChange={setVolume}
                  max={100}
                  step={1}
                  className="w-full"
                  disabled={isMuted}
                />
              </div>

              <span className="text-xs text-gray-500 min-w-[30px]">
                {isMuted ? '0%' : `${volume[0]}%`}
              </span>
            </div>
          </div>

          {/* 进度条 */}
          <div className="space-y-2">
            <Slider
              value={[currentTime]}
              onValueChange={([value]) => handleSeek(value)}
              max={timeline.duration}
              step={0.1}
              className="w-full"
            />
            
            {/* 状态信息 */}
            <div className="flex items-center justify-between text-xs text-gray-500">
              <div className="flex items-center space-x-4">
                <Badge variant="secondary" className="text-xs">
                  {videoClips.length} 视频
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {audioClips.length} 音频
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {subtitleClips.length} 字幕
                </Badge>
              </div>
              
              <div>
                分辨率: {width}x{height} | 帧率: 30fps
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
