#!/usr/bin/env node

/**
 * Test script for Image SWR cache implementation
 * Tests the /api/image-records/cached endpoint with both GET and POST methods
 */

const BASE_URL = 'http://localhost:3001'

// Test data
const testData = {
  projectId: '4db5bf32-fdda-4bf0-ab45-f8e8315ef302',
  userId: '559f045d-5477-443c-92ee-93d03fff3b3c',
  shotNumbers: [1, 2, 3, 4, 5]
}

async function testPOSTMethod() {
  console.log('\n🧪 Testing POST method...')
  
  try {
    const response = await fetch(`${BASE_URL}/api/image-records/cached`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    })
    
    const data = await response.json()
    
    console.log(`📊 POST Response Status: ${response.status}`)
    console.log(`📊 POST Response:`, JSON.stringify(data, null, 2))
    
    if (response.ok) {
      console.log('✅ POST method working correctly')
      return data
    } else {
      console.log('❌ POST method failed')
      return null
    }
  } catch (error) {
    console.error('❌ POST method error:', error.message)
    return null
  }
}

async function testGETMethod() {
  console.log('\n🧪 Testing GET method...')
  
  try {
    const queryParams = new URLSearchParams({
      projectId: testData.projectId,
      userId: testData.userId,
      shotNumbers: testData.shotNumbers.join(',')
    })
    
    const response = await fetch(`${BASE_URL}/api/image-records/cached?${queryParams}`)
    const data = await response.json()
    
    console.log(`📊 GET Response Status: ${response.status}`)
    console.log(`📊 GET Response:`, JSON.stringify(data, null, 2))
    
    if (response.ok) {
      console.log('✅ GET method working correctly')
      return data
    } else {
      console.log('❌ GET method failed')
      return null
    }
  } catch (error) {
    console.error('❌ GET method error:', error.message)
    return null
  }
}

async function testCachePerformance() {
  console.log('\n🚀 Testing cache performance...')
  
  const iterations = 5
  const times = []
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now()
    
    const response = await fetch(`${BASE_URL}/api/image-records/cached`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    })
    
    const endTime = Date.now()
    const duration = endTime - startTime
    times.push(duration)
    
    console.log(`📊 Request ${i + 1}: ${duration}ms`)
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  const avgTime = times.reduce((a, b) => a + b, 0) / times.length
  console.log(`📊 Average response time: ${avgTime.toFixed(2)}ms`)
  console.log(`📊 Min response time: ${Math.min(...times)}ms`)
  console.log(`📊 Max response time: ${Math.max(...times)}ms`)
}

async function testDataStructure(data) {
  console.log('\n🔍 Testing data structure...')
  
  if (!data) {
    console.log('❌ No data to test')
    return
  }
  
  // Check required fields
  const requiredFields = ['success', 'tasks', 'totalShots', 'totalCompletedTasks', 'totalCompletedImages', 'cached']
  const missingFields = requiredFields.filter(field => !(field in data))
  
  if (missingFields.length > 0) {
    console.log(`❌ Missing required fields: ${missingFields.join(', ')}`)
  } else {
    console.log('✅ All required fields present')
  }
  
  // Check tasks structure
  if (data.tasks && typeof data.tasks === 'object') {
    const shotNumbers = Object.keys(data.tasks).map(Number)
    console.log(`📊 Found tasks for shot numbers: ${shotNumbers.join(', ')}`)
    
    // Check each shot's task data
    shotNumbers.forEach(shotNumber => {
      const shotTasks = data.tasks[shotNumber]
      if (Array.isArray(shotTasks)) {
        console.log(`📊 Shot ${shotNumber}: ${shotTasks.length} tasks`)
        
        shotTasks.forEach((task, index) => {
          if (task.status === 'completed' && task.generated_images) {
            console.log(`  ✅ Task ${index + 1}: ${task.status} with ${task.generated_images.length} images`)
          } else {
            console.log(`  📝 Task ${index + 1}: ${task.status}`)
          }
        })
      }
    })
  }
  
  // Summary
  console.log(`📊 Summary:`)
  console.log(`  - Total shots: ${data.totalShots}`)
  console.log(`  - Completed tasks: ${data.totalCompletedTasks}`)
  console.log(`  - Total images: ${data.totalCompletedImages}`)
  console.log(`  - Cached: ${data.cached}`)
  console.log(`  - Query time: ${data.queriedAt}`)
}

async function main() {
  console.log('🎯 Image SWR Cache Implementation Test')
  console.log('=====================================')
  console.log(`🔗 Base URL: ${BASE_URL}`)
  console.log(`📋 Test Data:`, JSON.stringify(testData, null, 2))
  
  // Test POST method
  const postResult = await testPOSTMethod()
  
  // Test GET method
  const getResult = await testGETMethod()
  
  // Compare results
  if (postResult && getResult) {
    console.log('\n🔍 Comparing POST vs GET results...')
    
    const postTasks = Object.keys(postResult.tasks || {}).length
    const getTasks = Object.keys(getResult.tasks || {}).length
    
    if (postTasks === getTasks) {
      console.log('✅ Both methods return consistent results')
    } else {
      console.log('⚠️ Results differ between methods')
      console.log(`POST tasks: ${postTasks}, GET tasks: ${getTasks}`)
    }
  }
  
  // Test data structure
  await testDataStructure(postResult || getResult)
  
  // Test cache performance
  await testCachePerformance()
  
  console.log('\n🎉 Test completed!')
}

// Run the test
main().catch(console.error)
