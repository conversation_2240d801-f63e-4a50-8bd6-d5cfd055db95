// 视频编辑器相关类型定义

export interface VideoClip {
  id: string
  name: string
  duration: number
  thumbnail: string
  videoUrl?: string
  startTime: number
  endTime: number
  track: number
  sceneData?: any
  // 缓存相关字段
  hasVideoCache?: boolean
  hasImageCache?: boolean
  videoTaskStatus?: string
  imageTaskStatus?: string
  cacheSource?: 'video' | 'image' | 'none'
  lastCacheCheck?: number
  // 编辑属性
  volume?: number
  opacity?: number
  filters?: VideoFilter[]
  transform?: Transform
}

export interface AudioClip {
  id: string
  name: string
  duration: number
  startTime: number
  endTime: number
  volume: number
  track: number
  audioUrl?: string
  waveformData?: number[]
  filters?: AudioFilter[]
}

export interface SubtitleClip {
  id: string
  text: string
  startTime: number
  endTime: number
  track: number
  style: SubtitleStyle
  position: Position
}

export interface VideoFilter {
  id: string
  type: 'brightness' | 'contrast' | 'saturation' | 'blur' | 'sepia' | 'grayscale'
  value: number
  enabled: boolean
}

export interface AudioFilter {
  id: string
  type: 'volume' | 'fade' | 'echo' | 'reverb' | 'equalizer'
  value: number
  enabled: boolean
}

export interface SubtitleStyle {
  fontFamily: string
  fontSize: number
  color: string
  backgroundColor?: string
  bold: boolean
  italic: boolean
  underline: boolean
  shadow: boolean
}

export interface Position {
  x: number
  y: number
}

export interface Transform {
  x: number
  y: number
  scaleX: number
  scaleY: number
  rotation: number
}

export interface TimelineState {
  currentTime: number
  duration: number
  isPlaying: boolean
  playbackRate: number
  zoomLevel: number
  scrollPosition: number
  selectedClips: string[]
  clipboardClips: (VideoClip | AudioClip | SubtitleClip)[]
}

export interface ViewportState {
  width: number
  height: number
  pixelsPerSecond: number
  visibleTimeRange: {
    start: number
    end: number
  }
}

export interface ProjectSettings {
  name: string
  description: string
  resolution: {
    width: number
    height: number
  }
  frameRate: number
  outputFormat: string
  quality: string
}

export interface HistoryState {
  past: EditorState[]
  present: EditorState
  future: EditorState[]
  canUndo: boolean
  canRedo: boolean
}

export interface EditorState {
  videoClips: VideoClip[]
  audioClips: AudioClip[]
  subtitleClips: SubtitleClip[]
  timeline: TimelineState
  viewport: ViewportState
  project: ProjectSettings
  timestamp: number
}

export interface DragState {
  isDragging: boolean
  dragType: 'video' | 'audio' | 'subtitle' | null
  draggedItems: string[]
  dropTarget: {
    track: number
    time: number
  } | null
  previewPosition: {
    track: number
    time: number
  } | null
}

export interface UIState {
  selectedTool: 'select' | 'cut' | 'zoom' | 'hand'
  showWaveforms: boolean
  showThumbnails: boolean
  showGrid: boolean
  snapToGrid: boolean
  snapThreshold: number
  isSmartMode: boolean
  sidebarWidth: number
  timelineHeight: number
  previewPanelHeight: number
}

export interface CacheState {
  videoTaskData: any
  imageTaskData: any
  isVideoLoading: boolean
  isImageLoading: boolean
  videoError: any
  imageError: any
  lastRefresh: number
}

// 动作类型
export type EditorAction = 
  | { type: 'ADD_VIDEO_CLIP'; payload: VideoClip }
  | { type: 'REMOVE_VIDEO_CLIP'; payload: string }
  | { type: 'UPDATE_VIDEO_CLIP'; payload: { id: string; updates: Partial<VideoClip> } }
  | { type: 'ADD_AUDIO_CLIP'; payload: AudioClip }
  | { type: 'REMOVE_AUDIO_CLIP'; payload: string }
  | { type: 'UPDATE_AUDIO_CLIP'; payload: { id: string; updates: Partial<AudioClip> } }
  | { type: 'ADD_SUBTITLE_CLIP'; payload: SubtitleClip }
  | { type: 'REMOVE_SUBTITLE_CLIP'; payload: string }
  | { type: 'UPDATE_SUBTITLE_CLIP'; payload: { id: string; updates: Partial<SubtitleClip> } }
  | { type: 'SET_CURRENT_TIME'; payload: number }
  | { type: 'SET_PLAYING'; payload: boolean }
  | { type: 'SET_ZOOM_LEVEL'; payload: number }
  | { type: 'SET_SELECTED_CLIPS'; payload: string[] }
  | { type: 'UNDO' }
  | { type: 'REDO' }
  | { type: 'SAVE_STATE' }

// 工具函数类型
export interface TimelineUtils {
  timeToPixels: (time: number) => number
  pixelsToTime: (pixels: number) => number
  snapToGrid: (time: number) => number
  getClipAtPosition: (track: number, time: number) => VideoClip | AudioClip | SubtitleClip | null
  getOverlappingClips: (clip: VideoClip | AudioClip | SubtitleClip) => (VideoClip | AudioClip | SubtitleClip)[]
  calculateTotalDuration: () => number
}

export interface SelectionUtils {
  selectClip: (id: string, addToSelection?: boolean) => void
  selectClipsInRange: (startTime: number, endTime: number, track?: number) => void
  clearSelection: () => void
  deleteSelectedClips: () => void
  copySelectedClips: () => void
  pasteClips: (time: number, track?: number) => void
}

export interface PlaybackUtils {
  play: () => void
  pause: () => void
  stop: () => void
  seekTo: (time: number) => void
  setPlaybackRate: (rate: number) => void
  skipToNextClip: () => void
  skipToPreviousClip: () => void
}
