"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { Rect, Group, Line } from 'react-konva'
import WaveSurfer from 'wavesurfer.js'
import { AudioClip } from '../../stores'

interface AudioTrackProps {
  clip: AudioClip
  x: number
  y: number
  width: number
  height: number
  isSelected: boolean
  showWaveform: boolean
  pixelsPerSecond: number
  onSelect: (clipId: string) => void
  onMove?: (clipId: string, newStartTime: number, newTrack: number) => void
  onResize?: (clipId: string, newDuration: number) => void
}

export const AudioTrack: React.FC<AudioTrackProps> = ({
  clip,
  x,
  y,
  width,
  height,
  isSelected,
  showWaveform,
  pixelsPerSecond,
  onSelect,
  onMove,
  onResize
}) => {
  const [waveformData, setWaveformData] = useState<number[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const wavesurferRef = useRef<WaveSurfer | null>(null)

  // 生成波形数据
  const generateWaveformData = useCallback(async (audioUrl: string) => {
    if (!showWaveform || !audioUrl) return

    setIsLoading(true)
    
    try {
      // 创建临时的 WaveSurfer 实例来生成波形数据
      const tempContainer = document.createElement('div')
      tempContainer.style.visibility = 'hidden'
      document.body.appendChild(tempContainer)

      const tempWavesurfer = WaveSurfer.create({
        container: tempContainer,
        width: Math.floor(width),
        height: 64,
        normalize: true,
        backend: 'WebAudio'
      })

      tempWavesurfer.on('ready', () => {
        // 获取波形数据
        const peaks = tempWavesurfer.backend.getPeaks(Math.floor(width / 2))
        setWaveformData(Array.from(peaks))
        
        // 清理
        tempWavesurfer.destroy()
        document.body.removeChild(tempContainer)
        setIsLoading(false)
      })

      tempWavesurfer.on('error', (error) => {
        console.error('波形生成错误:', error)
        tempWavesurfer.destroy()
        document.body.removeChild(tempContainer)
        setIsLoading(false)
      })

      tempWavesurfer.load(audioUrl)
    } catch (error) {
      console.error('波形生成失败:', error)
      setIsLoading(false)
    }
  }, [audioUrl, showWaveform, width])

  // 当音频URL或显示设置改变时重新生成波形
  useEffect(() => {
    if (clip.audioUrl && showWaveform) {
      generateWaveformData(clip.audioUrl)
    } else {
      setWaveformData([])
    }
  }, [clip.audioUrl, showWaveform, generateWaveformData])

  // 渲染波形
  const renderWaveform = () => {
    if (!showWaveform || waveformData.length === 0) {
      return null
    }

    const waveformElements = []
    const barWidth = Math.max(1, width / waveformData.length)
    const centerY = height / 2

    for (let i = 0; i < waveformData.length; i++) {
      const barHeight = Math.abs(waveformData[i]) * (height - 8)
      const barX = i * barWidth
      const barY = centerY - barHeight / 2

      waveformElements.push(
        <Rect
          key={`waveform-${i}`}
          x={barX}
          y={barY}
          width={Math.max(1, barWidth - 0.5)}
          height={Math.max(1, barHeight)}
          fill={isSelected ? '#8b5cf6' : '#6366f1'}
          opacity={0.8}
        />
      )
    }

    return waveformElements
  }

  // 渲染简化波形（当没有实际数据时）
  const renderSimpleWaveform = () => {
    if (!showWaveform) return null

    const elements = []
    const barCount = Math.floor(width / 4)
    const centerY = height / 2

    for (let i = 0; i < barCount; i++) {
      const barHeight = Math.random() * (height - 16) + 4
      const barX = i * 4
      const barY = centerY - barHeight / 2

      elements.push(
        <Rect
          key={`simple-wave-${i}`}
          x={barX}
          y={barY}
          width={2}
          height={barHeight}
          fill={isSelected ? '#a855f7' : '#6366f1'}
          opacity={0.6}
        />
      )
    }

    return elements
  }

  // 处理点击
  const handleClick = () => {
    onSelect(clip.id)
  }

  // 处理拖拽
  const handleDragMove = (e: any) => {
    if (!onMove) return

    const newX = e.target.x()
    const newY = e.target.y()
    
    const newTime = newX / pixelsPerSecond
    const newTrack = Math.max(0, Math.floor(newY / height))
    
    // 实时更新位置预览
    e.target.x(Math.max(0, newX))
    e.target.y(Math.max(0, newY))
  }

  const handleDragEnd = (e: any) => {
    if (!onMove) return

    const newX = e.target.x()
    const newY = e.target.y()
    
    const newTime = Math.max(0, newX / pixelsPerSecond)
    const newTrack = Math.max(0, Math.floor(newY / height))
    
    onMove(clip.id, newTime, newTrack)
    
    // 重置位置，让父组件重新渲染
    e.target.x(x)
    e.target.y(y)
  }

  // 获取音频轨道颜色
  const getTrackColors = () => {
    if (isSelected) {
      return {
        fill: '#8b5cf6',
        stroke: '#7c3aed'
      }
    }
    return {
      fill: '#6366f1',
      stroke: '#4f46e5'
    }
  }

  const colors = getTrackColors()

  return (
    <Group
      x={x}
      y={y}
      draggable
      onClick={handleClick}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
    >
      {/* 主要音频轨道背景 */}
      <Rect
        width={width}
        height={height}
        fill={colors.fill}
        stroke={colors.stroke}
        strokeWidth={isSelected ? 2 : 1}
        cornerRadius={4}
        shadowColor={isSelected ? colors.stroke : 'transparent'}
        shadowBlur={isSelected ? 4 : 0}
        shadowOpacity={0.3}
      />

      {/* 波形显示 */}
      {isLoading ? (
        // 加载指示器
        <Group>
          <Rect
            x={4}
            y={height / 2 - 2}
            width={width - 8}
            height={4}
            fill="rgba(255, 255, 255, 0.5)"
            cornerRadius={2}
          />
          <Rect
            x={8}
            y={height / 2 - 1}
            width={Math.min(width - 16, 40)}
            height={2}
            fill="white"
            cornerRadius={1}
          />
        </Group>
      ) : waveformData.length > 0 ? (
        // 实际波形数据
        <Group clipX={4} clipY={4} clipWidth={width - 8} clipHeight={height - 8}>
          {renderWaveform()}
        </Group>
      ) : (
        // 简化波形或占位符
        showWaveform ? (
          <Group clipX={4} clipY={4} clipWidth={width - 8} clipHeight={height - 8}>
            {renderSimpleWaveform()}
          </Group>
        ) : (
          <Rect
            x={4}
            y={height / 2 - 2}
            width={width - 8}
            height={4}
            fill="rgba(255, 255, 255, 0.6)"
            cornerRadius={2}
          />
        )
      )}

      {/* 音频片段标题 */}
      <Rect
        x={0}
        y={0}
        width={width}
        height={16}
        fill="rgba(0, 0, 0, 0.3)"
        cornerRadius={[4, 4, 0, 0]}
      />
      
      {/* 标题文本 */}
      {/* 注意：Konva 的 Text 组件需要单独处理 */}

      {/* 音量指示器 */}
      <Group x={width - 24} y={4}>
        <Rect
          width={20}
          height={12}
          fill="rgba(0, 0, 0, 0.5)"
          cornerRadius={2}
        />
        
        {/* 音量条 */}
        <Rect
          x={2}
          y={4}
          width={(clip.volume / 100) * 16}
          height={4}
          fill="white"
          cornerRadius={1}
        />
      </Group>

      {/* 调整手柄（选中时显示） */}
      {isSelected && onResize && (
        <>
          {/* 左侧调整手柄 */}
          <Rect
            x={0}
            y={0}
            width={4}
            height={height}
            fill="rgba(255, 255, 255, 0.8)"
            stroke={colors.stroke}
            strokeWidth={1}
            cornerRadius={[2, 0, 0, 2]}
          />
          
          {/* 右侧调整手柄 */}
          <Rect
            x={width - 4}
            y={0}
            width={4}
            height={height}
            fill="rgba(255, 255, 255, 0.8)"
            stroke={colors.stroke}
            strokeWidth={1}
            cornerRadius={[0, 2, 2, 0]}
          />
        </>
      )}

      {/* 静音指示器 */}
      {clip.volume === 0 && (
        <Group x={4} y={4}>
          <Rect
            width={16}
            height={12}
            fill="rgba(239, 68, 68, 0.9)"
            cornerRadius={2}
          />
          {/* 静音图标 - 可以用简单的线条表示 */}
          <Line
            points={[4, 4, 12, 8, 4, 12]}
            stroke="white"
            strokeWidth={1}
            closed={false}
          />
          <Line
            points={[10, 6, 14, 6]}
            stroke="white"
            strokeWidth={2}
          />
        </Group>
      )}
    </Group>
  )
}
