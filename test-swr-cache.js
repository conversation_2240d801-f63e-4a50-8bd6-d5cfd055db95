#!/usr/bin/env node

/**
 * Test script for SWR cache implementation
 * Tests the /api/video-records/cached endpoint with both GET and POST methods
 */

const BASE_URL = 'http://localhost:3001'

// Test data
const testData = {
  projectId: '4db5bf32-fdda-4bf0-ab45-f8e8315ef302',
  userId: '559f045d-5477-443c-92ee-93d03fff3b3c',
  shotIds: [
    '7268a0c0-d00f-470c-a7e6-beca3669474c',
    'eb005ede-1e86-44f9-8115-6638dcf2d2ec',
    '74b6048e-631b-4df5-9149-2f547c9837aa'
  ]
}

async function testPOSTMethod() {
  console.log('\n🧪 Testing POST method...')
  
  try {
    const response = await fetch(`${BASE_URL}/api/video-records/cached`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    })
    
    const data = await response.json()
    
    console.log(`📊 POST Response Status: ${response.status}`)
    console.log(`📊 POST Response:`, JSON.stringify(data, null, 2))
    
    if (response.ok) {
      console.log('✅ POST method working correctly')
      return data
    } else {
      console.log('❌ POST method failed')
      return null
    }
  } catch (error) {
    console.error('❌ POST method error:', error.message)
    return null
  }
}

async function testGETMethod() {
  console.log('\n🧪 Testing GET method...')
  
  try {
    const queryParams = new URLSearchParams({
      projectId: testData.projectId,
      userId: testData.userId,
      shotIds: testData.shotIds.join(',')
    })
    
    const response = await fetch(`${BASE_URL}/api/video-records/cached?${queryParams}`)
    const data = await response.json()
    
    console.log(`📊 GET Response Status: ${response.status}`)
    console.log(`📊 GET Response:`, JSON.stringify(data, null, 2))
    
    if (response.ok) {
      console.log('✅ GET method working correctly')
      return data
    } else {
      console.log('❌ GET method failed')
      return null
    }
  } catch (error) {
    console.error('❌ GET method error:', error.message)
    return null
  }
}

async function testCachePerformance() {
  console.log('\n🚀 Testing cache performance...')
  
  const iterations = 5
  const times = []
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now()
    
    const response = await fetch(`${BASE_URL}/api/video-records/cached`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    })
    
    const endTime = Date.now()
    const duration = endTime - startTime
    times.push(duration)
    
    console.log(`📊 Request ${i + 1}: ${duration}ms`)
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  const avgTime = times.reduce((a, b) => a + b, 0) / times.length
  console.log(`📊 Average response time: ${avgTime.toFixed(2)}ms`)
  console.log(`📊 Min response time: ${Math.min(...times)}ms`)
  console.log(`📊 Max response time: ${Math.max(...times)}ms`)
}

async function main() {
  console.log('🎯 SWR Cache Implementation Test')
  console.log('================================')
  console.log(`🔗 Base URL: ${BASE_URL}`)
  console.log(`📋 Test Data:`, JSON.stringify(testData, null, 2))
  
  // Test POST method
  const postResult = await testPOSTMethod()
  
  // Test GET method
  const getResult = await testGETMethod()
  
  // Compare results
  if (postResult && getResult) {
    console.log('\n🔍 Comparing POST vs GET results...')
    
    const postTasks = Object.keys(postResult.tasks || {}).length
    const getTasks = Object.keys(getResult.tasks || {}).length
    
    if (postTasks === getTasks) {
      console.log('✅ Both methods return consistent results')
    } else {
      console.log('⚠️ Results differ between methods')
      console.log(`POST tasks: ${postTasks}, GET tasks: ${getTasks}`)
    }
  }
  
  // Test cache performance
  await testCachePerformance()
  
  console.log('\n🎉 Test completed!')
}

// Run the test
main().catch(console.error)
