"use client"

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { TimelineCanvas } from './TimelineCanvas'
import { TimelineControls } from './TimelineControls'
import { TimelineTracks } from './TimelineTracks'
import { useVideoEditor } from '../../stores'

interface TimelineProps {
  className?: string
  onClipSelect?: (clipId: string) => void
  onTimeChange?: (time: number) => void
}

export const Timeline: React.FC<TimelineProps> = ({
  className = '',
  onClipSelect,
  onTimeChange
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 1200, height: 400 })
  
  const {
    timeline,
    actions,
    setCurrentTime,
    updateVideoClip,
    updateAudioClip,
    updateSubtitleClip
  } = useVideoEditor()

  // 响应式尺寸调整
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        setDimensions({
          width: Math.max(800, rect.width - 200), // 减去轨道面板宽度
          height: Math.max(300, rect.height - 100) // 减去控制面板高度
        })
      }
    }

    updateDimensions()
    window.addEventListener('resize', updateDimensions)
    return () => window.removeEventListener('resize', updateDimensions)
  }, [])

  // 播放控制
  const handlePlay = useCallback(() => {
    console.log('🎬 开始播放')
    // 这里可以集成实际的视频播放逻辑
  }, [])

  const handlePause = useCallback(() => {
    console.log('⏸️ 暂停播放')
  }, [])

  const handleStop = useCallback(() => {
    console.log('⏹️ 停止播放')
    setCurrentTime(0)
  }, [setCurrentTime])

  const handlePrevious = useCallback(() => {
    // 跳转到上一个片段或关键帧
    const currentTime = timeline.currentTime
    // 简单实现：向前跳5秒
    const newTime = Math.max(0, currentTime - 5)
    setCurrentTime(newTime)
    onTimeChange?.(newTime)
  }, [timeline.currentTime, setCurrentTime, onTimeChange])

  const handleNext = useCallback(() => {
    // 跳转到下一个片段或关键帧
    const currentTime = timeline.currentTime
    // 简单实现：向后跳5秒
    const newTime = Math.min(timeline.duration, currentTime + 5)
    setCurrentTime(newTime)
    onTimeChange?.(newTime)
  }, [timeline.currentTime, timeline.duration, setCurrentTime, onTimeChange])

  // 片段移动处理
  const handleClipMove = useCallback((clipId: string, newStartTime: number, newTrack: number) => {
    console.log(`🎬 移动片段 ${clipId} 到时间 ${newStartTime}，轨道 ${newTrack}`)
    
    // 查找片段类型并更新
    const videoClip = actions.getClipById?.(clipId)
    if (videoClip && 'videoUrl' in videoClip) {
      const duration = videoClip.duration
      updateVideoClip(clipId, {
        startTime: newStartTime,
        endTime: newStartTime + duration,
        track: newTrack
      })
    } else if (videoClip && 'audioUrl' in videoClip) {
      const duration = videoClip.duration
      updateAudioClip(clipId, {
        startTime: newStartTime,
        endTime: newStartTime + duration,
        track: newTrack
      })
    } else if (videoClip && 'text' in videoClip) {
      const duration = videoClip.endTime - videoClip.startTime
      updateSubtitleClip(clipId, {
        startTime: newStartTime,
        endTime: newStartTime + duration,
        track: newTrack
      })
    }
  }, [updateVideoClip, updateAudioClip, updateSubtitleClip, actions])

  // 添加轨道处理
  const handleAddTrack = useCallback((type: 'video' | 'audio' | 'subtitle') => {
    console.log(`➕ 添加 ${type} 轨道`)
    // 这里可以实现添加轨道的逻辑
    // 目前轨道是动态计算的，所以不需要特别的添加逻辑
  }, [])

  // 时间变化处理
  const handleTimelineTimeChange = useCallback((time: number) => {
    setCurrentTime(time)
    onTimeChange?.(time)
  }, [setCurrentTime, onTimeChange])

  return (
    <div 
      ref={containerRef}
      className={`timeline-container flex flex-col bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}
    >
      {/* 时间轴控制栏 */}
      <TimelineControls
        onPlay={handlePlay}
        onPause={handlePause}
        onStop={handleStop}
        onPrevious={handlePrevious}
        onNext={handleNext}
      />

      {/* 主时间轴区域 */}
      <div className="flex flex-1 min-h-0">
        {/* 轨道面板 */}
        <div className="w-48 flex-shrink-0">
          <TimelineTracks
            height={dimensions.height}
            onAddTrack={handleAddTrack}
          />
        </div>

        {/* 时间轴画布 */}
        <div className="flex-1 min-w-0">
          <TimelineCanvas
            width={dimensions.width}
            height={dimensions.height}
            onTimeChange={handleTimelineTimeChange}
            onClipSelect={onClipSelect}
            onClipMove={handleClipMove}
          />
        </div>
      </div>

      {/* 状态栏 */}
      <div className="bg-gray-50 border-t border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between text-xs text-gray-600">
          <div className="flex items-center space-x-4">
            <span>
              时间: {Math.floor(timeline.currentTime / 60)}:{Math.floor(timeline.currentTime % 60).toString().padStart(2, '0')}
            </span>
            <span>
              总时长: {Math.floor(timeline.duration / 60)}:{Math.floor(timeline.duration % 60).toString().padStart(2, '0')}
            </span>
            <span>
              缩放: {Math.round(timeline.zoomLevel * 100)}%
            </span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span>
              选中: {timeline.selectedClips.length} 个片段
            </span>
            <span>
              帧率: 30fps
            </span>
            <span>
              分辨率: 1920x1080
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

// 导出时间轴相关组件
export { TimelineCanvas, TimelineControls, TimelineTracks }
