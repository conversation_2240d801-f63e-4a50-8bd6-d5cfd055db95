import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { EditorState, HistoryState } from './types'

interface HistoryStore {
  // 历史状态
  past: EditorState[]
  present: EditorState | null
  future: EditorState[]
  maxHistorySize: number
  
  // 状态查询
  canUndo: boolean
  canRedo: boolean
  
  // 操作方法
  saveState: (state: EditorState) => void
  undo: () => EditorState | null
  redo: () => EditorState | null
  clearHistory: () => void
  setMaxHistorySize: (size: number) => void
  
  // 批量操作支持
  startBatch: () => void
  endBatch: () => void
  isBatching: boolean
  batchedActions: EditorState[]
}

export const useHistoryStore = create<HistoryStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // 初始状态
      past: [],
      present: null,
      future: [],
      maxHistorySize: 50,
      canUndo: false,
      canRedo: false,
      isBatching: false,
      batchedActions: [],

      // 保存状态到历史记录
      saveState: (state) => set((draft) => {
        const { isBatching, batchedActions } = get()
        
        // 如果正在批量操作，先收集状态
        if (isBatching) {
          draft.batchedActions.push(state)
          return
        }
        
        // 创建状态快照
        const stateSnapshot: EditorState = {
          videoClips: state.videoClips.map(clip => ({ ...clip })),
          audioClips: state.audioClips.map(clip => ({ ...clip })),
          subtitleClips: state.subtitleClips.map(clip => ({ ...clip })),
          timeline: { ...state.timeline },
          viewport: { ...state.viewport },
          project: { ...state.project },
          timestamp: Date.now()
        }
        
        // 如果当前状态存在，将其移到过去
        if (draft.present) {
          draft.past.push(draft.present)
          
          // 限制历史记录大小
          if (draft.past.length > draft.maxHistorySize) {
            draft.past.shift()
          }
        }
        
        // 设置新的当前状态
        draft.present = stateSnapshot
        
        // 清空未来状态（因为有了新的操作）
        draft.future = []
        
        // 更新状态标志
        draft.canUndo = draft.past.length > 0
        draft.canRedo = false
      }),

      // 撤销操作
      undo: () => {
        const state = get()
        if (!state.canUndo || state.past.length === 0) {
          return null
        }
        
        let undoState: EditorState | null = null
        
        set((draft) => {
          const previousState = draft.past.pop()
          if (previousState && draft.present) {
            // 将当前状态移到未来
            draft.future.unshift(draft.present)
            
            // 设置前一个状态为当前状态
            draft.present = previousState
            undoState = previousState
            
            // 更新状态标志
            draft.canUndo = draft.past.length > 0
            draft.canRedo = true
          }
        })
        
        return undoState
      },

      // 重做操作
      redo: () => {
        const state = get()
        if (!state.canRedo || state.future.length === 0) {
          return null
        }
        
        let redoState: EditorState | null = null
        
        set((draft) => {
          const nextState = draft.future.shift()
          if (nextState && draft.present) {
            // 将当前状态移到过去
            draft.past.push(draft.present)
            
            // 设置下一个状态为当前状态
            draft.present = nextState
            redoState = nextState
            
            // 更新状态标志
            draft.canUndo = true
            draft.canRedo = draft.future.length > 0
          }
        })
        
        return redoState
      },

      // 清空历史记录
      clearHistory: () => set((draft) => {
        draft.past = []
        draft.future = []
        draft.present = null
        draft.canUndo = false
        draft.canRedo = false
        draft.isBatching = false
        draft.batchedActions = []
      }),

      // 设置最大历史记录大小
      setMaxHistorySize: (size) => set((draft) => {
        draft.maxHistorySize = Math.max(1, Math.min(size, 100))
        
        // 如果当前历史记录超过新的限制，裁剪它
        if (draft.past.length > draft.maxHistorySize) {
          draft.past = draft.past.slice(-draft.maxHistorySize)
        }
      }),

      // 开始批量操作
      startBatch: () => set((draft) => {
        draft.isBatching = true
        draft.batchedActions = []
      }),

      // 结束批量操作
      endBatch: () => set((draft) => {
        if (draft.isBatching && draft.batchedActions.length > 0) {
          // 只保存批量操作的最后一个状态
          const finalState = draft.batchedActions[draft.batchedActions.length - 1]
          
          // 创建状态快照
          const stateSnapshot: EditorState = {
            videoClips: finalState.videoClips.map(clip => ({ ...clip })),
            audioClips: finalState.audioClips.map(clip => ({ ...clip })),
            subtitleClips: finalState.subtitleClips.map(clip => ({ ...clip })),
            timeline: { ...finalState.timeline },
            viewport: { ...finalState.viewport },
            project: { ...finalState.project },
            timestamp: Date.now()
          }
          
          // 如果当前状态存在，将其移到过去
          if (draft.present) {
            draft.past.push(draft.present)
            
            // 限制历史记录大小
            if (draft.past.length > draft.maxHistorySize) {
              draft.past.shift()
            }
          }
          
          // 设置新的当前状态
          draft.present = stateSnapshot
          
          // 清空未来状态
          draft.future = []
          
          // 更新状态标志
          draft.canUndo = draft.past.length > 0
          draft.canRedo = false
        }
        
        draft.isBatching = false
        draft.batchedActions = []
      })
    }))
  )
)

// 历史记录工具函数
export const historyUtils = {
  // 创建状态快照
  createSnapshot: (state: Partial<EditorState>): EditorState => ({
    videoClips: state.videoClips || [],
    audioClips: state.audioClips || [],
    subtitleClips: state.subtitleClips || [],
    timeline: state.timeline || {
      currentTime: 0,
      duration: 100,
      isPlaying: false,
      playbackRate: 1,
      zoomLevel: 1,
      scrollPosition: 0,
      selectedClips: [],
      clipboardClips: []
    },
    viewport: state.viewport || {
      width: 1200,
      height: 400,
      pixelsPerSecond: 20,
      visibleTimeRange: { start: 0, end: 60 }
    },
    project: state.project || {
      name: 'AI生成内容',
      description: '',
      resolution: { width: 1920, height: 1080 },
      frameRate: 30,
      outputFormat: 'mp4',
      quality: 'high'
    },
    timestamp: Date.now()
  }),

  // 比较两个状态是否相同（用于避免重复保存）
  statesEqual: (state1: EditorState, state2: EditorState): boolean => {
    return (
      JSON.stringify(state1.videoClips) === JSON.stringify(state2.videoClips) &&
      JSON.stringify(state1.audioClips) === JSON.stringify(state2.audioClips) &&
      JSON.stringify(state1.subtitleClips) === JSON.stringify(state2.subtitleClips) &&
      JSON.stringify(state1.timeline) === JSON.stringify(state2.timeline) &&
      JSON.stringify(state1.project) === JSON.stringify(state2.project)
    )
  },

  // 获取状态差异描述（用于调试）
  getStateDiff: (oldState: EditorState, newState: EditorState): string[] => {
    const diffs: string[] = []
    
    if (oldState.videoClips.length !== newState.videoClips.length) {
      diffs.push(`视频片段数量: ${oldState.videoClips.length} -> ${newState.videoClips.length}`)
    }
    
    if (oldState.audioClips.length !== newState.audioClips.length) {
      diffs.push(`音频片段数量: ${oldState.audioClips.length} -> ${newState.audioClips.length}`)
    }
    
    if (oldState.subtitleClips.length !== newState.subtitleClips.length) {
      diffs.push(`字幕片段数量: ${oldState.subtitleClips.length} -> ${newState.subtitleClips.length}`)
    }
    
    if (oldState.timeline.currentTime !== newState.timeline.currentTime) {
      diffs.push(`播放时间: ${oldState.timeline.currentTime} -> ${newState.timeline.currentTime}`)
    }
    
    if (oldState.timeline.zoomLevel !== newState.timeline.zoomLevel) {
      diffs.push(`缩放级别: ${oldState.timeline.zoomLevel} -> ${newState.timeline.zoomLevel}`)
    }
    
    return diffs
  }
}

// 自动保存历史记录的 Hook
import React from 'react'

export const useAutoSaveHistory = (editorState: Partial<EditorState>, delay = 1000) => {
  const { saveState } = useHistoryStore()

  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (editorState.videoClips || editorState.audioClips || editorState.subtitleClips) {
        const snapshot = historyUtils.createSnapshot(editorState)
        saveState(snapshot)
      }
    }, delay)

    return () => clearTimeout(timer)
  }, [editorState, delay, saveState])
}

// 键盘快捷键支持
export const useHistoryKeyboard = () => {
  const { undo, redo, canUndo, canRedo } = useHistoryStore()

  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && !event.shiftKey && event.key === 'z' && canUndo) {
        event.preventDefault()
        undo()
      } else if (
        ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'z') ||
        ((event.ctrlKey || event.metaKey) && event.key === 'y')
      ) {
        if (canRedo) {
          event.preventDefault()
          redo()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [undo, redo, canUndo, canRedo])
}
