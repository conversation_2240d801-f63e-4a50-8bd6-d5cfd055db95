"use client"

import React, { useState, use<PERSON><PERSON>back, useEffect, useMemo } from 'react'
import useS<PERSON> from 'swr'
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import {
  Video,
  Music,
  Type,
  Download,
  Upload,
  Save,
  Sparkles,
  RefreshCw,
  SkipBack,
  Loader2,
  CheckCircle,
  AlertCircle
} from "lucide-react"

// 导入现有的组件和工具
import { DragDropProvider } from './dnd'
import { Timeline } from './timeline'
import { AudioEditor } from './audio'
import { SubtitleEditor } from './subtitle'
import { VideoExporter } from './ffmpeg'
import { MultiTrackPreview } from './preview'
import { useVideoEditor, VideoClip, AudioClip, SubtitleClip } from '../stores'

// SWR fetchers for cache data
const fetchVideoTaskStatus = async (key: string) => {
  const [, projectId, userId, shotIdsStr] = key.split('|')
  const shotIds = shotIdsStr.split(',')

  const response = await fetch('/api/video-records/cached', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ projectId, userId, shotIds })
  })

  if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
  return response.json()
}

const fetchImageTaskStatus = async (key: string) => {
  const [, projectId, userId, shotNumbersStr] = key.split('|')
  const shotNumbers = shotNumbersStr.split(',').map(num => parseInt(num))

  const response = await fetch('/api/image-records/cached', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ projectId, userId, shotNumbers })
  })

  if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
  return response.json()
}

interface VideoEditorProps {
  projectId?: string
  userId?: string
  scriptData?: any
  shotsWithImages?: any[]
  onSave?: (projectData: any) => void
  onExport?: (videoData: Uint8Array, filename: string) => void
  onBack?: () => void
  className?: string
}

export const VideoEditor: React.FC<VideoEditorProps> = ({
  projectId,
  userId,
  scriptData,
  shotsWithImages,
  onSave,
  onExport,
  onBack,
  className = ''
}) => {
  const {
    videoClips,
    audioClips,
    subtitleClips,
    timeline,
    ui,
    project,
    actions,
    setCurrentTime,
    setPlaying,
    selectClips,
    addVideoClip,
    addAudioClip,
    addSubtitleClip,
    updateVideoClip,
    updateAudioClip,
    updateSubtitleClip,
    removeVideoClip,
    removeAudioClip,
    removeSubtitleClip
  } = useVideoEditor()

  // 状态管理
  const [activePanel, setActivePanel] = useState<'media' | 'audio' | 'subtitle' | 'effects' | 'export'>('media')
  const [selectedClipId, setSelectedClipId] = useState<string | null>(null)
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false)
  const [previewTime, setPreviewTime] = useState(0)
  const [isSmartMode, setIsSmartMode] = useState(true)
  const [volume, setVolume] = useState([80])
  const [isMuted, setIsMuted] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)

  // SWR 缓存数据获取
  const shotIds = useMemo(() => {
    return shotsWithImages?.map(shot => shot.shotId).filter(Boolean) as string[] || []
  }, [shotsWithImages])

  const shotNumbers = useMemo(() => {
    return shotsWithImages?.map(shot => shot.shotNumber).filter(Boolean) as number[] || []
  }, [shotsWithImages])

  const videoCacheKey = projectId && userId && shotIds?.length
    ? `video-tasks|${projectId}|${userId}|${shotIds.join(',')}`
    : null

  const imageCacheKey = projectId && userId && shotNumbers?.length
    ? `image-tasks|${projectId}|${userId}|${shotNumbers.join(',')}`
    : null

  const { data: videoTaskData, error: videoError, isLoading: isVideoLoading, mutate: mutateVideo } = useSWR(
    videoCacheKey,
    fetchVideoTaskStatus,
    {
      dedupingInterval: 5 * 60 * 1000,
      revalidateOnFocus: true,
      refreshInterval: 2 * 60 * 1000,
      errorRetryCount: 3
    }
  )

  const { data: imageTaskData, error: imageError, isLoading: isImageLoading, mutate: mutateImage } = useSWR(
    imageCacheKey,
    fetchImageTaskStatus,
    {
      dedupingInterval: 5 * 60 * 1000,
      revalidateOnFocus: true,
      refreshInterval: 2 * 60 * 1000,
      errorRetryCount: 3
    }
  )

  // 获取选中的片段
  const selectedClip = selectedClipId
    ? videoClips.find(c => c.id === selectedClipId) ||
      audioClips.find(c => c.id === selectedClipId) ||
      subtitleClips.find(c => c.id === selectedClipId)
    : null

  // 智能生成视频片段：优先使用视频缓存，其次使用图片缓存
  const generateSmartVideoClips = useCallback((): VideoClip[] => {
    if (!shotsWithImages || shotsWithImages.length === 0) {
      console.log('没有场景数据可用于生成视频片段')
      return []
    }

    console.log('🎬 [SMART-CLIPS] 开始智能生成视频片段')
    console.log('🎬 [SMART-CLIPS] 视频缓存数据:', videoTaskData)
    console.log('🎬 [SMART-CLIPS] 图片缓存数据:', imageTaskData)

    const newVideoClips: VideoClip[] = shotsWithImages.map((shot, index) => {
      const duration = 5
      const startTime = index * duration

      // 检查视频缓存
      let hasVideoCache = false
      let videoUrl: string | undefined
      let videoTaskStatus: string | undefined

      if (videoTaskData?.tasks && shot.shotId) {
        const videoTasks = videoTaskData.tasks[shot.shotId]
        if (videoTasks && videoTasks.length > 0) {
          const latestVideoTask = videoTasks[0]
          videoTaskStatus = latestVideoTask.status

          if (latestVideoTask.status === 'completed' && latestVideoTask.generated_videos) {
            hasVideoCache = true
            const firstVideo = latestVideoTask.generated_videos[0]
            if (firstVideo) {
              videoUrl = firstVideo.video_url
            }
          }
        }
      }

      // 检查图片缓存
      let hasImageCache = false
      let thumbnailUrl = '/placeholder.svg'
      let imageTaskStatus: string | undefined

      if (imageTaskData?.tasks && shot.shotNumber) {
        const imageTasks = imageTaskData.tasks[shot.shotNumber]
        if (imageTasks && imageTasks.length > 0) {
          const latestImageTask = imageTasks[0]
          imageTaskStatus = latestImageTask.status

          if (latestImageTask.status === 'completed' && latestImageTask.generated_images) {
            hasImageCache = true
            const firstImage = latestImageTask.generated_images[0]
            if (firstImage) {
              thumbnailUrl = firstImage.cdn_url || firstImage.image_url
            }
          }
        }
      }

      // 如果没有缓存数据，使用原始的shotsWithImages数据
      if (!hasVideoCache && !hasImageCache) {
        const primaryImage = shot.images?.find((img: any) => img.isPrimary) || shot.images?.[0]
        if (primaryImage) {
          thumbnailUrl = primaryImage.url
          hasImageCache = true
        }
        videoUrl = shot.videoUrl
      }

      // 确定缓存源：优先视频，其次图片
      let cacheSource: 'video' | 'image' | 'none' = 'none'
      if (hasVideoCache) {
        cacheSource = 'video'
      } else if (hasImageCache) {
        cacheSource = 'image'
      }

      console.log(`🎬 [SMART-CLIPS] 镜头 ${shot.shotNumber || index + 1}:`, {
        shotId: shot.shotId,
        shotNumber: shot.shotNumber,
        hasVideoCache,
        hasImageCache,
        cacheSource,
        videoUrl: videoUrl ? '有视频' : '无视频',
        thumbnailUrl: thumbnailUrl !== '/placeholder.svg' ? '有缩略图' : '无缩略图'
      })

      return {
        id: `smart-clip-${shot.shotId || shot.shotNumber || index}`,
        name: `镜头 ${shot.shotNumber || index + 1}`,
        duration: duration,
        thumbnail: thumbnailUrl,
        videoUrl: videoUrl,
        startTime: startTime,
        endTime: startTime + duration,
        track: 0,
        sceneData: shot,
        hasVideoCache,
        hasImageCache,
        videoTaskStatus,
        imageTaskStatus,
        cacheSource,
        lastCacheCheck: Date.now()
      }
    })

    console.log(`🎬 [SMART-CLIPS] 智能生成完成，共 ${newVideoClips.length} 个片段`)
    console.log(`🎬 [SMART-CLIPS] 视频缓存: ${newVideoClips.filter(c => c.hasVideoCache).length} 个`)
    console.log(`🎬 [SMART-CLIPS] 图片缓存: ${newVideoClips.filter(c => c.hasImageCache).length} 个`)

    return newVideoClips
  }, [shotsWithImages, videoTaskData, imageTaskData])

  // 智能剪辑处理
  const handleSmartEditing = useCallback(() => {
    console.log('🎬 [SMART-EDITING] 开始智能剪辑')

    const newVideoClips = generateSmartVideoClips()

    actions.startBatch()
    // 清空现有片段
    videoClips.forEach(clip => removeVideoClip(clip.id))
    // 添加新片段
    newVideoClips.forEach(clip => addVideoClip(clip))
    actions.endBatch()

    console.log('🎬 [SMART-EDITING] 智能剪辑完成，生成了', newVideoClips.length, '个视频片段')

    // 显示缓存统计信息
    const videoCount = newVideoClips.filter(c => c.hasVideoCache).length
    const imageCount = newVideoClips.filter(c => c.hasImageCache).length
    const noCacheCount = newVideoClips.filter(c => c.cacheSource === 'none').length

    console.log(`🎬 [SMART-EDITING] 缓存统计: 视频${videoCount}个, 图片${imageCount}个, 无缓存${noCacheCount}个`)
  }, [generateSmartVideoClips, actions, videoClips, removeVideoClip, addVideoClip])

  // 刷新缓存数据
  const refreshTaskStatus = useCallback(() => {
    const promises = []
    if (mutateVideo) promises.push(mutateVideo())
    if (mutateImage) promises.push(mutateImage())
    return Promise.all(promises)
  }, [mutateVideo, mutateImage])

  // 初始化智能剪辑
  useEffect(() => {
    console.log('🎬 [INIT] VideoEditor 初始化')
    console.log('🎬 [INIT] shotsWithImages:', shotsWithImages)
    console.log('🎬 [INIT] 视频缓存加载状态:', isVideoLoading)
    console.log('🎬 [INIT] 图片缓存加载状态:', isImageLoading)

    // 等待缓存数据加载完成后再生成视频片段
    if (shotsWithImages && shotsWithImages.length > 0 && !isVideoLoading && !isImageLoading && !isInitialized) {
      const clips = generateSmartVideoClips()

      actions.startBatch()
      clips.forEach(clip => addVideoClip(clip))
      actions.endBatch()

      setIsInitialized(true)
      console.log('🎬 [INIT] 智能初始化完成，生成了', clips.length, '个视频片段')

      // 显示缓存统计
      const videoCount = clips.filter(c => c.hasVideoCache).length
      const imageCount = clips.filter(c => c.hasImageCache).length
      console.log(`🎬 [INIT] 缓存统计: 视频${videoCount}个, 图片${imageCount}个`)
    }
  }, [shotsWithImages, videoTaskData, imageTaskData, isVideoLoading, isImageLoading, isInitialized, generateSmartVideoClips, actions, addVideoClip])

  // 片段选择处理
  const handleClipSelect = useCallback((clipId: string) => {
    setSelectedClipId(clipId)
    selectClips([clipId])
  }, [selectClips])

  // 时间变化处理
  const handleTimeChange = useCallback((time: number) => {
    setCurrentTime(time)
    setPreviewTime(time)
  }, [setCurrentTime])

  // 播放控制
  const handlePlayPause = useCallback(() => {
    const newPlaying = !timeline.isPlaying
    setPlaying(newPlaying)
    setIsPreviewPlaying(newPlaying)
  }, [timeline.isPlaying, setPlaying])

  // 片段更新处理
  const handleClipUpdate = useCallback((clipId: string, updates: any) => {
    const videoClip = videoClips.find(c => c.id === clipId)
    const audioClip = audioClips.find(c => c.id === clipId)
    const subtitleClip = subtitleClips.find(c => c.id === clipId)

    if (videoClip) {
      updateVideoClip(clipId, updates)
    } else if (audioClip) {
      updateAudioClip(clipId, updates)
    } else if (subtitleClip) {
      updateSubtitleClip(clipId, updates)
    }
  }, [videoClips, audioClips, subtitleClips, updateVideoClip, updateAudioClip, updateSubtitleClip])

  // 片段删除处理
  const handleClipDelete = useCallback((clipId: string) => {
    const videoClip = videoClips.find(c => c.id === clipId)
    const audioClip = audioClips.find(c => c.id === clipId)
    const subtitleClip = subtitleClips.find(c => c.id === clipId)

    if (videoClip) {
      removeVideoClip(clipId)
    } else if (audioClip) {
      removeAudioClip(clipId)
    } else if (subtitleClip) {
      removeSubtitleClip(clipId)
    }

    if (selectedClipId === clipId) {
      setSelectedClipId(null)
    }
  }, [videoClips, audioClips, subtitleClips, removeVideoClip, removeAudioClip, removeSubtitleClip, selectedClipId])

  // 片段复制处理
  const handleClipDuplicate = useCallback((clipId: string) => {
    const videoClip = videoClips.find(c => c.id === clipId)
    const audioClip = audioClips.find(c => c.id === clipId)
    const subtitleClip = subtitleClips.find(c => c.id === clipId)

    if (videoClip) {
      const newClip = {
        ...videoClip,
        id: `${clipId}-copy-${Date.now()}`,
        startTime: videoClip.endTime,
        endTime: videoClip.endTime + videoClip.duration
      }
      addVideoClip(newClip)
    } else if (audioClip) {
      const newClip = {
        ...audioClip,
        id: `${clipId}-copy-${Date.now()}`,
        startTime: audioClip.endTime,
        endTime: audioClip.endTime + audioClip.duration
      }
      addAudioClip(newClip)
    } else if (subtitleClip) {
      const newClip = {
        ...subtitleClip,
        id: `${clipId}-copy-${Date.now()}`,
        startTime: subtitleClip.endTime,
        endTime: subtitleClip.endTime + (subtitleClip.endTime - subtitleClip.startTime)
      }
      addSubtitleClip(newClip)
    }
  }, [videoClips, audioClips, subtitleClips, addVideoClip, addAudioClip, addSubtitleClip])

  // 保存项目
  const handleSave = useCallback(() => {
    const projectData = {
      id: projectId,
      name: project.name,
      description: project.description,
      videoClips,
      audioClips,
      subtitleClips,
      timeline,
      project,
      savedAt: new Date().toISOString()
    }
    onSave?.(projectData)
  }, [projectId, project, videoClips, audioClips, subtitleClips, timeline, onSave])

  // 导出处理
  const handleExportComplete = useCallback((videoData: Uint8Array, filename: string) => {
    onExport?.(videoData, filename)
  }, [onExport])

  return (
    <ResponsiveLayout
      className={className}
      topBar={
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center space-x-4">
            {/* 返回按钮 */}
            {onBack && (
              <Button variant="outline" size="sm" onClick={onBack}>
                <SkipBack className="w-4 h-4 mr-1" />
                返回
              </Button>
            )}

            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Video className="w-4 h-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-800">{project.name || '视频编辑项目'}</h1>
                <p className="text-xs text-gray-500">AI智能视频编辑器</p>
              </div>
            </div>

            <Separator orientation="vertical" className="h-8" />

            {/* 项目统计 */}
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="text-xs">
                {videoClips.length} 视频
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {audioClips.length} 音频
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {subtitleClips.length} 字幕
              </Badge>
            </div>

            {/* 缓存状态指示器 */}
            {(isVideoLoading || isImageLoading) && (
              <div className="flex items-center space-x-2">
                <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                <span className="text-xs text-gray-600">加载缓存数据...</span>
              </div>
            )}
          </div>

          {/* 右侧操作按钮 */}
          <div className="flex items-center space-x-2">
            {/* 智能模式切换 */}
            <div className="flex items-center space-x-2">
              <Switch
                checked={isSmartMode}
                onCheckedChange={setIsSmartMode}
                id="smart-mode"
              />
              <Label htmlFor="smart-mode" className="text-sm">智能模式</Label>
            </div>

            <Separator orientation="vertical" className="h-6" />

            {/* 刷新缓存 */}
            <Button
              variant="outline"
              size="sm"
              onClick={refreshTaskStatus}
              disabled={isVideoLoading || isImageLoading}
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              刷新
            </Button>

            {/* 重新生成 */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleSmartEditing}
              disabled={isVideoLoading || isImageLoading}
            >
              <Sparkles className="w-4 h-4 mr-1" />
              重新生成
            </Button>

            <Button variant="outline" size="sm" onClick={handleSave}>
              <Save className="w-4 h-4 mr-1" />
              保存
            </Button>

            <Button size="sm" className="bg-green-500 hover:bg-green-600">
              <Download className="w-4 h-4 mr-1" />
              导出
            </Button>
          </div>
        </div>
      }

        {/* 主要内容区域 */}
        <div className="flex-1 flex min-h-0">
          {/* 左侧面板 */}
          <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
            <Tabs value={activePanel} onValueChange={(value: any) => setActivePanel(value)} className="flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-5 m-2">
                <TabsTrigger value="media" className="text-xs">
                  <Video className="w-3 h-3 mr-1" />
                  媒体
                </TabsTrigger>
                <TabsTrigger value="audio" className="text-xs">
                  <Music className="w-3 h-3 mr-1" />
                  音频
                </TabsTrigger>
                <TabsTrigger value="subtitle" className="text-xs">
                  <Type className="w-3 h-3 mr-1" />
                  字幕
                </TabsTrigger>
                <TabsTrigger value="effects" className="text-xs">
                  <Sparkles className="w-3 h-3 mr-1" />
                  特效
                </TabsTrigger>
                <TabsTrigger value="export" className="text-xs">
                  <Download className="w-3 h-3 mr-1" />
                  导出
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden">
                <TabsContent value="media" className="h-full p-4 overflow-y-auto">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-semibold">视频素材</h3>
                      <Button size="sm" variant="outline">
                        <Upload className="w-3 h-3 mr-1" />
                        上传
                      </Button>
                    </div>
                    
                    <div className="space-y-2">
                      {videoClips.map((clip) => (
                        <div
                          key={clip.id}
                          className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                            selectedClipId === clip.id
                              ? 'border-blue-300 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => handleClipSelect(clip.id)}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-8 bg-gray-200 rounded overflow-hidden">
                              {clip.thumbnail && (
                                <img 
                                  src={clip.thumbnail} 
                                  alt={clip.name}
                                  className="w-full h-full object-cover"
                                />
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">{clip.name}</p>
                              <p className="text-xs text-gray-500">{Math.floor(clip.duration)}s</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="audio" className="h-full p-0">
                  <AudioEditor
                    selectedClip={selectedClip && 'audioUrl' in selectedClip ? selectedClip as AudioClip : undefined}
                    onClipUpdate={handleClipUpdate}
                    onClipDelete={handleClipDelete}
                    onClipDuplicate={handleClipDuplicate}
                  />
                </TabsContent>

                <TabsContent value="subtitle" className="h-full p-0">
                  <SubtitleEditor
                    selectedClip={selectedClip && 'text' in selectedClip ? selectedClip as SubtitleClip : undefined}
                    onClipUpdate={handleClipUpdate}
                    onClipDelete={handleClipDelete}
                    onClipDuplicate={handleClipDuplicate}
                    onAddClip={(clip) => addSubtitleClip({ ...clip, id: `subtitle-${Date.now()}` })}
                  />
                </TabsContent>

                <TabsContent value="effects" className="h-full p-4">
                  <div className="text-center text-gray-500">
                    <Sparkles className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">特效功能开发中...</p>
                  </div>
                </TabsContent>

                <TabsContent value="export" className="h-full p-0">
                  <VideoExporter
                    onExportComplete={handleExportComplete}
                    onError={(error) => console.error('导出错误:', error)}
                  />
                </TabsContent>
              </div>
            </Tabs>
          </div>

          {/* 中央内容区域 */}
          <div className="flex-1 flex flex-col min-w-0">
            {/* 预览区域 */}
            <div className="flex-1 p-6 pb-0">
              <Card className="h-full">
                <CardContent className="p-0 h-full relative">
                  <div className="w-full h-full bg-black rounded-lg flex items-center justify-center relative overflow-hidden">
                    {/* 视频预览区域 */}
                    <div className="text-white text-center">
                      <div className="text-6xl mb-4">🎬</div>
                      <p className="text-xl mb-2">视频预览</p>
                      <p className="text-sm opacity-70">
                        时间: {Math.floor(previewTime / 60)}:{Math.floor(previewTime % 60).toString().padStart(2, '0')}
                      </p>
                    </div>

                    {/* 字幕渲染层 */}
                    <SubtitleRenderer
                      subtitles={subtitleClips}
                      currentTime={previewTime}
                      videoWidth={1920}
                      videoHeight={1080}
                    />

                    {/* 播放控制覆盖层 */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={handlePlayPause}
                        className="bg-black/50 hover:bg-black/70 text-white border-white/20"
                      >
                        {isPreviewPlaying ? (
                          <Pause className="w-4 h-4" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 时间轴区域 */}
            <div className="p-6 pt-0">
              <Timeline
                onClipSelect={handleClipSelect}
                onTimeChange={handleTimeChange}
              />
            </div>
          </div>

          {/* 右侧属性面板 */}
          <div className="w-80 bg-white border-l border-gray-200 p-4 overflow-y-auto">
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-semibold mb-3">项目信息</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">分辨率:</span>
                    <span>{project.resolution.width}x{project.resolution.height}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">帧率:</span>
                    <span>{project.frameRate} FPS</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">总时长:</span>
                    <span>{Math.floor(timeline.duration / 60)}:{Math.floor(timeline.duration % 60).toString().padStart(2, '0')}</span>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-semibold mb-3">选中片段</h3>
                {selectedClip ? (
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">名称:</span>
                      <span className="truncate ml-2">{selectedClip.name || '未命名'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">轨道:</span>
                      <span>{selectedClip.track}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">开始:</span>
                      <span>{selectedClip.startTime.toFixed(1)}s</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">结束:</span>
                      <span>{selectedClip.endTime.toFixed(1)}s</span>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">未选择片段</p>
                )}
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-semibold mb-3">快捷操作</h3>
                <div className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Scissors className="w-3 h-3 mr-2" />
                    分割片段
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Layers className="w-3 h-3 mr-2" />
                    调整层级
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Settings className="w-3 h-3 mr-2" />
                    片段设置
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DragDropProvider>
  )
}
