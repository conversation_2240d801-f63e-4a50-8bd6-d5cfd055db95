"use client"

import React, { useState, useCallback, useEffect } from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Video,
  Music,
  Type,
  Settings,
  Download,
  Upload,
  Play,
  Pause,
  Save,
  Eye,
  Layers,
  Scissors,
  Sparkles,
  RefreshCw
} from "lucide-react"

// 导入所有组件
import { DragDropProvider } from './dnd'
import { Timeline } from './timeline'
import { WaveformVisualization, AudioEditor } from './audio'
import { SubtitleEditor, SubtitleRenderer } from './subtitle'
import { VideoProcessor, VideoExporter } from './ffmpeg'
import { useVideoEditor, VideoClip, AudioClip, SubtitleClip } from '../stores'

interface VideoEditorProps {
  projectId?: string
  userId?: string
  scriptData?: any
  shotsWithImages?: any[]
  onSave?: (projectData: any) => void
  onExport?: (videoData: Uint8Array, filename: string) => void
  className?: string
}

export const VideoEditor: React.FC<VideoEditorProps> = ({
  projectId,
  userId,
  scriptData,
  shotsWithImages,
  onSave,
  onExport,
  className = ''
}) => {
  const {
    videoClips,
    audioClips,
    subtitleClips,
    timeline,
    ui,
    project,
    actions,
    setCurrentTime,
    setPlaying,
    selectClips,
    addVideoClip,
    addAudioClip,
    addSubtitleClip,
    updateVideoClip,
    updateAudioClip,
    updateSubtitleClip,
    removeVideoClip,
    removeAudioClip,
    removeSubtitleClip
  } = useVideoEditor()

  const [activePanel, setActivePanel] = useState<'media' | 'audio' | 'subtitle' | 'effects' | 'export'>('media')
  const [selectedClipId, setSelectedClipId] = useState<string | null>(null)
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false)
  const [previewTime, setPreviewTime] = useState(0)

  // 获取选中的片段
  const selectedClip = selectedClipId 
    ? videoClips.find(c => c.id === selectedClipId) ||
      audioClips.find(c => c.id === selectedClipId) ||
      subtitleClips.find(c => c.id === selectedClipId)
    : null

  // 智能生成视频片段（从原有逻辑移植）
  const generateSmartVideoClips = useCallback((): VideoClip[] => {
    if (!shotsWithImages || shotsWithImages.length === 0) {
      return []
    }

    return shotsWithImages.map((shot, index) => {
      const duration = 5
      const startTime = index * duration

      // 获取缩略图
      const primaryImage = shot.images?.find((img: any) => img.isPrimary) || shot.images?.[0]
      const thumbnailUrl = primaryImage?.url || '/placeholder.svg'

      return {
        id: `smart-clip-${shot.shotId || shot.shotNumber || index}`,
        name: `镜头 ${shot.shotNumber || index + 1}`,
        duration: duration,
        thumbnail: thumbnailUrl,
        videoUrl: shot.videoUrl,
        startTime: startTime,
        endTime: startTime + duration,
        track: 0,
        sceneData: shot,
        hasVideoCache: !!shot.videoUrl,
        hasImageCache: !!primaryImage,
        cacheSource: shot.videoUrl ? 'video' : (primaryImage ? 'image' : 'none'),
        lastCacheCheck: Date.now()
      }
    })
  }, [shotsWithImages])

  // 初始化智能剪辑
  useEffect(() => {
    if (shotsWithImages && shotsWithImages.length > 0 && videoClips.length === 0) {
      const smartClips = generateSmartVideoClips()
      actions.startBatch()
      smartClips.forEach(clip => addVideoClip(clip))
      actions.endBatch()
    }
  }, [shotsWithImages, videoClips.length, generateSmartVideoClips, addVideoClip, actions])

  // 片段选择处理
  const handleClipSelect = useCallback((clipId: string) => {
    setSelectedClipId(clipId)
    selectClips([clipId])
  }, [selectClips])

  // 时间变化处理
  const handleTimeChange = useCallback((time: number) => {
    setCurrentTime(time)
    setPreviewTime(time)
  }, [setCurrentTime])

  // 播放控制
  const handlePlayPause = useCallback(() => {
    const newPlaying = !timeline.isPlaying
    setPlaying(newPlaying)
    setIsPreviewPlaying(newPlaying)
  }, [timeline.isPlaying, setPlaying])

  // 片段更新处理
  const handleClipUpdate = useCallback((clipId: string, updates: any) => {
    const videoClip = videoClips.find(c => c.id === clipId)
    const audioClip = audioClips.find(c => c.id === clipId)
    const subtitleClip = subtitleClips.find(c => c.id === clipId)

    if (videoClip) {
      updateVideoClip(clipId, updates)
    } else if (audioClip) {
      updateAudioClip(clipId, updates)
    } else if (subtitleClip) {
      updateSubtitleClip(clipId, updates)
    }
  }, [videoClips, audioClips, subtitleClips, updateVideoClip, updateAudioClip, updateSubtitleClip])

  // 片段删除处理
  const handleClipDelete = useCallback((clipId: string) => {
    const videoClip = videoClips.find(c => c.id === clipId)
    const audioClip = audioClips.find(c => c.id === clipId)
    const subtitleClip = subtitleClips.find(c => c.id === clipId)

    if (videoClip) {
      removeVideoClip(clipId)
    } else if (audioClip) {
      removeAudioClip(clipId)
    } else if (subtitleClip) {
      removeSubtitleClip(clipId)
    }

    if (selectedClipId === clipId) {
      setSelectedClipId(null)
    }
  }, [videoClips, audioClips, subtitleClips, removeVideoClip, removeAudioClip, removeSubtitleClip, selectedClipId])

  // 片段复制处理
  const handleClipDuplicate = useCallback((clipId: string) => {
    const videoClip = videoClips.find(c => c.id === clipId)
    const audioClip = audioClips.find(c => c.id === clipId)
    const subtitleClip = subtitleClips.find(c => c.id === clipId)

    if (videoClip) {
      const newClip = {
        ...videoClip,
        id: `${clipId}-copy-${Date.now()}`,
        startTime: videoClip.endTime,
        endTime: videoClip.endTime + videoClip.duration
      }
      addVideoClip(newClip)
    } else if (audioClip) {
      const newClip = {
        ...audioClip,
        id: `${clipId}-copy-${Date.now()}`,
        startTime: audioClip.endTime,
        endTime: audioClip.endTime + audioClip.duration
      }
      addAudioClip(newClip)
    } else if (subtitleClip) {
      const newClip = {
        ...subtitleClip,
        id: `${clipId}-copy-${Date.now()}`,
        startTime: subtitleClip.endTime,
        endTime: subtitleClip.endTime + (subtitleClip.endTime - subtitleClip.startTime)
      }
      addSubtitleClip(newClip)
    }
  }, [videoClips, audioClips, subtitleClips, addVideoClip, addAudioClip, addSubtitleClip])

  // 保存项目
  const handleSave = useCallback(() => {
    const projectData = {
      id: projectId,
      name: project.name,
      description: project.description,
      videoClips,
      audioClips,
      subtitleClips,
      timeline,
      project,
      savedAt: new Date().toISOString()
    }
    onSave?.(projectData)
  }, [projectId, project, videoClips, audioClips, subtitleClips, timeline, onSave])

  // 导出处理
  const handleExportComplete = useCallback((videoData: Uint8Array, filename: string) => {
    onExport?.(videoData, filename)
  }, [onExport])

  return (
    <DragDropProvider>
      <div className={`video-editor h-full flex flex-col bg-gray-50 ${className}`}>
        {/* 顶部工具栏 */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <Video className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-gray-800">{project.name}</h1>
                  <p className="text-xs text-gray-500">AI视频编辑器</p>
                </div>
              </div>

              <Separator orientation="vertical" className="h-8" />

              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="text-xs">
                  {videoClips.length} 视频
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {audioClips.length} 音频
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {subtitleClips.length} 字幕
                </Badge>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleSave}>
                <Save className="w-4 h-4 mr-1" />
                保存
              </Button>
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-1" />
                预览
              </Button>
              <Button size="sm" className="bg-green-500 hover:bg-green-600">
                <Download className="w-4 h-4 mr-1" />
                导出
              </Button>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 flex min-h-0">
          {/* 左侧面板 */}
          <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
            <Tabs value={activePanel} onValueChange={(value: any) => setActivePanel(value)} className="flex-1 flex flex-col">
              <TabsList className="grid w-full grid-cols-5 m-2">
                <TabsTrigger value="media" className="text-xs">
                  <Video className="w-3 h-3 mr-1" />
                  媒体
                </TabsTrigger>
                <TabsTrigger value="audio" className="text-xs">
                  <Music className="w-3 h-3 mr-1" />
                  音频
                </TabsTrigger>
                <TabsTrigger value="subtitle" className="text-xs">
                  <Type className="w-3 h-3 mr-1" />
                  字幕
                </TabsTrigger>
                <TabsTrigger value="effects" className="text-xs">
                  <Sparkles className="w-3 h-3 mr-1" />
                  特效
                </TabsTrigger>
                <TabsTrigger value="export" className="text-xs">
                  <Download className="w-3 h-3 mr-1" />
                  导出
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden">
                <TabsContent value="media" className="h-full p-4 overflow-y-auto">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-semibold">视频素材</h3>
                      <Button size="sm" variant="outline">
                        <Upload className="w-3 h-3 mr-1" />
                        上传
                      </Button>
                    </div>
                    
                    <div className="space-y-2">
                      {videoClips.map((clip) => (
                        <div
                          key={clip.id}
                          className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                            selectedClipId === clip.id
                              ? 'border-blue-300 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => handleClipSelect(clip.id)}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-8 bg-gray-200 rounded overflow-hidden">
                              {clip.thumbnail && (
                                <img 
                                  src={clip.thumbnail} 
                                  alt={clip.name}
                                  className="w-full h-full object-cover"
                                />
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">{clip.name}</p>
                              <p className="text-xs text-gray-500">{Math.floor(clip.duration)}s</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="audio" className="h-full p-0">
                  <AudioEditor
                    selectedClip={selectedClip && 'audioUrl' in selectedClip ? selectedClip as AudioClip : undefined}
                    onClipUpdate={handleClipUpdate}
                    onClipDelete={handleClipDelete}
                    onClipDuplicate={handleClipDuplicate}
                  />
                </TabsContent>

                <TabsContent value="subtitle" className="h-full p-0">
                  <SubtitleEditor
                    selectedClip={selectedClip && 'text' in selectedClip ? selectedClip as SubtitleClip : undefined}
                    onClipUpdate={handleClipUpdate}
                    onClipDelete={handleClipDelete}
                    onClipDuplicate={handleClipDuplicate}
                    onAddClip={(clip) => addSubtitleClip({ ...clip, id: `subtitle-${Date.now()}` })}
                  />
                </TabsContent>

                <TabsContent value="effects" className="h-full p-4">
                  <div className="text-center text-gray-500">
                    <Sparkles className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">特效功能开发中...</p>
                  </div>
                </TabsContent>

                <TabsContent value="export" className="h-full p-0">
                  <VideoExporter
                    onExportComplete={handleExportComplete}
                    onError={(error) => console.error('导出错误:', error)}
                  />
                </TabsContent>
              </div>
            </Tabs>
          </div>

          {/* 中央内容区域 */}
          <div className="flex-1 flex flex-col min-w-0">
            {/* 预览区域 */}
            <div className="flex-1 p-6 pb-0">
              <Card className="h-full">
                <CardContent className="p-0 h-full relative">
                  <div className="w-full h-full bg-black rounded-lg flex items-center justify-center relative overflow-hidden">
                    {/* 视频预览区域 */}
                    <div className="text-white text-center">
                      <div className="text-6xl mb-4">🎬</div>
                      <p className="text-xl mb-2">视频预览</p>
                      <p className="text-sm opacity-70">
                        时间: {Math.floor(previewTime / 60)}:{Math.floor(previewTime % 60).toString().padStart(2, '0')}
                      </p>
                    </div>

                    {/* 字幕渲染层 */}
                    <SubtitleRenderer
                      subtitles={subtitleClips}
                      currentTime={previewTime}
                      videoWidth={1920}
                      videoHeight={1080}
                    />

                    {/* 播放控制覆盖层 */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={handlePlayPause}
                        className="bg-black/50 hover:bg-black/70 text-white border-white/20"
                      >
                        {isPreviewPlaying ? (
                          <Pause className="w-4 h-4" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 时间轴区域 */}
            <div className="p-6 pt-0">
              <Timeline
                onClipSelect={handleClipSelect}
                onTimeChange={handleTimeChange}
              />
            </div>
          </div>

          {/* 右侧属性面板 */}
          <div className="w-80 bg-white border-l border-gray-200 p-4 overflow-y-auto">
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-semibold mb-3">项目信息</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">分辨率:</span>
                    <span>{project.resolution.width}x{project.resolution.height}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">帧率:</span>
                    <span>{project.frameRate} FPS</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">总时长:</span>
                    <span>{Math.floor(timeline.duration / 60)}:{Math.floor(timeline.duration % 60).toString().padStart(2, '0')}</span>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-semibold mb-3">选中片段</h3>
                {selectedClip ? (
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">名称:</span>
                      <span className="truncate ml-2">{selectedClip.name || '未命名'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">轨道:</span>
                      <span>{selectedClip.track}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">开始:</span>
                      <span>{selectedClip.startTime.toFixed(1)}s</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">结束:</span>
                      <span>{selectedClip.endTime.toFixed(1)}s</span>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">未选择片段</p>
                )}
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-semibold mb-3">快捷操作</h3>
                <div className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Scissors className="w-3 h-3 mr-2" />
                    分割片段
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Layers className="w-3 h-3 mr-2" />
                    调整层级
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start">
                    <Settings className="w-3 h-3 mr-2" />
                    片段设置
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DragDropProvider>
  )
}
