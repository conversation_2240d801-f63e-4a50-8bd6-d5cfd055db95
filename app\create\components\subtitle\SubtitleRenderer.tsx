"use client"

import React, { useMemo } from 'react'
import { SubtitleClip } from '../../stores'

interface SubtitleRendererProps {
  subtitles: SubtitleClip[]
  currentTime: number
  videoWidth?: number
  videoHeight?: number
  className?: string
  style?: React.CSSProperties
}

export const SubtitleRenderer: React.FC<SubtitleRendererProps> = ({
  subtitles,
  currentTime,
  videoWidth = 1920,
  videoHeight = 1080,
  className = '',
  style = {}
}) => {
  // 获取当前时间应该显示的字幕
  const activeSubtitles = useMemo(() => {
    return subtitles.filter(subtitle => 
      currentTime >= subtitle.startTime && currentTime <= subtitle.endTime
    ).sort((a, b) => a.track - b.track) // 按轨道排序
  }, [subtitles, currentTime])

  // 渲染单个字幕
  const renderSubtitle = (subtitle: SubtitleClip, index: number) => {
    const {
      text,
      style: subtitleStyle,
      position,
      track
    } = subtitle

    // 计算位置
    const left = `${position.x}%`
    const top = `${position.y}%`

    // 构建样式
    const textStyle: React.CSSProperties = {
      position: 'absolute',
      left,
      top,
      transform: 'translate(-50%, -50%)',
      fontFamily: subtitleStyle.fontFamily || 'Arial',
      fontSize: `${(subtitleStyle.fontSize || 24) * (videoWidth / 1920)}px`, // 响应式字体大小
      color: subtitleStyle.color || '#ffffff',
      fontWeight: subtitleStyle.bold ? 'bold' : 'normal',
      fontStyle: subtitleStyle.italic ? 'italic' : 'normal',
      textDecoration: subtitleStyle.underline ? 'underline' : 'none',
      textAlign: 'center',
      whiteSpace: 'pre-line',
      wordWrap: 'break-word',
      maxWidth: '80%',
      zIndex: 1000 + track, // 轨道越高，层级越高
      pointerEvents: 'none',
      userSelect: 'none',
      
      // 文字阴影效果
      textShadow: subtitleStyle.shadow 
        ? '2px 2px 4px rgba(0, 0, 0, 0.8), -1px -1px 2px rgba(0, 0, 0, 0.8), 1px -1px 2px rgba(0, 0, 0, 0.8), -1px 1px 2px rgba(0, 0, 0, 0.8)'
        : 'none',
      
      // 背景颜色
      backgroundColor: subtitleStyle.backgroundColor !== 'transparent' 
        ? subtitleStyle.backgroundColor 
        : 'transparent',
      
      // 如果有背景色，添加内边距
      padding: subtitleStyle.backgroundColor !== 'transparent' ? '4px 8px' : '0',
      borderRadius: subtitleStyle.backgroundColor !== 'transparent' ? '4px' : '0',
      
      // 动画效果
      animation: 'subtitleFadeIn 0.3s ease-in-out',
    }

    return (
      <div
        key={`${subtitle.id}-${index}`}
        style={textStyle}
        className="subtitle-text"
      >
        {text}
      </div>
    )
  }

  if (activeSubtitles.length === 0) {
    return null
  }

  return (
    <div
      className={`subtitle-renderer ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        overflow: 'hidden',
        ...style
      }}
    >
      {activeSubtitles.map((subtitle, index) => renderSubtitle(subtitle, index))}
      
      {/* CSS 动画定义 */}
      <style jsx>{`
        @keyframes subtitleFadeIn {
          from {
            opacity: 0;
            transform: translate(-50%, -50%) translateY(10px);
          }
          to {
            opacity: 1;
            transform: translate(-50%, -50%) translateY(0);
          }
        }
        
        .subtitle-text {
          line-height: 1.2;
          letter-spacing: 0.5px;
        }
        
        /* 响应式字体大小 */
        @media (max-width: 768px) {
          .subtitle-text {
            font-size: 16px !important;
            max-width: 90%;
          }
        }
        
        @media (max-width: 480px) {
          .subtitle-text {
            font-size: 14px !important;
            max-width: 95%;
          }
        }
      `}</style>
    </div>
  )
}

// 字幕预览组件（用于编辑器中的预览）
export const SubtitlePreview: React.FC<{
  subtitle: SubtitleClip
  videoWidth?: number
  videoHeight?: number
  className?: string
}> = ({
  subtitle,
  videoWidth = 1920,
  videoHeight = 1080,
  className = ''
}) => {
  const previewStyle: React.CSSProperties = {
    position: 'relative',
    width: '100%',
    height: '200px',
    backgroundColor: '#000000',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    borderRadius: '8px'
  }

  return (
    <div className={`subtitle-preview ${className}`} style={previewStyle}>
      <SubtitleRenderer
        subtitles={[subtitle]}
        currentTime={subtitle.startTime + (subtitle.endTime - subtitle.startTime) / 2}
        videoWidth={videoWidth}
        videoHeight={videoHeight}
      />
      
      {/* 预览背景网格 */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px',
          pointerEvents: 'none',
          zIndex: 0
        }}
      />
      
      {/* 中心十字线 */}
      <div
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          width: '20px',
          height: '20px',
          transform: 'translate(-50%, -50%)',
          border: '1px solid rgba(255,255,255,0.3)',
          pointerEvents: 'none',
          zIndex: 0
        }}
      >
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '-10px',
            right: '-10px',
            height: '1px',
            backgroundColor: 'rgba(255,255,255,0.3)'
          }}
        />
        <div
          style={{
            position: 'absolute',
            left: '50%',
            top: '-10px',
            bottom: '-10px',
            width: '1px',
            backgroundColor: 'rgba(255,255,255,0.3)'
          }}
        />
      </div>
    </div>
  )
}

// 字幕轨道渲染组件（用于时间轴）
export const SubtitleTrackRenderer: React.FC<{
  subtitle: SubtitleClip
  x: number
  y: number
  width: number
  height: number
  isSelected: boolean
  onSelect?: (id: string) => void
}> = ({
  subtitle,
  x,
  y,
  width,
  height,
  isSelected,
  onSelect
}) => {
  const trackStyle: React.CSSProperties = {
    position: 'absolute',
    left: x,
    top: y,
    width,
    height,
    backgroundColor: isSelected ? '#8b5cf6' : '#6366f1',
    border: `2px solid ${isSelected ? '#7c3aed' : '#4f46e5'}`,
    borderRadius: '4px',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontSize: '12px',
    fontWeight: 'medium',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    padding: '0 8px',
    boxShadow: isSelected ? '0 2px 8px rgba(139, 92, 246, 0.3)' : 'none',
    transition: 'all 0.2s ease-in-out'
  }

  const handleClick = () => {
    onSelect?.(subtitle.id)
  }

  return (
    <div
      style={trackStyle}
      onClick={handleClick}
      title={`${subtitle.text} (${subtitle.startTime.toFixed(1)}s - ${subtitle.endTime.toFixed(1)}s)`}
    >
      {subtitle.text.length > 20 ? subtitle.text.substring(0, 20) + '...' : subtitle.text}
    </div>
  )
}
