{"name": "ai-drama", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:env": "node scripts/test-env.js", "test:kling": "node scripts/test-kling-tasks.js", "test:new-api": "node scripts/test-new-api-format.js", "test:list-fix": "node scripts/test-list-query-fix.js", "debug:kling": "node scripts/debug-kling-response.js", "kling:query": "node scripts/query-single-task.js", "kling:curl": "node scripts/generate-curl.js", "kling:list": "node scripts/kling-task-manager.js --list", "kling:interactive": "node scripts/kling-task-manager.js --interactive", "kling:succeed": "node scripts/kling-task-manager.js --list --status succeed", "kling:failed": "node scripts/kling-task-manager.js --list --status failed"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.16", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.52.0", "ai": "^4.3.19", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "fabric": "^6.7.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "konva": "^9.3.22", "lucide-react": "^0.525.0", "minio": "^8.0.5", "next": "15.4.1", "next-themes": "^0.4.6", "prisma": "^6.12.0", "react": "19.1.0", "react-day-picker": "^9.8.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-konva": "^19.0.7", "react-resizable-panels": "^3.0.3", "recharts": "^3.1.0", "sonner": "^2.0.6", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "wavesurfer.js": "^7.10.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/fabric": "^5.3.10", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/wavesurfer.js": "^6.0.12", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}