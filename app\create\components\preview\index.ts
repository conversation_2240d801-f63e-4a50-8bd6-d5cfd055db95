// 预览组件统一导出

export { MultiTrackPreview } from './MultiTrackPreview'

// 预览相关工具函数
export const previewUtils = {
  // 计算视频适配尺寸
  calculateVideoFit: (
    videoWidth: number,
    videoHeight: number,
    containerWidth: number,
    containerHeight: number,
    mode: 'contain' | 'cover' | 'fill' = 'contain'
  ): { width: number; height: number; x: number; y: number } => {
    const videoAspect = videoWidth / videoHeight
    const containerAspect = containerWidth / containerHeight

    let width: number, height: number, x: number, y: number

    switch (mode) {
      case 'contain':
        if (videoAspect > containerAspect) {
          // 视频更宽，以容器宽度为准
          width = containerWidth
          height = containerWidth / videoAspect
          x = 0
          y = (containerHeight - height) / 2
        } else {
          // 视频更高，以容器高度为准
          width = containerHeight * videoAspect
          height = containerHeight
          x = (containerWidth - width) / 2
          y = 0
        }
        break

      case 'cover':
        if (videoAspect > containerAspect) {
          // 视频更宽，以容器高度为准
          width = containerHeight * videoAspect
          height = containerHeight
          x = (containerWidth - width) / 2
          y = 0
        } else {
          // 视频更高，以容器宽度为准
          width = containerWidth
          height = containerWidth / videoAspect
          x = 0
          y = (containerHeight - height) / 2
        }
        break

      case 'fill':
        width = containerWidth
        height = containerHeight
        x = 0
        y = 0
        break

      default:
        throw new Error(`Unsupported fit mode: ${mode}`)
    }

    return { width, height, x, y }
  },

  // 创建视频缩略图
  createVideoThumbnail: (
    video: HTMLVideoElement,
    time: number = 0,
    width: number = 320,
    height: number = 240
  ): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) {
        reject(new Error('无法创建 Canvas 上下文'))
        return
      }

      canvas.width = width
      canvas.height = height

      const handleSeeked = () => {
        try {
          const fit = previewUtils.calculateVideoFit(
            video.videoWidth,
            video.videoHeight,
            width,
            height,
            'contain'
          )

          // 清空画布
          ctx.fillStyle = '#000000'
          ctx.fillRect(0, 0, width, height)

          // 绘制视频帧
          ctx.drawImage(video, fit.x, fit.y, fit.width, fit.height)

          // 转换为 Data URL
          const dataUrl = canvas.toDataURL('image/jpeg', 0.8)
          resolve(dataUrl)
        } catch (error) {
          reject(error)
        } finally {
          video.removeEventListener('seeked', handleSeeked)
        }
      }

      video.addEventListener('seeked', handleSeeked)
      video.currentTime = time
    })
  },

  // 混合多个音频源
  mixAudioSources: (
    audioElements: HTMLAudioElement[],
    volumes: number[] = [],
    masterVolume: number = 1
  ): void => {
    audioElements.forEach((audio, index) => {
      const volume = volumes[index] !== undefined ? volumes[index] : 1
      audio.volume = Math.max(0, Math.min(1, volume * masterVolume))
    })
  },

  // 同步多个媒体元素的播放时间
  syncMediaElements: (
    elements: (HTMLVideoElement | HTMLAudioElement)[],
    targetTime: number,
    tolerance: number = 0.1
  ): void => {
    elements.forEach(element => {
      if (Math.abs(element.currentTime - targetTime) > tolerance) {
        element.currentTime = targetTime
      }
    })
  },

  // 检查媒体元素是否准备就绪
  checkMediaReady: (elements: (HTMLVideoElement | HTMLAudioElement)[]): boolean => {
    return elements.every(element => element.readyState >= 2) // HAVE_CURRENT_DATA
  },

  // 预加载媒体元素
  preloadMedia: (
    urls: string[],
    type: 'video' | 'audio' = 'video'
  ): Promise<(HTMLVideoElement | HTMLAudioElement)[]> => {
    const promises = urls.map(url => {
      return new Promise<HTMLVideoElement | HTMLAudioElement>((resolve, reject) => {
        const element = type === 'video' 
          ? document.createElement('video')
          : document.createElement('audio')

        element.preload = 'metadata'
        element.crossOrigin = 'anonymous'

        element.addEventListener('loadedmetadata', () => resolve(element))
        element.addEventListener('error', () => reject(new Error(`Failed to load ${type}: ${url}`)))

        element.src = url
      })
    })

    return Promise.all(promises)
  },

  // 计算帧率
  calculateFrameRate: (
    frameCount: number,
    duration: number
  ): number => {
    return frameCount / duration
  },

  // 时间码转换
  timeToTimecode: (
    seconds: number,
    frameRate: number = 30,
    dropFrame: boolean = false
  ): string => {
    const totalFrames = Math.round(seconds * frameRate)
    
    let hours = Math.floor(totalFrames / (frameRate * 3600))
    let minutes = Math.floor((totalFrames % (frameRate * 3600)) / (frameRate * 60))
    let secs = Math.floor((totalFrames % (frameRate * 60)) / frameRate)
    let frames = totalFrames % frameRate

    // Drop frame 计算（仅适用于 29.97fps）
    if (dropFrame && Math.abs(frameRate - 29.97) < 0.1) {
      const dropFrames = Math.round(frameRate * 0.066666) // 2 frames per minute
      const totalMinutes = hours * 60 + minutes
      const droppedFrames = totalMinutes * dropFrames - Math.floor(totalMinutes / 10) * dropFrames
      
      const adjustedFrames = totalFrames - droppedFrames
      hours = Math.floor(adjustedFrames / (frameRate * 3600))
      minutes = Math.floor((adjustedFrames % (frameRate * 3600)) / (frameRate * 60))
      secs = Math.floor((adjustedFrames % (frameRate * 60)) / frameRate)
      frames = adjustedFrames % frameRate
    }

    const separator = dropFrame ? ';' : ':'
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}${separator}${Math.floor(frames).toString().padStart(2, '0')}`
  },

  // 时间码转秒数
  timecodeToTime: (
    timecode: string,
    frameRate: number = 30
  ): number => {
    const parts = timecode.split(/[:;]/)
    if (parts.length !== 4) {
      throw new Error('Invalid timecode format')
    }

    const hours = parseInt(parts[0])
    const minutes = parseInt(parts[1])
    const seconds = parseInt(parts[2])
    const frames = parseInt(parts[3])

    return hours * 3600 + minutes * 60 + seconds + frames / frameRate
  },

  // 性能监控
  performanceMonitor: {
    frameCount: 0,
    lastTime: 0,
    fps: 0,

    update: (): number => {
      const now = performance.now()
      previewUtils.performanceMonitor.frameCount++

      if (now - previewUtils.performanceMonitor.lastTime >= 1000) {
        previewUtils.performanceMonitor.fps = 
          previewUtils.performanceMonitor.frameCount * 1000 / 
          (now - previewUtils.performanceMonitor.lastTime)
        
        previewUtils.performanceMonitor.frameCount = 0
        previewUtils.performanceMonitor.lastTime = now
      }

      return previewUtils.performanceMonitor.fps
    },

    reset: (): void => {
      previewUtils.performanceMonitor.frameCount = 0
      previewUtils.performanceMonitor.lastTime = performance.now()
      previewUtils.performanceMonitor.fps = 0
    }
  },

  // 内存使用监控
  getMemoryUsage: (): { used: number; total: number; percentage: number } | null => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
      }
    }
    return null
  },

  // 清理媒体资源
  cleanupMediaElements: (elements: (HTMLVideoElement | HTMLAudioElement)[]): void => {
    elements.forEach(element => {
      element.pause()
      element.src = ''
      element.load()
      element.remove()
    })
  }
}
