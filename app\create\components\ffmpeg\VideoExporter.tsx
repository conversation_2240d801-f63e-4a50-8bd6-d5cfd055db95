"use client"

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Switch } from "@/components/ui/switch"
import {
  Download,
  Video,
  Settings,
  FileVideo,
  Loader2,
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  Eye
} from "lucide-react"
import { useVideoEditor } from '../../stores'
import { ffmpegProcessor } from './FFmpegProcessor'
import { SubtitleParser } from '../subtitle'

interface ExportSettings {
  format: string
  quality: 'low' | 'medium' | 'high' | 'ultra'
  resolution: { width: number; height: number }
  fps: number
  bitrate?: string
  includeAudio: boolean
  includeSubtitles: boolean
  subtitleStyle: {
    fontFamily: string
    fontSize: number
    color: string
    backgroundColor: string
  }
}

interface VideoExporterProps {
  onExportComplete?: (result: Uint8Array, filename: string) => void
  onError?: (error: string) => void
  className?: string
}

export const VideoExporter: React.FC<VideoExporterProps> = ({
  onExportComplete,
  onError,
  className = ''
}) => {
  const {
    videoClips,
    audioClips,
    subtitleClips,
    timeline,
    project
  } = useVideoEditor()

  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState('')
  const [exportSettings, setExportSettings] = useState<ExportSettings>({
    format: 'mp4',
    quality: 'medium',
    resolution: { width: 1920, height: 1080 },
    fps: 30,
    includeAudio: true,
    includeSubtitles: true,
    subtitleStyle: {
      fontFamily: 'Arial',
      fontSize: 24,
      color: '#ffffff',
      backgroundColor: 'transparent'
    }
  })

  // 预设分辨率选项
  const resolutionPresets = [
    { name: '4K (3840x2160)', width: 3840, height: 2160 },
    { name: 'Full HD (1920x1080)', width: 1920, height: 1080 },
    { name: 'HD (1280x720)', width: 1280, height: 720 },
    { name: 'SD (854x480)', width: 854, height: 480 },
    { name: '竖屏 (1080x1920)', width: 1080, height: 1920 },
    { name: '方形 (1080x1080)', width: 1080, height: 1080 }
  ]

  // 质量设置映射
  const qualitySettings = {
    low: { crf: 28, bitrate: '1M' },
    medium: { crf: 23, bitrate: '3M' },
    high: { crf: 18, bitrate: '8M' },
    ultra: { crf: 15, bitrate: '15M' }
  }

  // 计算导出时长
  const calculateExportDuration = useCallback(() => {
    const videoMaxTime = Math.max(0, ...videoClips.map(c => c.endTime))
    const audioMaxTime = Math.max(0, ...audioClips.map(c => c.endTime))
    const subtitleMaxTime = Math.max(0, ...subtitleClips.map(c => c.endTime))
    return Math.max(videoMaxTime, audioMaxTime, subtitleMaxTime, timeline.duration)
  }, [videoClips, audioClips, subtitleClips, timeline.duration])

  // 生成字幕文件内容
  const generateSubtitleContent = useCallback(() => {
    if (subtitleClips.length === 0) return null

    const entries = subtitleClips.map(clip => ({
      id: clip.id,
      startTime: clip.startTime,
      endTime: clip.endTime,
      text: clip.text,
      style: clip.style,
      position: clip.position
    }))

    return SubtitleParser.exportToSRT(entries)
  }, [subtitleClips])

  // 执行导出
  const handleExport = useCallback(async () => {
    if (videoClips.length === 0) {
      onError?.('没有视频片段可导出')
      return
    }

    try {
      setIsExporting(true)
      setExportProgress(0)
      setCurrentStep('准备导出...')

      // 初始化 FFmpeg
      if (!ffmpegProcessor.isReady()) {
        setCurrentStep('初始化 FFmpeg...')
        await ffmpegProcessor.initialize()
      }

      setExportProgress(10)
      setCurrentStep('处理视频片段...')

      // 这里需要实现复杂的视频合成逻辑
      // 由于 FFmpeg.wasm 的限制，这是一个简化的实现
      
      // 1. 处理第一个视频片段作为基础
      const firstClip = videoClips[0]
      if (!firstClip.videoUrl) {
        throw new Error('视频片段缺少视频文件')
      }

      setExportProgress(30)
      setCurrentStep('应用视频设置...')

      // 获取视频文件
      const videoResponse = await fetch(firstClip.videoUrl)
      const videoBlob = await videoResponse.blob()
      const videoFile = new File([videoBlob], 'video.mp4', { type: 'video/mp4' })

      setExportProgress(50)

      // 应用基本视频处理
      let processedVideo = await ffmpegProcessor.convertVideo(videoFile, exportSettings.format, {
        width: exportSettings.resolution.width,
        height: exportSettings.resolution.height,
        fps: exportSettings.fps,
        quality: exportSettings.quality,
        startTime: firstClip.startTime,
        duration: firstClip.endTime - firstClip.startTime
      })

      setExportProgress(70)

      // 如果需要添加字幕
      if (exportSettings.includeSubtitles && subtitleClips.length > 0) {
        setCurrentStep('添加字幕...')
        const subtitleContent = generateSubtitleContent()
        if (subtitleContent) {
          processedVideo = await ffmpegProcessor.addSubtitlesToVideo(
            processedVideo,
            subtitleContent,
            {
              fontFamily: exportSettings.subtitleStyle.fontFamily,
              fontSize: exportSettings.subtitleStyle.fontSize,
              color: exportSettings.subtitleStyle.color,
              backgroundColor: exportSettings.subtitleStyle.backgroundColor
            },
            exportSettings.format
          )
        }
      }

      setExportProgress(90)
      setCurrentStep('完成导出...')

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const filename = `${project.name || 'video'}_${timestamp}.${exportSettings.format}`

      setExportProgress(100)
      setCurrentStep('导出完成')

      onExportComplete?.(processedVideo, filename)

    } catch (error) {
      console.error('视频导出失败:', error)
      onError?.('视频导出失败: ' + (error as Error).message)
    } finally {
      setIsExporting(false)
    }
  }, [
    videoClips,
    subtitleClips,
    exportSettings,
    project.name,
    generateSubtitleContent,
    onExportComplete,
    onError
  ])

  // 预览导出设置
  const handlePreview = useCallback(() => {
    // 这里可以实现预览功能
    console.log('预览导出设置:', exportSettings)
  }, [exportSettings])

  // 下载导出的文件
  const downloadFile = useCallback((data: Uint8Array, filename: string) => {
    const blob = new Blob([data])
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }, [])

  const exportDuration = calculateExportDuration()
  const estimatedFileSize = (exportDuration * parseInt(qualitySettings[exportSettings.quality].bitrate) * 1024) / 8 // 估算文件大小（字节）

  return (
    <div className={`video-exporter ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Download className="w-5 h-5 text-green-600" />
            <span>视频导出</span>
            <Badge variant="secondary" className="text-xs">
              {videoClips.length} 个片段
            </Badge>
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* 项目信息 */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-semibold mb-2">项目信息</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">项目名称:</span>
                <span className="ml-2 font-medium">{project.name}</span>
              </div>
              <div>
                <span className="text-gray-600">总时长:</span>
                <span className="ml-2 font-medium">{Math.floor(exportDuration / 60)}:{Math.floor(exportDuration % 60).toString().padStart(2, '0')}</span>
              </div>
              <div>
                <span className="text-gray-600">视频片段:</span>
                <span className="ml-2 font-medium">{videoClips.length} 个</span>
              </div>
              <div>
                <span className="text-gray-600">音频片段:</span>
                <span className="ml-2 font-medium">{audioClips.length} 个</span>
              </div>
              <div>
                <span className="text-gray-600">字幕片段:</span>
                <span className="ml-2 font-medium">{subtitleClips.length} 个</span>
              </div>
              <div>
                <span className="text-gray-600">预计大小:</span>
                <span className="ml-2 font-medium">{(estimatedFileSize / 1024 / 1024).toFixed(1)} MB</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* 导出设置 */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">导出设置</h3>

            {/* 基本设置 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-xs">输出格式</Label>
                <Select
                  value={exportSettings.format}
                  onValueChange={(value) => setExportSettings(prev => ({ ...prev, format: value }))}
                >
                  <SelectTrigger className="text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mp4">MP4 (推荐)</SelectItem>
                    <SelectItem value="webm">WebM</SelectItem>
                    <SelectItem value="avi">AVI</SelectItem>
                    <SelectItem value="mov">MOV</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-xs">质量</Label>
                <Select
                  value={exportSettings.quality}
                  onValueChange={(value: any) => setExportSettings(prev => ({ ...prev, quality: value }))}
                >
                  <SelectTrigger className="text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">低质量 (快速)</SelectItem>
                    <SelectItem value="medium">中等质量</SelectItem>
                    <SelectItem value="high">高质量</SelectItem>
                    <SelectItem value="ultra">超高质量 (慢)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 分辨率设置 */}
            <div>
              <Label className="text-xs mb-2 block">分辨率</Label>
              <div className="grid grid-cols-2 gap-2 mb-2">
                {resolutionPresets.map((preset) => (
                  <Button
                    key={preset.name}
                    variant={
                      exportSettings.resolution.width === preset.width &&
                      exportSettings.resolution.height === preset.height
                        ? "default" : "outline"
                    }
                    size="sm"
                    className="text-xs"
                    onClick={() => setExportSettings(prev => ({
                      ...prev,
                      resolution: { width: preset.width, height: preset.height }
                    }))}
                  >
                    {preset.name}
                  </Button>
                ))}
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Input
                  type="number"
                  placeholder="宽度"
                  value={exportSettings.resolution.width}
                  onChange={(e) => setExportSettings(prev => ({
                    ...prev,
                    resolution: { ...prev.resolution, width: parseInt(e.target.value) || 1920 }
                  }))}
                  className="text-sm"
                />
                <Input
                  type="number"
                  placeholder="高度"
                  value={exportSettings.resolution.height}
                  onChange={(e) => setExportSettings(prev => ({
                    ...prev,
                    resolution: { ...prev.resolution, height: parseInt(e.target.value) || 1080 }
                  }))}
                  className="text-sm"
                />
              </div>
            </div>

            {/* 帧率设置 */}
            <div>
              <Label className="text-xs">帧率 (FPS)</Label>
              <Select
                value={exportSettings.fps.toString()}
                onValueChange={(value) => setExportSettings(prev => ({ ...prev, fps: parseInt(value) }))}
              >
                <SelectTrigger className="text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="24">24 FPS (电影)</SelectItem>
                  <SelectItem value="30">30 FPS (标准)</SelectItem>
                  <SelectItem value="60">60 FPS (高帧率)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 包含选项 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm">包含音频</Label>
                <Switch
                  checked={exportSettings.includeAudio}
                  onCheckedChange={(checked) => setExportSettings(prev => ({ ...prev, includeAudio: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-sm">包含字幕</Label>
                <Switch
                  checked={exportSettings.includeSubtitles}
                  onCheckedChange={(checked) => setExportSettings(prev => ({ ...prev, includeSubtitles: checked }))}
                />
              </div>
            </div>

            {/* 字幕样式设置 */}
            {exportSettings.includeSubtitles && subtitleClips.length > 0 && (
              <div className="space-y-3 p-3 bg-purple-50 rounded-lg">
                <Label className="text-sm font-medium">字幕样式</Label>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label className="text-xs">字体</Label>
                    <Select
                      value={exportSettings.subtitleStyle.fontFamily}
                      onValueChange={(value) => setExportSettings(prev => ({
                        ...prev,
                        subtitleStyle: { ...prev.subtitleStyle, fontFamily: value }
                      }))}
                    >
                      <SelectTrigger className="text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Arial">Arial</SelectItem>
                        <SelectItem value="Helvetica">Helvetica</SelectItem>
                        <SelectItem value="SimHei">黑体</SelectItem>
                        <SelectItem value="Microsoft YaHei">微软雅黑</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="text-xs">字号</Label>
                    <Input
                      type="number"
                      value={exportSettings.subtitleStyle.fontSize}
                      onChange={(e) => setExportSettings(prev => ({
                        ...prev,
                        subtitleStyle: { ...prev.subtitleStyle, fontSize: parseInt(e.target.value) || 24 }
                      }))}
                      className="text-sm"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* 导出进度 */}
          {isExporting && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">导出进度</span>
                <span className="text-sm text-gray-600">{Math.round(exportProgress)}%</span>
              </div>
              <Progress value={exportProgress} className="w-full" />
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>{currentStep}</span>
              </div>
            </div>
          )}

          {/* 警告信息 */}
          {videoClips.length === 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                没有视频片段可导出，请先添加视频内容到时间轴
              </AlertDescription>
            </Alert>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={handlePreview}
              disabled={isExporting || videoClips.length === 0}
            >
              <Eye className="w-4 h-4 mr-2" />
              预览设置
            </Button>

            <Button
              onClick={handleExport}
              disabled={isExporting || videoClips.length === 0}
              className="bg-green-500 hover:bg-green-600"
            >
              {isExporting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  导出中...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  开始导出
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
