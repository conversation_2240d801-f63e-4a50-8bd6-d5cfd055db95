"use client"

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Volume2,
  VolumeX,
  Scissors,
  Copy,
  Trash2,
  Upload,
  Download,
  Settings,
  Waveform,
  Music,
  Headphones
} from "lucide-react"
import { WaveformVisualization } from './WaveformVisualization'
import { useVideoEditor, AudioClip } from '../../stores'

interface AudioEditorProps {
  selectedClip?: AudioClip
  onClipUpdate?: (clipId: string, updates: Partial<AudioClip>) => void
  onClipDelete?: (clipId: string) => void
  onClipDuplicate?: (clipId: string) => void
  className?: string
}

export const AudioEditor: React.FC<AudioEditorProps> = ({
  selectedClip,
  onClipUpdate,
  onClipDelete,
  onClipDuplicate,
  className = ''
}) => {
  const { audioClips, ui } = useVideoEditor()
  const [showAdvanced, setShowAdvanced] = useState(false)

  // 音量调整
  const handleVolumeChange = useCallback((volume: number[]) => {
    if (selectedClip && onClipUpdate) {
      onClipUpdate(selectedClip.id, { volume: volume[0] })
    }
  }, [selectedClip, onClipUpdate])

  // 静音切换
  const handleMuteToggle = useCallback(() => {
    if (selectedClip && onClipUpdate) {
      const newVolume = selectedClip.volume === 0 ? 80 : 0
      onClipUpdate(selectedClip.id, { volume: newVolume })
    }
  }, [selectedClip, onClipUpdate])

  // 时间调整
  const handleTimeChange = useCallback((field: 'startTime' | 'endTime', value: number) => {
    if (selectedClip && onClipUpdate) {
      const updates: Partial<AudioClip> = { [field]: value }
      
      // 确保时间逻辑正确
      if (field === 'startTime' && value >= selectedClip.endTime) {
        updates.endTime = value + 1
      } else if (field === 'endTime' && value <= selectedClip.startTime) {
        updates.startTime = value - 1
      }
      
      onClipUpdate(selectedClip.id, updates)
    }
  }, [selectedClip, onClipUpdate])

  // 轨道调整
  const handleTrackChange = useCallback((track: number) => {
    if (selectedClip && onClipUpdate) {
      onClipUpdate(selectedClip.id, { track })
    }
  }, [selectedClip, onClipUpdate])

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
  }

  return (
    <div className={`audio-editor ${className}`}>
      {selectedClip ? (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center space-x-2">
                <Music className="w-5 h-5 text-blue-600" />
                <span>音频编辑器</span>
              </CardTitle>
              <Badge variant="secondary" className="text-xs">
                {selectedClip.name}
              </Badge>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* 波形可视化 */}
            <div>
              <Label className="text-sm font-medium mb-2 block">音频波形</Label>
              <WaveformVisualization
                audioUrl={selectedClip.audioUrl}
                height={120}
                waveColor="#6366f1"
                progressColor="#3b82f6"
                responsive={true}
                className="border rounded-lg"
              />
            </div>

            <Separator />

            {/* 基本控制 */}
            <div className="space-y-4">
              <h3 className="text-sm font-semibold text-gray-700">基本控制</h3>
              
              {/* 音量控制 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm">音量</Label>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleMuteToggle}
                      className="w-8 h-8 p-0"
                    >
                      {selectedClip.volume === 0 ? (
                        <VolumeX className="w-4 h-4" />
                      ) : (
                        <Volume2 className="w-4 h-4" />
                      )}
                    </Button>
                    <span className="text-sm text-gray-600 min-w-[40px]">
                      {selectedClip.volume}%
                    </span>
                  </div>
                </div>
                <Slider
                  value={[selectedClip.volume]}
                  onValueChange={handleVolumeChange}
                  max={100}
                  step={1}
                  className="w-full"
                />
              </div>

              {/* 时间控制 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm">开始时间</Label>
                  <div className="mt-1">
                    <input
                      type="number"
                      value={selectedClip.startTime.toFixed(3)}
                      onChange={(e) => handleTimeChange('startTime', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      step="0.001"
                      min="0"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {formatTime(selectedClip.startTime)}
                    </p>
                  </div>
                </div>
                
                <div>
                  <Label className="text-sm">结束时间</Label>
                  <div className="mt-1">
                    <input
                      type="number"
                      value={selectedClip.endTime.toFixed(3)}
                      onChange={(e) => handleTimeChange('endTime', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      step="0.001"
                      min={selectedClip.startTime + 0.001}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {formatTime(selectedClip.endTime)}
                    </p>
                  </div>
                </div>
              </div>

              {/* 轨道选择 */}
              <div>
                <Label className="text-sm">音频轨道</Label>
                <div className="mt-1">
                  <select
                    value={selectedClip.track}
                    onChange={(e) => handleTrackChange(parseInt(e.target.value))}
                    className="w-full px-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {Array.from({ length: 8 }, (_, i) => (
                      <option key={i} value={i}>
                        音频轨道 {i + 1}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            <Separator />

            {/* 高级设置 */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-semibold text-gray-700">高级设置</h3>
                <Switch
                  checked={showAdvanced}
                  onCheckedChange={setShowAdvanced}
                />
              </div>

              {showAdvanced && (
                <div className="space-y-4">
                  {/* 音频滤镜 */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">音频滤镜</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Button variant="outline" size="sm" className="text-xs">
                        降噪
                      </Button>
                      <Button variant="outline" size="sm" className="text-xs">
                        均衡器
                      </Button>
                      <Button variant="outline" size="sm" className="text-xs">
                        混响
                      </Button>
                      <Button variant="outline" size="sm" className="text-xs">
                        压缩
                      </Button>
                    </div>
                  </div>

                  {/* 淡入淡出 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm">淡入时长 (秒)</Label>
                      <Slider
                        value={[0]}
                        max={5}
                        step={0.1}
                        className="w-full mt-2"
                      />
                    </div>
                    <div>
                      <Label className="text-sm">淡出时长 (秒)</Label>
                      <Slider
                        value={[0]}
                        max={5}
                        step={0.1}
                        className="w-full mt-2"
                      />
                    </div>
                  </div>

                  {/* 音频信息 */}
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h4 className="text-sm font-medium mb-2">音频信息</h4>
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>时长: {formatTime(selectedClip.duration)}</div>
                      <div>采样率: 44.1 kHz</div>
                      <div>比特率: 320 kbps</div>
                      <div>声道: 立体声</div>
                      <div>格式: MP3</div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* 操作按钮 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onClipDuplicate?.(selectedClip.id)}
                >
                  <Copy className="w-4 h-4 mr-1" />
                  复制
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                >
                  <Scissors className="w-4 h-4 mr-1" />
                  分割
                </Button>
              </div>

              <Button
                variant="destructive"
                size="sm"
                onClick={() => onClipDelete?.(selectedClip.id)}
              >
                <Trash2 className="w-4 h-4 mr-1" />
                删除
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* 无选中片段时的状态 */
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-6xl mb-4">🎵</div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">音频编辑器</h3>
            <p className="text-sm text-gray-600 mb-4">
              选择一个音频片段开始编辑
            </p>
            
            {audioClips.length === 0 && (
              <div className="space-y-3">
                <Button variant="outline" className="w-full">
                  <Upload className="w-4 h-4 mr-2" />
                  上传音频文件
                </Button>
                <p className="text-xs text-gray-500">
                  支持 MP3, WAV, AAC, OGG 格式
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
