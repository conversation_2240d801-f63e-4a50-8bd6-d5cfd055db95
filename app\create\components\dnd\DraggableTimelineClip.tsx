"use client"

import React, { useRef } from 'react'
import { useDrag, useDrop } from 'react-dnd'
import { Rect, Group, Text } from 'react-konva'
import { DragTypes, DragItem, DropResult } from './DragDropProvider'
import { VideoClip, AudioClip, SubtitleClip } from '../../stores'

interface DraggableTimelineClipProps {
  clip: VideoClip | AudioClip | SubtitleClip
  x: number
  y: number
  width: number
  height: number
  isSelected: boolean
  onSelect: (clipId: string) => void
  onMove: (clipId: string, newStartTime: number, newTrack: number) => void
  onResize?: (clipId: string, newDuration: number) => void
  pixelsPerSecond: number
  trackHeight: number
  snapToGrid?: boolean
  snapThreshold?: number
}

export const DraggableTimelineClip: React.FC<DraggableTimelineClipProps> = ({
  clip,
  x,
  y,
  width,
  height,
  isSelected,
  onSelect,
  onMove,
  onResize,
  pixelsPerSecond,
  trackHeight,
  snapToGrid = true,
  snapThreshold = 0.1
}) => {
  const groupRef = useRef<any>(null)
  
  // 确定片段类型
  const clipType = 'videoUrl' in clip ? 'video' : 
                   'audioUrl' in clip ? 'audio' : 'subtitle'

  // 创建拖拽项目数据
  const dragItem: DragItem = {
    type: clipType === 'video' ? DragTypes.VIDEO_CLIP :
          clipType === 'audio' ? DragTypes.AUDIO_CLIP :
          DragTypes.SUBTITLE_CLIP,
    id: clip.id,
    clipType,
    originalTrack: clip.track,
    originalStartTime: clip.startTime,
    duration: 'duration' in clip ? clip.duration : (clip.endTime - clip.startTime),
    name: clip.name || `${clipType} clip`,
    thumbnail: 'thumbnail' in clip ? clip.thumbnail : undefined,
    data: clip
  }

  // 设置拖拽
  const [{ isDragging }, drag] = useDrag({
    type: dragItem.type,
    item: dragItem,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult<DropResult>()
      if (dropResult && dropResult.dropZone === 'timeline') {
        onMove(item.id, dropResult.time, dropResult.track)
      }
    }
  })

  // 设置放置目标（用于片段之间的重新排序）
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: [DragTypes.VIDEO_CLIP, DragTypes.AUDIO_CLIP, DragTypes.SUBTITLE_CLIP, DragTypes.MEDIA_ITEM],
    drop: (item: DragItem, monitor): DropResult => {
      const clientOffset = monitor.getClientOffset()
      if (!clientOffset || !groupRef.current) {
        return { track: clip.track, time: clip.startTime, dropZone: 'timeline' }
      }

      // 计算相对于时间轴的位置
      const stage = groupRef.current.getStage()
      const stagePos = stage.getPointerPosition()
      
      // 转换为时间和轨道
      const newTime = (stagePos.x - 200) / pixelsPerSecond // 减去轨道面板宽度
      const newTrack = Math.floor((stagePos.y - 40) / trackHeight) // 减去标尺高度

      return {
        track: Math.max(0, newTrack),
        time: Math.max(0, snapToGrid ? snapTime(newTime) : newTime),
        dropZone: 'timeline'
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  })

  // 时间吸附函数
  const snapTime = (time: number): number => {
    if (!snapToGrid) return time
    
    const gridInterval = 1 // 1秒网格
    const snappedTime = Math.round(time / gridInterval) * gridInterval
    
    if (Math.abs(time - snappedTime) <= snapThreshold!) {
      return snappedTime
    }
    return time
  }

  // 获取片段颜色
  const getClipColors = () => {
    const baseColors = {
      video: { fill: '#10b981', stroke: '#059669' },
      audio: { fill: '#6366f1', stroke: '#4f46e5' },
      subtitle: { fill: '#8b5cf6', stroke: '#7c3aed' }
    }

    const selectedColors = {
      video: { fill: '#3b82f6', stroke: '#1d4ed8' },
      audio: { fill: '#8b5cf6', stroke: '#7c3aed' },
      subtitle: { fill: '#f59e0b', stroke: '#d97706' }
    }

    return isSelected ? selectedColors[clipType] : baseColors[clipType]
  }

  const colors = getClipColors()

  // 处理点击选择
  const handleClick = () => {
    onSelect(clip.id)
  }

  // 处理拖拽移动
  const handleDragMove = (e: any) => {
    const newX = e.target.x()
    const newY = e.target.y()
    
    // 计算新的时间和轨道
    const newTime = newX / pixelsPerSecond
    const newTrack = Math.max(0, Math.floor(newY / trackHeight))
    
    // 应用吸附
    const snappedTime = snapToGrid ? snapTime(newTime) : newTime
    const snappedX = snappedTime * pixelsPerSecond
    const snappedY = newTrack * trackHeight
    
    // 更新位置
    e.target.x(snappedX)
    e.target.y(snappedY)
  }

  // 处理拖拽结束
  const handleDragEnd = (e: any) => {
    const newX = e.target.x()
    const newY = e.target.y()
    
    const newTime = newX / pixelsPerSecond
    const newTrack = Math.max(0, Math.floor(newY / trackHeight))
    
    const snappedTime = snapToGrid ? snapTime(newTime) : newTime
    
    // 通知父组件
    onMove(clip.id, snappedTime, newTrack)
    
    // 重置位置，让父组件重新渲染
    e.target.x(x)
    e.target.y(y)
  }

  // 合并拖拽引用
  const combinedRef = (node: any) => {
    groupRef.current = node
    drag(drop(node))
  }

  return (
    <Group
      ref={combinedRef}
      x={x}
      y={y}
      draggable
      onClick={handleClick}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
      opacity={isDragging ? 0.5 : 1}
    >
      {/* 主要片段矩形 */}
      <Rect
        width={width}
        height={height}
        fill={colors.fill}
        stroke={colors.stroke}
        strokeWidth={isSelected ? 2 : 1}
        cornerRadius={4}
        shadowColor={isSelected ? colors.stroke : 'transparent'}
        shadowBlur={isSelected ? 4 : 0}
        shadowOpacity={0.3}
      />

      {/* 拖拽覆盖层（提供更好的拖拽体验） */}
      {isOver && canDrop && (
        <Rect
          width={width}
          height={height}
          fill="rgba(59, 130, 246, 0.2)"
          stroke="#3b82f6"
          strokeWidth={2}
          cornerRadius={4}
          dash={[5, 5]}
        />
      )}

      {/* 缩略图区域（视频片段） */}
      {clipType === 'video' && 'thumbnail' in clip && clip.thumbnail && (
        <Rect
          x={2}
          y={2}
          width={Math.min(width - 4, height - 4)}
          height={height - 4}
          cornerRadius={2}
          fillPatternImage={(() => {
            const img = new Image()
            img.src = clip.thumbnail
            return img
          })()}
        />
      )}

      {/* 波形区域（音频片段） */}
      {clipType === 'audio' && (
        <Rect
          x={4}
          y={height / 2 - 2}
          width={width - 8}
          height={4}
          fill="#a855f7"
          opacity={0.6}
        />
      )}

      {/* 片段标题 */}
      <Text
        x={8}
        y={height / 2 - 6}
        text={clip.name}
        fontSize={11}
        fontFamily="Arial"
        fill="white"
        width={width - 16}
        ellipsis={true}
      />

      {/* 缓存状态指示器 */}
      {'cacheSource' in clip && (
        <>
          <Rect
            x={width - 20}
            y={4}
            width={16}
            height={12}
            fill={
              clip.cacheSource === 'video' ? '#3b82f6' :
              clip.cacheSource === 'image' ? '#10b981' : '#6b7280'
            }
            cornerRadius={2}
          />
          <Text
            x={width - 18}
            y={6}
            text={
              clip.cacheSource === 'video' ? '🎥' :
              clip.cacheSource === 'image' ? '🖼️' : '📝'
            }
            fontSize={8}
            fill="white"
          />
        </>
      )}

      {/* 调整手柄（左右两侧） */}
      {isSelected && onResize && (
        <>
          {/* 左侧调整手柄 */}
          <Rect
            x={0}
            y={0}
            width={4}
            height={height}
            fill="rgba(255, 255, 255, 0.8)"
            stroke={colors.stroke}
            strokeWidth={1}
            cornerRadius={[2, 0, 0, 2]}
            cursor="ew-resize"
          />
          
          {/* 右侧调整手柄 */}
          <Rect
            x={width - 4}
            y={0}
            width={4}
            height={height}
            fill="rgba(255, 255, 255, 0.8)"
            stroke={colors.stroke}
            strokeWidth={1}
            cornerRadius={[0, 2, 2, 0]}
            cursor="ew-resize"
          />
        </>
      )}
    </Group>
  )
}
