// 字幕解析器 - 支持 SRT, VTT, ASS 格式

export interface SubtitleEntry {
  id: string
  startTime: number
  endTime: number
  text: string
  style?: SubtitleStyle
  position?: SubtitlePosition
  metadata?: Record<string, any>
}

export interface SubtitleStyle {
  fontFamily?: string
  fontSize?: number
  color?: string
  backgroundColor?: string
  bold?: boolean
  italic?: boolean
  underline?: boolean
  shadow?: boolean
  outline?: boolean
  alignment?: 'left' | 'center' | 'right'
}

export interface SubtitlePosition {
  x?: number
  y?: number
  anchor?: 'top-left' | 'top-center' | 'top-right' | 'center-left' | 'center' | 'center-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
}

export class SubtitleParser {
  // 解析 SRT 格式
  static parseSRT(content: string): SubtitleEntry[] {
    const entries: SubtitleEntry[] = []
    const blocks = content.trim().split(/\n\s*\n/)

    for (const block of blocks) {
      const lines = block.trim().split('\n')
      if (lines.length < 3) continue

      const id = lines[0].trim()
      const timeLine = lines[1].trim()
      const text = lines.slice(2).join('\n').trim()

      // 解析时间戳 (00:00:20,000 --> 00:00:24,400)
      const timeMatch = timeLine.match(/(\d{2}):(\d{2}):(\d{2}),(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2}),(\d{3})/)
      if (!timeMatch) continue

      const startTime = this.parseTimeToSeconds(
        parseInt(timeMatch[1]), // hours
        parseInt(timeMatch[2]), // minutes
        parseInt(timeMatch[3]), // seconds
        parseInt(timeMatch[4])  // milliseconds
      )

      const endTime = this.parseTimeToSeconds(
        parseInt(timeMatch[5]), // hours
        parseInt(timeMatch[6]), // minutes
        parseInt(timeMatch[7]), // seconds
        parseInt(timeMatch[8])  // milliseconds
      )

      entries.push({
        id: `srt-${id}`,
        startTime,
        endTime,
        text: this.cleanText(text)
      })
    }

    return entries
  }

  // 解析 VTT 格式
  static parseVTT(content: string): SubtitleEntry[] {
    const entries: SubtitleEntry[] = []
    const lines = content.split('\n')
    let currentEntry: Partial<SubtitleEntry> | null = null
    let entryIndex = 0

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      // 跳过 WEBVTT 头部和空行
      if (line === 'WEBVTT' || line === '' || line.startsWith('NOTE')) {
        continue
      }

      // 检查是否是时间戳行
      const timeMatch = line.match(/(\d{2}):(\d{2}):(\d{2})\.(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2})\.(\d{3})(.*)/)
      if (timeMatch) {
        // 如果有之前的条目，先保存
        if (currentEntry && currentEntry.text) {
          entries.push({
            id: `vtt-${entryIndex++}`,
            startTime: currentEntry.startTime!,
            endTime: currentEntry.endTime!,
            text: currentEntry.text,
            style: currentEntry.style,
            position: currentEntry.position
          })
        }

        // 开始新条目
        const startTime = this.parseTimeToSeconds(
          parseInt(timeMatch[1]),
          parseInt(timeMatch[2]),
          parseInt(timeMatch[3]),
          parseInt(timeMatch[4])
        )

        const endTime = this.parseTimeToSeconds(
          parseInt(timeMatch[5]),
          parseInt(timeMatch[6]),
          parseInt(timeMatch[7]),
          parseInt(timeMatch[8])
        )

        currentEntry = {
          startTime,
          endTime,
          text: '',
          style: {},
          position: {}
        }

        // 解析位置和样式设置
        const settings = timeMatch[8]?.trim()
        if (settings) {
          this.parseVTTSettings(settings, currentEntry)
        }
      } else if (currentEntry && line !== '') {
        // 累积文本内容
        if (currentEntry.text) {
          currentEntry.text += '\n' + line
        } else {
          currentEntry.text = line
        }
      }
    }

    // 保存最后一个条目
    if (currentEntry && currentEntry.text) {
      entries.push({
        id: `vtt-${entryIndex}`,
        startTime: currentEntry.startTime!,
        endTime: currentEntry.endTime!,
        text: currentEntry.text,
        style: currentEntry.style,
        position: currentEntry.position
      })
    }

    return entries
  }

  // 解析 ASS 格式（简化版）
  static parseASS(content: string): SubtitleEntry[] {
    const entries: SubtitleEntry[] = []
    const lines = content.split('\n')
    let formatLine = ''
    let entryIndex = 0

    for (const line of lines) {
      const trimmedLine = line.trim()

      // 查找格式定义行
      if (trimmedLine.startsWith('Format:')) {
        formatLine = trimmedLine.substring(7).trim()
        continue
      }

      // 解析对话行
      if (trimmedLine.startsWith('Dialogue:')) {
        const dialogueData = trimmedLine.substring(9).trim()
        const entry = this.parseASSDialogue(dialogueData, formatLine, entryIndex++)
        if (entry) {
          entries.push(entry)
        }
      }
    }

    return entries
  }

  // 解析 ASS 对话行
  private static parseASSDialogue(data: string, format: string, index: number): SubtitleEntry | null {
    const formatFields = format.split(',').map(f => f.trim())
    const dataFields = data.split(',')

    if (dataFields.length < formatFields.length) return null

    const entry: Partial<SubtitleEntry> = {
      id: `ass-${index}`,
      style: {},
      metadata: {}
    }

    for (let i = 0; i < formatFields.length; i++) {
      const field = formatFields[i].toLowerCase()
      const value = dataFields[i]?.trim()

      switch (field) {
        case 'start':
          entry.startTime = this.parseASSTime(value)
          break
        case 'end':
          entry.endTime = this.parseASSTime(value)
          break
        case 'text':
          // ASS 文本可能包含格式标签
          entry.text = this.parseASSText(dataFields.slice(i).join(','))
          break
        case 'style':
          entry.metadata!.styleName = value
          break
        case 'name':
          entry.metadata!.characterName = value
          break
      }
    }

    if (entry.startTime !== undefined && entry.endTime !== undefined && entry.text) {
      return entry as SubtitleEntry
    }

    return null
  }

  // 解析 ASS 时间格式 (H:MM:SS.cc)
  private static parseASSTime(timeStr: string): number {
    const match = timeStr.match(/(\d+):(\d{2}):(\d{2})\.(\d{2})/)
    if (!match) return 0

    return this.parseTimeToSeconds(
      parseInt(match[1]),
      parseInt(match[2]),
      parseInt(match[3]),
      parseInt(match[4]) * 10 // centiseconds to milliseconds
    )
  }

  // 解析 ASS 文本（移除格式标签）
  private static parseASSText(text: string): string {
    // 移除 ASS 格式标签 {\...}
    return text.replace(/\{[^}]*\}/g, '').trim()
  }

  // 解析 VTT 设置
  private static parseVTTSettings(settings: string, entry: Partial<SubtitleEntry>) {
    const settingPairs = settings.split(/\s+/)
    
    for (const pair of settingPairs) {
      const [key, value] = pair.split(':')
      if (!key || !value) continue

      switch (key.toLowerCase()) {
        case 'position':
          if (!entry.position) entry.position = {}
          const pos = parseFloat(value.replace('%', ''))
          entry.position.x = pos
          break
        case 'line':
          if (!entry.position) entry.position = {}
          const line = parseFloat(value.replace('%', ''))
          entry.position.y = line
          break
        case 'align':
          if (!entry.style) entry.style = {}
          entry.style.alignment = value as 'left' | 'center' | 'right'
          break
        case 'size':
          if (!entry.style) entry.style = {}
          entry.style.fontSize = parseFloat(value.replace('%', ''))
          break
      }
    }
  }

  // 时间转换为秒
  private static parseTimeToSeconds(hours: number, minutes: number, seconds: number, milliseconds: number): number {
    return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
  }

  // 清理文本内容
  private static cleanText(text: string): string {
    return text
      .replace(/<[^>]*>/g, '') // 移除 HTML 标签
      .replace(/\{[^}]*\}/g, '') // 移除格式标签
      .trim()
  }

  // 导出为 SRT 格式
  static exportToSRT(entries: SubtitleEntry[]): string {
    return entries.map((entry, index) => {
      const startTime = this.formatSRTTime(entry.startTime)
      const endTime = this.formatSRTTime(entry.endTime)
      
      return `${index + 1}\n${startTime} --> ${endTime}\n${entry.text}\n`
    }).join('\n')
  }

  // 导出为 VTT 格式
  static exportToVTT(entries: SubtitleEntry[]): string {
    let vtt = 'WEBVTT\n\n'
    
    vtt += entries.map(entry => {
      const startTime = this.formatVTTTime(entry.startTime)
      const endTime = this.formatVTTTime(entry.endTime)
      
      let timeLine = `${startTime} --> ${endTime}`
      
      // 添加位置和样式设置
      if (entry.position || entry.style) {
        const settings = []
        if (entry.position?.x !== undefined) settings.push(`position:${entry.position.x}%`)
        if (entry.position?.y !== undefined) settings.push(`line:${entry.position.y}%`)
        if (entry.style?.alignment) settings.push(`align:${entry.style.alignment}`)
        if (settings.length > 0) {
          timeLine += ' ' + settings.join(' ')
        }
      }
      
      return `${timeLine}\n${entry.text}\n`
    }).join('\n')
    
    return vtt
  }

  // 格式化 SRT 时间
  private static formatSRTTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
  }

  // 格式化 VTT 时间
  private static formatVTTTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
  }

  // 自动检测字幕格式
  static detectFormat(content: string): 'srt' | 'vtt' | 'ass' | 'unknown' {
    const trimmedContent = content.trim()
    
    if (trimmedContent.startsWith('WEBVTT')) {
      return 'vtt'
    }
    
    if (trimmedContent.includes('[Script Info]') || trimmedContent.includes('Dialogue:')) {
      return 'ass'
    }
    
    // 检查 SRT 格式特征
    const srtPattern = /^\d+\s*\n\d{2}:\d{2}:\d{2},\d{3}\s*-->\s*\d{2}:\d{2}:\d{2},\d{3}/m
    if (srtPattern.test(trimmedContent)) {
      return 'srt'
    }
    
    return 'unknown'
  }

  // 通用解析方法
  static parse(content: string, format?: string): SubtitleEntry[] {
    const detectedFormat = format || this.detectFormat(content)
    
    switch (detectedFormat) {
      case 'srt':
        return this.parseSRT(content)
      case 'vtt':
        return this.parseVTT(content)
      case 'ass':
        return this.parseASS(content)
      default:
        throw new Error(`Unsupported subtitle format: ${detectedFormat}`)
    }
  }
}
