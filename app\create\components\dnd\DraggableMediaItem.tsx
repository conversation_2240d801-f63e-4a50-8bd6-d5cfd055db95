"use client"

import React from 'react'
import { useDrag } from 'react-dnd'
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Video, Volume2, Type } from "lucide-react"
import { DragTypes, DragItem } from './DragDropProvider'
import { VideoClip, AudioClip, SubtitleClip } from '../../stores'

interface DraggableMediaItemProps {
  clip: VideoClip | AudioClip | SubtitleClip
  onSelect?: (clipId: string) => void
  isSelected?: boolean
}

export const DraggableMediaItem: React.FC<DraggableMediaItemProps> = ({
  clip,
  onSelect,
  isSelected = false
}) => {
  // 确定片段类型
  const clipType = 'videoUrl' in clip ? 'video' : 
                   'audioUrl' in clip ? 'audio' : 'subtitle'

  // 创建拖拽项目数据
  const dragItem: DragItem = {
    type: DragTypes.MEDIA_ITEM,
    id: clip.id,
    clipType,
    duration: 'duration' in clip ? clip.duration : (clip.endTime - clip.startTime),
    name: clip.name || `${clipType} clip`,
    thumbnail: 'thumbnail' in clip ? clip.thumbnail : undefined,
    data: clip
  }

  // 设置拖拽
  const [{ isDragging }, drag, dragPreview] = useDrag({
    type: DragTypes.MEDIA_ITEM,
    item: dragItem,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult()
      if (dropResult) {
        console.log('拖拽结束:', item, dropResult)
      }
    }
  })

  // 获取图标
  const getIcon = () => {
    switch (clipType) {
      case 'video':
        return <Video className="w-4 h-4" />
      case 'audio':
        return <Volume2 className="w-4 h-4" />
      case 'subtitle':
        return <Type className="w-4 h-4" />
      default:
        return null
    }
  }

  // 获取缓存状态标识
  const getCacheInfo = () => {
    if ('cacheSource' in clip) {
      return {
        source: clip.cacheSource,
        status: clip.videoTaskStatus || clip.imageTaskStatus
      }
    }
    return { source: 'none', status: undefined }
  }

  const cacheInfo = getCacheInfo()

  return (
    <>
      {/* 拖拽预览 */}
      <div ref={dragPreview} style={{ opacity: isDragging ? 0.5 : 1 }}>
        <div
          ref={drag}
          className={`
            group relative bg-slate-50 rounded-lg p-3 border transition-all cursor-grab active:cursor-grabbing
            ${isSelected 
              ? 'border-emerald-400 bg-emerald-50/50 shadow-md' 
              : 'border-slate-200 hover:border-emerald-300 hover:bg-emerald-50/30'
            }
            ${isDragging ? 'opacity-50 scale-95' : 'opacity-100 scale-100'}
          `}
          onClick={() => onSelect?.(clip.id)}
        >
          <div className="flex items-center space-x-3">
            {/* 缩略图或图标 */}
            <div className="w-16 h-12 bg-gradient-to-br from-slate-300 to-slate-400 rounded-md flex items-center justify-center overflow-hidden flex-shrink-0">
              {'thumbnail' in clip && clip.thumbnail && clip.thumbnail !== '/placeholder.svg' ? (
                <img 
                  src={clip.thumbnail} 
                  alt={clip.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className={`w-8 h-8 rounded-md flex items-center justify-center ${
                  clipType === 'video' ? 'bg-emerald-100 text-emerald-600' :
                  clipType === 'audio' ? 'bg-blue-100 text-blue-600' :
                  'bg-purple-100 text-purple-600'
                }`}>
                  {getIcon()}
                </div>
              )}
            </div>

            {/* 片段信息 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <p className="text-sm font-medium text-slate-800 truncate">
                  {clip.name}
                </p>
                
                {/* 缓存状态指示器 */}
                <Badge
                  className={`ml-2 font-semibold px-2 py-1 text-xs ${
                    cacheInfo.source === 'video'
                      ? "bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-blue-200"
                      : cacheInfo.source === 'image'
                      ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200"
                      : "bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200"
                  }`}
                  title={
                    cacheInfo.source === 'video'
                      ? `视频缓存\n状态: ${cacheInfo.status || '未知'}`
                      : cacheInfo.source === 'image'
                      ? `图片缓存\n状态: ${cacheInfo.status || '未知'}`
                      : "无缓存数据"
                  }
                >
                  {cacheInfo.source === 'video' ? '🎥 视频' :
                   cacheInfo.source === 'image' ? '🖼️ 图片' : '📝 无缓存'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <p className="text-xs text-slate-500">
                  {Math.floor(dragItem.duration)}s | {clipType === 'video' ? '1920x1080' : clipType.toUpperCase()}
                </p>
                
                {/* 轨道信息 */}
                {clip.track !== undefined && (
                  <span className="text-xs text-slate-400">
                    轨道 {clip.track}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* 拖拽提示 */}
          <div className="mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="text-xs text-slate-500 text-center">
              拖拽到时间轴添加
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              size="sm"
              className="h-6 px-2 text-xs bg-emerald-500 hover:bg-emerald-600"
              onClick={(e) => {
                e.stopPropagation()
                // 直接添加到时间轴
                console.log('直接添加到时间轴:', clip.id)
              }}
            >
              添加
            </Button>
          </div>

          {/* 选中状态指示器 */}
          {isSelected && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full border-2 border-white"></div>
          )}
        </div>
      </div>
    </>
  )
}
