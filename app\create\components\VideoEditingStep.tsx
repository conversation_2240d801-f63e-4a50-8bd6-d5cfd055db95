"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import React from "react"
import useS<PERSON>, { mutate } from "swr"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import {
  Video,
  Play,
  Pause,
  Volume2,
  VolumeX,
  SkipBack,
  SkipForward,
  Scissors,
  Upload,
  Download,
  Save,
  Eye,
  Palette,
  Clock,
  Music,
  Type,
  Sparkles,
  Settings,
  MoreHorizontal,
  Trash2,
  Copy,
  Move,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  RotateCw,
  RefreshC<PERSON>,
} from "lucide-react"

// SWR fetcher function for video task status
const fetchVideoTaskStatus = async (key: string) => {
  const [, projectId, userId, shotIdsStr] = key.split('|')
  const shotIds = shotIdsStr.split(',')

  const response = await fetch('/api/video-records/cached', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      projectId,
      userId,
      shotIds
    })
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  return response.json()
}

// SWR fetcher function for image task status
const fetchImageTaskStatus = async (key: string) => {
  const [, projectId, userId, shotNumbersStr] = key.split('|')
  const shotNumbers = shotNumbersStr.split(',').map(num => parseInt(num))

  const response = await fetch('/api/image-records/cached', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      projectId,
      userId,
      shotNumbers
    })
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  return response.json()
}

// Custom hook for managing combined video and image task status with SWR caching
const useCombinedTaskStatus = (projectId?: string, userId?: string, shotsWithImages?: any[]) => {
  // Extract shot IDs and shot numbers
  const shotIds = React.useMemo(() => {
    return shotsWithImages?.map(shot => shot.shotId).filter(Boolean) as string[] || []
  }, [shotsWithImages])

  const shotNumbers = React.useMemo(() => {
    return shotsWithImages?.map(shot => shot.shotNumber).filter(Boolean) as number[] || []
  }, [shotsWithImages])

  // Create cache keys
  const videoCacheKey = projectId && userId && shotIds?.length
    ? `video-tasks|${projectId}|${userId}|${shotIds.join(',')}`
    : null

  const imageCacheKey = projectId && userId && shotNumbers?.length
    ? `image-tasks|${projectId}|${userId}|${shotNumbers.join(',')}`
    : null

  // Fetch video task data
  const { data: videoTaskData, error: videoError, isLoading: isVideoLoading, mutate: mutateVideo } = useSWR(
    videoCacheKey,
    fetchVideoTaskStatus,
    {
      dedupingInterval: 5 * 60 * 1000,
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      refreshInterval: 2 * 60 * 1000,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
      refreshWhenHidden: false,
      refreshWhenOffline: false,
    }
  )

  // Fetch image task data
  const { data: imageTaskData, error: imageError, isLoading: isImageLoading, mutate: mutateImage } = useSWR(
    imageCacheKey,
    fetchImageTaskStatus,
    {
      dedupingInterval: 5 * 60 * 1000,
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      refreshInterval: 2 * 60 * 1000,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
      refreshWhenHidden: false,
      refreshWhenOffline: false,
    }
  )

  // Helper function to refresh both caches
  const refreshTaskStatus = useCallback(() => {
    const promises = []
    if (mutateVideo) promises.push(mutateVideo())
    if (mutateImage) promises.push(mutateImage())
    return Promise.all(promises)
  }, [mutateVideo, mutateImage])

  return {
    videoTaskData,
    imageTaskData,
    isVideoLoading,
    isImageLoading,
    videoError,
    imageError,
    refreshTaskStatus,
    mutateVideo,
    mutateImage
  }
}

interface VideoClip {
  id: string
  name: string
  duration: number
  thumbnail: string
  videoUrl?: string
  startTime: number
  endTime: number
  track: number
  sceneData?: any // 保存场景的完整数据
  // 缓存相关字段
  hasVideoCache?: boolean
  hasImageCache?: boolean
  videoTaskStatus?: string
  imageTaskStatus?: string
  cacheSource?: 'video' | 'image' | 'none'
  lastCacheCheck?: number
}

interface AudioClip {
  id: string
  name: string
  duration: number
  startTime: number
  endTime: number
  volume: number
}

interface VideoEditingStepProps {
  projectId?: string
  userId?: string
  scriptData?: any
  shotsWithImages?: any[]
}

export function VideoEditingStep({
  projectId,
  userId,
  scriptData,
  shotsWithImages,
}: VideoEditingStepProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(100)
  const [volume, setVolume] = useState([80])
  const [isMuted, setIsMuted] = useState(false)
  const [selectedClip, setSelectedClip] = useState<VideoClip | null>(null)
  const [zoomLevel, setZoomLevel] = useState(1)
  const [isSmartMode, setIsSmartMode] = useState(true)

  // Use combined SWR hook for caching video and image task status
  const {
    videoTaskData,
    imageTaskData,
    isVideoLoading,
    isImageLoading,
    videoError,
    imageError,
    refreshTaskStatus
  } = useCombinedTaskStatus(projectId, userId, shotsWithImages)

  // 图片序列播放相关状态
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [imagePlaybackTimer, setImagePlaybackTimer] = useState<NodeJS.Timeout | null>(null)
  
  // 视频片段数据
  const [videoClips, setVideoClips] = useState<VideoClip[]>([])
  const [audioClips, setAudioClips] = useState<AudioClip[]>([])

  // 输出设置
  const [outputResolution, setOutputResolution] = useState("1920x1080")
  const [outputFormat, setOutputFormat] = useState("mp4")

  // 智能生成视频片段：优先使用视频缓存，其次使用图片缓存
  const generateSmartVideoClips = React.useCallback((): VideoClip[] => {
    if (!shotsWithImages || shotsWithImages.length === 0) {
      console.log('没有场景数据可用于生成视频片段')
      return []
    }

    console.log('🎬 [SMART-CLIPS] 开始智能生成视频片段')
    console.log('🎬 [SMART-CLIPS] 视频缓存数据:', videoTaskData)
    console.log('🎬 [SMART-CLIPS] 图片缓存数据:', imageTaskData)

    const newVideoClips: VideoClip[] = shotsWithImages.map((shot, index) => {
      const duration = 5
      const startTime = index * duration

      // 检查视频缓存
      let hasVideoCache = false
      let videoUrl: string | undefined
      let videoTaskStatus: string | undefined

      if (videoTaskData?.tasks && shot.shotId) {
        const videoTasks = videoTaskData.tasks[shot.shotId]
        if (videoTasks && videoTasks.length > 0) {
          const latestVideoTask = videoTasks[0]
          videoTaskStatus = latestVideoTask.status

          if (latestVideoTask.status === 'completed' && latestVideoTask.generated_videos) {
            hasVideoCache = true
            // 使用第一个生成的视频
            const firstVideo = latestVideoTask.generated_videos[0]
            if (firstVideo) {
              videoUrl = firstVideo.video_url
            }
          }
        }
      }

      // 检查图片缓存
      let hasImageCache = false
      let thumbnailUrl = '/placeholder.svg'
      let imageTaskStatus: string | undefined

      if (imageTaskData?.tasks && shot.shotNumber) {
        const imageTasks = imageTaskData.tasks[shot.shotNumber]
        if (imageTasks && imageTasks.length > 0) {
          const latestImageTask = imageTasks[0]
          imageTaskStatus = latestImageTask.status

          if (latestImageTask.status === 'completed' && latestImageTask.generated_images) {
            hasImageCache = true
            // 使用第一个生成的图片作为缩略图
            const firstImage = latestImageTask.generated_images[0]
            if (firstImage) {
              thumbnailUrl = firstImage.cdn_url || firstImage.image_url
            }
          }
        }
      }

      // 如果没有缓存数据，使用原始的shotsWithImages数据
      if (!hasVideoCache && !hasImageCache) {
        const primaryImage = shot.images?.find((img: any) => img.isPrimary) || shot.images?.[0]
        if (primaryImage) {
          thumbnailUrl = primaryImage.url
          hasImageCache = true // 标记为有图片缓存（来自原始数据）
        }
        videoUrl = shot.videoUrl // 使用原始视频URL（如果有）
      }

      // 确定缓存源：优先视频，其次图片
      let cacheSource: 'video' | 'image' | 'none' = 'none'
      if (hasVideoCache) {
        cacheSource = 'video'
      } else if (hasImageCache) {
        cacheSource = 'image'
      }

      console.log(`🎬 [SMART-CLIPS] 镜头 ${shot.shotNumber || index + 1}:`, {
        shotId: shot.shotId,
        shotNumber: shot.shotNumber,
        hasVideoCache,
        hasImageCache,
        cacheSource,
        videoUrl: videoUrl ? '有视频' : '无视频',
        thumbnailUrl: thumbnailUrl !== '/placeholder.svg' ? '有缩略图' : '无缩略图'
      })

      return {
        id: `smart-clip-${shot.shotId || shot.shotNumber || index}`,
        name: `镜头 ${shot.shotNumber || index + 1}`,
        duration: duration,
        thumbnail: thumbnailUrl,
        videoUrl: videoUrl,
        startTime: startTime,
        endTime: startTime + duration,
        track: 0,
        sceneData: shot,
        // 缓存相关字段
        hasVideoCache,
        hasImageCache,
        videoTaskStatus,
        imageTaskStatus,
        cacheSource,
        lastCacheCheck: Date.now()
      }
    })

    console.log(`🎬 [SMART-CLIPS] 智能生成完成，共 ${newVideoClips.length} 个片段`)
    console.log(`🎬 [SMART-CLIPS] 视频缓存: ${newVideoClips.filter(c => c.hasVideoCache).length} 个`)
    console.log(`🎬 [SMART-CLIPS] 图片缓存: ${newVideoClips.filter(c => c.hasImageCache).length} 个`)

    return newVideoClips
  }, [shotsWithImages, videoTaskData, imageTaskData])

  // 智能剪辑：根据缓存数据生成视频片段
  const handleSmartEditing = () => {
    console.log('🎬 [SMART-EDITING] 开始智能剪辑')

    const newVideoClips = generateSmartVideoClips()
    setVideoClips(newVideoClips)

    console.log('🎬 [SMART-EDITING] 智能剪辑完成，生成了', newVideoClips.length, '个视频片段')

    // 显示缓存统计信息
    const videoCount = newVideoClips.filter(c => c.hasVideoCache).length
    const imageCount = newVideoClips.filter(c => c.hasImageCache).length
    const noCacheCount = newVideoClips.filter(c => c.cacheSource === 'none').length

    console.log(`🎬 [SMART-EDITING] 缓存统计: 视频${videoCount}个, 图片${imageCount}个, 无缓存${noCacheCount}个`)
  }
  const [aiEnhancement, setAiEnhancement] = useState([75])

  const videoRef = useRef<HTMLVideoElement>(null)

  // 初始化视频片段数据 - 使用智能缓存生成
  useEffect(() => {
    console.log('🎬 [INIT] VideoEditingStep 初始化')
    console.log('🎬 [INIT] shotsWithImages:', shotsWithImages)
    console.log('🎬 [INIT] 视频缓存加载状态:', isVideoLoading)
    console.log('🎬 [INIT] 图片缓存加载状态:', isImageLoading)

    // 等待缓存数据加载完成后再生成视频片段
    if (shotsWithImages && shotsWithImages.length > 0 && !isVideoLoading && !isImageLoading) {
      const clips = generateSmartVideoClips()
      setVideoClips(clips)
      setDuration(clips.length * 5)

      console.log('🎬 [INIT] 智能初始化完成，生成了', clips.length, '个视频片段')

      // 显示缓存统计
      const videoCount = clips.filter(c => c.hasVideoCache).length
      const imageCount = clips.filter(c => c.hasImageCache).length
      console.log(`🎬 [INIT] 缓存统计: 视频${videoCount}个, 图片${imageCount}个`)
    }
  }, [shotsWithImages, videoTaskData, imageTaskData, isVideoLoading, isImageLoading, generateSmartVideoClips])

  // 缓存数据加载状态日志
  React.useEffect(() => {
    if (videoTaskData && !isVideoLoading) {
      console.log('✅ [SWR-CACHE] 视频任务数据已从缓存加载:', {
        totalShots: Object.keys(videoTaskData.tasks || {}).length,
        totalCompletedTasks: videoTaskData.totalCompletedTasks,
        totalCompletedVideos: videoTaskData.totalCompletedVideos,
        cached: videoTaskData.cached
      })
    }
  }, [videoTaskData, isVideoLoading])

  React.useEffect(() => {
    if (imageTaskData && !isImageLoading) {
      console.log('✅ [SWR-CACHE] 图片任务数据已从缓存加载:', {
        totalShots: Object.keys(imageTaskData.tasks || {}).length,
        totalCompletedTasks: imageTaskData.totalCompletedTasks,
        totalCompletedImages: imageTaskData.totalCompletedImages,
        cached: imageTaskData.cached
      })
    }
  }, [imageTaskData, isImageLoading])

  const handlePlayPause = () => {
    const newIsPlaying = !isPlaying
    setIsPlaying(newIsPlaying)

    if (selectedClip?.videoUrl) {
      // 如果有视频URL，使用视频播放
      if (videoRef.current) {
        if (newIsPlaying) {
          videoRef.current.play()
        } else {
          videoRef.current.pause()
        }
      }
    } else {
      // 如果没有视频URL，播放图片序列
      if (newIsPlaying) {
        startImageSequencePlayback()
      } else {
        stopImageSequencePlayback()
      }
    }
  }

  // 开始图片序列播放
  const startImageSequencePlayback = () => {
    if (!videoClips || videoClips.length === 0) return

    // 清除之前的定时器
    if (imagePlaybackTimer) {
      clearInterval(imagePlaybackTimer)
    }

    // 计算总时长和当前应该显示的图片
    const totalDuration = videoClips.reduce((sum, clip) => sum + clip.duration, 0)
    setDuration(totalDuration)

    const timer = setInterval(() => {
      setCurrentTime(prevTime => {
        const newTime = prevTime + 0.1 // 每100ms更新一次

        // 计算当前应该显示哪个图片
        let accumulatedTime = 0
        let targetImageIndex = 0

        for (let i = 0; i < videoClips.length; i++) {
          if (newTime >= accumulatedTime && newTime < accumulatedTime + videoClips[i].duration) {
            targetImageIndex = i
            break
          }
          accumulatedTime += videoClips[i].duration
        }

        setCurrentImageIndex(targetImageIndex)

        // 如果播放完毕，停止播放
        if (newTime >= totalDuration) {
          setIsPlaying(false)
          stopImageSequencePlayback()
          return 0
        }

        return newTime
      })
    }, 100)

    setImagePlaybackTimer(timer)
  }

  // 停止图片序列播放
  const stopImageSequencePlayback = () => {
    if (imagePlaybackTimer) {
      clearInterval(imagePlaybackTimer)
      setImagePlaybackTimer(null)
    }
  }

  // 跳转到指定场景
  const handleSkipToScene = (direction: 'prev' | 'next') => {
    if (videoClips.length === 0) return

    let targetIndex = currentImageIndex
    if (direction === 'prev') {
      targetIndex = Math.max(0, currentImageIndex - 1)
    } else {
      targetIndex = Math.min(videoClips.length - 1, currentImageIndex + 1)
    }

    // 计算目标场景的开始时间
    let targetTime = 0
    for (let i = 0; i < targetIndex; i++) {
      targetTime += videoClips[i].duration
    }

    setCurrentTime(targetTime)
    setCurrentImageIndex(targetIndex)
  }

  // 重置播放到开始
  const handleResetPlayback = () => {
    setCurrentTime(0)
    setCurrentImageIndex(0)
    setIsPlaying(false)
    stopImageSequencePlayback()
  }

  // 清理定时器
  useEffect(() => {
    return () => {
      if (imagePlaybackTimer) {
        clearInterval(imagePlaybackTimer)
      }
    }
  }, [imagePlaybackTimer])

  const handleTimeUpdate = (newTime: number) => {
    setCurrentTime(newTime)

    if (selectedClip?.videoUrl && videoRef.current) {
      // 如果有视频，更新视频时间
      videoRef.current.currentTime = newTime
    } else {
      // 如果是图片序列，计算应该显示的图片
      let accumulatedTime = 0
      let targetImageIndex = 0

      for (let i = 0; i < videoClips.length; i++) {
        if (newTime >= accumulatedTime && newTime < accumulatedTime + videoClips[i].duration) {
          targetImageIndex = i
          break
        }
        accumulatedTime += videoClips[i].duration
      }

      setCurrentImageIndex(targetImageIndex)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <>
      <style jsx>{`
        .text-shadow-sm {
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
      `}</style>
      <div className="h-full flex bg-gradient-to-br from-slate-50/50 to-blue-50/30">
      {/* 左侧媒体库 */}
      <div className="w-80 bg-white/80 backdrop-blur-sm border-r border-slate-200/60 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center">
              <Video className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-slate-800">媒体库</h2>
              <p className="text-sm text-slate-600">拖拽素材到时间轴</p>
            </div>
          </div>

          {/* 智能缓存状态总览 */}
          {shotsWithImages && shotsWithImages.length > 0 && (
            <div className="mb-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center">
                    {isVideoLoading || isImageLoading ? (
                      <RefreshCw className="w-3 h-3 text-indigo-600 animate-spin" />
                    ) : (videoTaskData || imageTaskData) ? (
                      <span className="text-xs">🎬</span>
                    ) : (
                      <span className="text-xs">📝</span>
                    )}
                  </div>
                  <span className="text-sm font-semibold text-indigo-800">
                    {isVideoLoading || isImageLoading ? '正在加载缓存...' :
                     '智能缓存状态'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {(videoTaskData?.cached || imageTaskData?.cached) && (
                    <Badge className="bg-green-100 text-green-800 border-green-200 text-xs px-2 py-1">
                      来自缓存
                    </Badge>
                  )}
                  {(videoError || imageError) && (
                    <Badge className="bg-red-100 text-red-800 border-red-200 text-xs px-2 py-1">
                      加载失败
                    </Badge>
                  )}
                </div>
              </div>
              {(videoTaskData || imageTaskData) && (
                <div className="mt-2 text-xs text-indigo-600">
                  视频: {videoTaskData?.totalCompletedVideos || 0} 个 |
                  图片: {imageTaskData?.totalCompletedImages || 0} 张 |
                  查询时间: {(videoTaskData?.queriedAt || imageTaskData?.queriedAt) ?
                    new Date(videoTaskData?.queriedAt || imageTaskData?.queriedAt).toLocaleTimeString() : '未知'}
                </div>
              )}
            </div>
          )}

          {/* 视频素材 */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-semibold text-slate-700">视频素材</h3>
              <Badge variant="secondary" className="text-xs">
                {videoClips.length} 个
              </Badge>
            </div>
            <div className="space-y-3">
              {videoClips.map((clip) => (
                <div
                  key={clip.id}
                  className="group relative bg-slate-50 rounded-lg p-3 border border-slate-200 hover:border-emerald-300 hover:bg-emerald-50/50 transition-all cursor-pointer"
                  onClick={() => setSelectedClip(clip)}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-16 h-12 bg-gradient-to-br from-slate-300 to-slate-400 rounded-md flex items-center justify-center overflow-hidden">
                      {clip.thumbnail ? (
                        <img 
                          src={clip.thumbnail} 
                          alt={clip.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Video className="w-4 h-4 text-slate-600" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-slate-800 truncate">
                          {clip.name}
                        </p>
                        {/* 缓存状态指示器 */}
                        <Badge
                          className={`ml-2 font-semibold px-2 py-1 text-xs ${
                            clip.cacheSource === 'video'
                              ? "bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-blue-200"
                              : clip.cacheSource === 'image'
                              ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200"
                              : "bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200"
                          }`}
                          title={
                            clip.cacheSource === 'video'
                              ? `视频缓存\n状态: ${clip.videoTaskStatus || '未知'}\n来源: 视频生成缓存`
                              : clip.cacheSource === 'image'
                              ? `图片缓存\n状态: ${clip.imageTaskStatus || '未知'}\n来源: 图片生成缓存`
                              : "无缓存数据"
                          }
                        >
                          {clip.cacheSource === 'video' ? (
                            <>🎥 视频</>
                          ) : clip.cacheSource === 'image' ? (
                            <>🖼️ 图片</>
                          ) : (
                            <>📝 无缓存</>
                          )}
                        </Badge>
                      </div>
                      <p className="text-xs text-slate-500">
                        {formatTime(clip.duration)} | 1920x1080
                      </p>
                    </div>
                  </div>
                  <div className="mt-2 flex space-x-1">
                    <Button
                      size="sm"
                      className="h-6 px-2 text-xs bg-emerald-500 hover:bg-emerald-600"
                    >
                      添加到时间轴
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 音频素材 */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-semibold text-slate-700">音频素材</h3>
              <Button size="sm" variant="outline" className="h-7 px-2 text-xs">
                <Upload className="w-3 h-3 mr-1" />
                上传
              </Button>
            </div>
            <div className="p-4 border-2 border-dashed border-slate-300 rounded-lg text-center">
              <Music className="w-8 h-8 text-slate-400 mx-auto mb-2" />
              <p className="text-xs text-slate-500">
                拖拽音频文件到此处
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 中央内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部工具栏 */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <h1 className="text-xl font-bold text-slate-800">智能剪辑</h1>
              <Badge className="bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border-emerald-200">
                AI内容生成项目
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshTaskStatus}
                disabled={isVideoLoading || isImageLoading}
                title="刷新视频和图片缓存数据"
              >
                <RefreshCw className={`w-4 h-4 mr-1 ${(isVideoLoading || isImageLoading) ? 'animate-spin' : ''}`} />
                刷新缓存
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handlePlayPause}
                disabled={videoClips.length === 0}
              >
                <Eye className="w-4 h-4 mr-1" />
                {isPlaying ? '暂停预览' : '开始预览'}
              </Button>
              <Button
                size="sm"
                className="bg-emerald-500 hover:bg-emerald-600"
                onClick={handleSmartEditing}
              >
                <Sparkles className="w-4 h-4 mr-1" />
                智能剪辑
              </Button>
            </div>
          </div>
        </div>

        {/* 预览区域 */}
        <div className="flex-1 p-6 pb-0">
          <Card className="h-full">
            <CardContent className="p-6 h-full flex flex-col">
              <div className="flex-1 bg-black rounded-lg flex items-center justify-center mb-4 relative overflow-hidden">
                {selectedClip?.videoUrl ? (
                  // 视频播放
                  <video
                    ref={videoRef}
                    src={selectedClip.videoUrl}
                    className="w-full h-full object-contain"
                    onTimeUpdate={(e) => setCurrentTime(e.currentTarget.currentTime)}
                    onLoadedMetadata={(e) => setDuration(e.currentTarget.duration)}
                  />
                ) : videoClips.length > 0 ? (
                  // 图片序列播放
                  <div className="w-full h-full relative flex items-center justify-center">
                    {videoClips[currentImageIndex]?.thumbnail && videoClips[currentImageIndex].thumbnail !== '/placeholder.svg' ? (
                      <>
                        <img
                          src={videoClips[currentImageIndex].thumbnail}
                          alt={videoClips[currentImageIndex].name}
                          className="max-w-full max-h-full object-contain"
                        />
                        {/* 场景信息覆盖层 */}
                        <div className="absolute bottom-4 left-4 bg-black/70 text-white px-4 py-2 rounded-lg backdrop-blur-sm">
                          <div className="text-sm font-medium">{videoClips[currentImageIndex].name}</div>
                          <div className="text-xs opacity-80">
                            {videoClips[currentImageIndex].sceneData?.location} - {videoClips[currentImageIndex].sceneData?.action}
                          </div>
                        </div>
                        {/* 播放进度指示器 */}
                        <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-xs font-mono">
                          {Math.floor(currentTime)}s / {Math.floor(duration)}s
                        </div>
                      </>
                    ) : (
                      <div className="text-white text-center">
                        <div className="text-4xl mb-4">📷</div>
                        <p className="text-lg mb-2">{videoClips[currentImageIndex]?.name || '场景预览'}</p>
                        <p className="text-sm opacity-70">暂无图片</p>
                      </div>
                    )}
                  </div>
                ) : (
                  // 默认状态
                  <div className="text-white text-center">
                    <div className="text-6xl mb-4">🎬</div>
                    <p className="text-xl mb-2">视频预览区域</p>
                    <p className="text-sm opacity-70">点击"智能剪辑"开始预览</p>
                  </div>
                )}
              </div>

              {/* 播放控制 */}
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePlayPause}
                  className="w-10 h-10 p-0"
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-10 h-10 p-0"
                  onClick={handleResetPlayback}
                  disabled={videoClips.length === 0}
                  title="重置到开始"
                >
                  <RotateCcw className="w-4 h-4" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-10 h-10 p-0"
                  onClick={() => handleSkipToScene('prev')}
                  disabled={videoClips.length === 0}
                >
                  <SkipBack className="w-4 h-4" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-10 h-10 p-0"
                  onClick={() => handleSkipToScene('next')}
                  disabled={videoClips.length === 0}
                >
                  <SkipForward className="w-4 h-4" />
                </Button>

                <div className="flex-1 flex items-center space-x-2">
                  <span className="text-sm text-slate-600 min-w-[40px]">
                    {formatTime(currentTime)}
                  </span>
                  <div
                    className="flex-1 bg-slate-200 rounded-full h-2 relative cursor-pointer"
                    onClick={(e) => {
                      const rect = e.currentTarget.getBoundingClientRect()
                      const clickX = e.clientX - rect.left
                      const percentage = clickX / rect.width
                      const newTime = Math.max(0, Math.min(duration, percentage * duration))
                      handleTimeUpdate(newTime)
                    }}
                  >
                    <div
                      className="bg-emerald-500 rounded-full h-2 transition-all"
                      style={{ width: `${(currentTime / duration) * 100}%` }}
                    />
                    <input
                      type="range"
                      min="0"
                      max={duration}
                      value={currentTime}
                      onChange={(e) => handleTimeUpdate(Number(e.target.value))}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                  </div>
                  <span className="text-sm text-slate-600 min-w-[40px]">
                    {formatTime(duration)}
                  </span>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsMuted(!isMuted)}
                  className="w-10 h-10 p-0"
                >
                  {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 时间轴编辑器 */}
        <div className="p-6 pt-0">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">时间轴编辑器</CardTitle>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={isSmartMode}
                      onCheckedChange={setIsSmartMode}
                      className="data-[state=checked]:bg-emerald-500"
                    />
                    <Label className="text-sm text-slate-600">智能模式</Label>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Button variant="outline" size="sm" className="w-8 h-8 p-0">
                      <ZoomOut className="w-3 h-3" />
                    </Button>
                    <Button variant="outline" size="sm" className="w-8 h-8 p-0">
                      <ZoomIn className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {/* 视频轨道 */}
                <div className="flex items-center space-x-4">
                  <div className="w-20 text-sm font-medium text-slate-700">视频轨道</div>
                  <div className="flex-1 h-16 bg-slate-50 rounded-lg border-2 border-slate-200 flex items-center px-3 gap-1 overflow-x-auto relative">
                    {videoClips.map((clip, index) => (
                      <div
                        key={clip.id}
                        className="h-12 bg-white border-2 border-emerald-300 rounded-md flex-shrink-0 flex items-center justify-center text-emerald-700 text-xs font-medium cursor-pointer hover:border-emerald-400 hover:shadow-md transition-all relative group overflow-hidden"
                        style={{ width: `${Math.max(clip.duration * 20, 80)}px` }}
                        onClick={() => {
                          setSelectedClip(clip)
                          // 跳转到该片段的开始时间
                          handleTimeUpdate(clip.startTime)
                        }}
                      >
                        {clip.thumbnail && clip.thumbnail !== '/placeholder.svg' ? (
                          <>
                            <img
                              src={clip.thumbnail}
                              alt={clip.name}
                              className="absolute inset-0 w-full h-full object-cover rounded-sm"
                            />
                            <div className="absolute inset-0 bg-black/20 rounded-sm"></div>
                            <span className="relative z-10 truncate px-1 text-white text-shadow-sm font-semibold">
                              {clip.name}
                            </span>
                          </>
                        ) : (
                          <>
                            <div className="absolute inset-0 bg-gradient-to-r from-emerald-100 to-emerald-200 rounded-sm"></div>
                            <span className="relative z-10 truncate px-1 text-emerald-700">
                              {clip.name}
                            </span>
                          </>
                        )}
                        <div className="absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button size="sm" variant="destructive" className="w-5 h-5 p-0">
                            <Trash2 className="w-2 h-2" />
                          </Button>
                        </div>
                      </div>
                    ))}

                    {/* 播放进度指示器 */}
                    {videoClips.length > 0 && (
                      <div
                        className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 transition-all duration-100"
                        style={{
                          left: `${12 + (currentTime / duration) * (videoClips.reduce((sum, clip) => sum + Math.max(clip.duration * 20, 80), 0))}px`
                        }}
                      >
                        <div className="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full"></div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 音频轨道 */}
                <div className="flex items-center space-x-4">
                  <div className="w-20 text-sm font-medium text-slate-700">音频轨道</div>
                  <div className="flex-1 h-16 bg-slate-50 rounded-lg border-2 border-slate-200 flex items-center px-3 gap-1">
                    {audioClips.length > 0 ? (
                      audioClips.map((audio, index) => (
                        <div
                          key={audio.id}
                          className="h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-md flex-shrink-0 flex items-center justify-center text-white text-xs font-medium cursor-pointer hover:from-blue-500 hover:to-blue-600 transition-all"
                          style={{ width: `${audio.duration * 20}px` }}
                        >
                          {audio.name}
                        </div>
                      ))
                    ) : (
                      <div className="flex-1 flex items-center justify-center text-slate-400 text-sm">
                        拖拽音频文件到此处
                      </div>
                    )}
                  </div>
                </div>

                {/* 字幕轨道 */}
                <div className="flex items-center space-x-4">
                  <div className="w-20 text-sm font-medium text-slate-700">字幕轨道</div>
                  <div className="flex-1 h-16 bg-slate-50 rounded-lg border-2 border-slate-200 flex items-center px-3 gap-1">
                    <div className="flex-1 flex items-center justify-center text-slate-400 text-sm">
                      点击添加字幕
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 右侧属性面板 */}
      <div className="w-80 bg-white/80 backdrop-blur-sm border-l border-slate-200/60 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <Settings className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-slate-800">属性面板</h2>
              <p className="text-sm text-slate-600">编辑参数设置</p>
            </div>
          </div>

          {/* 视频设置 */}
          <div className="mb-8">
            <h3 className="text-sm font-semibold text-slate-700 mb-4">视频设置</h3>
            <div className="space-y-4">
              <div>
                <Label className="text-xs text-slate-600 mb-2 block">项目标题</Label>
                <input
                  type="text"
                  defaultValue="AI生成内容"
                  className="w-full px-3 py-2 text-sm border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                />
              </div>
              <div>
                <Label className="text-xs text-slate-600 mb-2 block">描述</Label>
                <textarea
                  placeholder="输入项目描述..."
                  rows={3}
                  className="w-full px-3 py-2 text-sm border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent resize-none"
                />
              </div>
            </div>
          </div>

          {/* AI参数 */}
          <div className="mb-8">
            <h3 className="text-sm font-semibold text-slate-700 mb-4">AI参数</h3>
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-xs text-slate-600">创意度</Label>
                  <span className="text-xs text-slate-500">{aiEnhancement[0]}%</span>
                </div>
                <Slider
                  value={aiEnhancement}
                  onValueChange={setAiEnhancement}
                  max={100}
                  step={1}
                  className="w-full"
                />
              </div>
              <div className="flex items-center justify-between">
                <Label className="text-xs text-slate-600">智能模式</Label>
                <Switch
                  checked={isSmartMode}
                  onCheckedChange={setIsSmartMode}
                />
              </div>
            </div>
          </div>

          {/* 输出设置 */}
          <div className="mb-8">
            <h3 className="text-sm font-semibold text-slate-700 mb-4">输出设置</h3>
            <div className="space-y-4">
              <div>
                <Label className="text-xs text-slate-600 mb-2 block">分辨率</Label>
                <Select value={outputResolution} onValueChange={setOutputResolution}>
                  <SelectTrigger className="w-full h-9 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1920x1080">1920x1080 (HD)</SelectItem>
                    <SelectItem value="3840x2160">3840x2160 (4K)</SelectItem>
                    <SelectItem value="1280x720">1280x720 (720p)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="text-xs text-slate-600 mb-2 block">格式</Label>
                <Select value={outputFormat} onValueChange={setOutputFormat}>
                  <SelectTrigger className="w-full h-9 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mp4">MP4</SelectItem>
                    <SelectItem value="mov">MOV</SelectItem>
                    <SelectItem value="avi">AVI</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Button className="w-full bg-emerald-500 hover:bg-emerald-600">
              <Sparkles className="w-4 h-4 mr-2" />
              应用AI增强
            </Button>
            <Button variant="outline" className="w-full">
              <Save className="w-4 h-4 mr-2" />
              保存项目
            </Button>
            <Button variant="outline" className="w-full">
              <Download className="w-4 h-4 mr-2" />
              导出视频
            </Button>
          </div>
        </div>
      </div>
      </div>
    </>
  )
}
