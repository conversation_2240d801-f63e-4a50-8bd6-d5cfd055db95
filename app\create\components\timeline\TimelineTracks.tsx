"use client"

import React from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import {
  Video,
  Volume2,
  VolumeX,
  Type,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  MoreHorizontal,
  Plus
} from "lucide-react"
import { useVideoEditor } from '../../stores'

interface TimelineTracksProps {
  height: number
  onAddTrack?: (type: 'video' | 'audio' | 'subtitle') => void
}

export const TimelineTracks: React.FC<TimelineTracksProps> = ({
  height,
  onAddTrack
}) => {
  const {
    videoClips,
    audioClips,
    subtitleClips,
    ui
  } = useVideoEditor()

  const TRACK_HEIGHT = 60
  const RULER_HEIGHT = 40

  // 计算轨道信息
  const getTrackInfo = () => {
    const videoTracks = new Set(videoClips.map(c => c.track))
    const audioTracks = new Set(audioClips.map(c => c.track))
    const subtitleTracks = new Set(subtitleClips.map(c => c.track))

    const maxVideoTrack = Math.max(-1, ...Array.from(videoTracks))
    const maxAudioTrack = Math.max(-1, ...Array.from(audioTracks))
    const maxSubtitleTrack = Math.max(-1, ...Array.from(subtitleTracks))

    const tracks = []

    // 视频轨道
    for (let i = 0; i <= Math.max(maxVideoTrack, 2); i++) {
      tracks.push({
        id: `video-${i}`,
        type: 'video' as const,
        index: i,
        name: `视频 ${i + 1}`,
        hasClips: videoTracks.has(i),
        clipCount: videoClips.filter(c => c.track === i).length,
        muted: false,
        visible: true,
        locked: false
      })
    }

    // 音频轨道
    for (let i = 0; i <= Math.max(maxAudioTrack, 1); i++) {
      tracks.push({
        id: `audio-${i}`,
        type: 'audio' as const,
        index: i,
        name: `音频 ${i + 1}`,
        hasClips: audioTracks.has(i),
        clipCount: audioClips.filter(c => c.track === i).length,
        muted: false,
        visible: true,
        locked: false
      })
    }

    // 字幕轨道
    for (let i = 0; i <= Math.max(maxSubtitleTrack, 0); i++) {
      tracks.push({
        id: `subtitle-${i}`,
        type: 'subtitle' as const,
        index: i,
        name: `字幕 ${i + 1}`,
        hasClips: subtitleTracks.has(i),
        clipCount: subtitleClips.filter(c => c.track === i).length,
        muted: false,
        visible: true,
        locked: false
      })
    }

    return tracks
  }

  const tracks = getTrackInfo()

  const getTrackIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-4 h-4" />
      case 'audio':
        return <Volume2 className="w-4 h-4" />
      case 'subtitle':
        return <Type className="w-4 h-4" />
      default:
        return null
    }
  }

  const getTrackColor = (type: string) => {
    switch (type) {
      case 'video':
        return 'bg-emerald-50 border-emerald-200'
      case 'audio':
        return 'bg-blue-50 border-blue-200'
      case 'subtitle':
        return 'bg-purple-50 border-purple-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  return (
    <div className="timeline-tracks bg-gray-50 border-r border-gray-200" style={{ height }}>
      {/* 标尺区域占位 */}
      <div 
        className="border-b border-gray-200 bg-white flex items-center justify-center"
        style={{ height: RULER_HEIGHT }}
      >
        <span className="text-xs text-gray-500 font-medium">轨道</span>
      </div>

      {/* 轨道列表 */}
      <div className="overflow-y-auto" style={{ height: height - RULER_HEIGHT }}>
        {tracks.map((track, index) => (
          <div
            key={track.id}
            className={`
              flex items-center border-b border-gray-200 px-3 py-2
              ${getTrackColor(track.type)}
              ${track.hasClips ? 'bg-opacity-100' : 'bg-opacity-50'}
            `}
            style={{ height: TRACK_HEIGHT }}
          >
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              {/* 轨道图标 */}
              <div className={`
                w-8 h-8 rounded-md flex items-center justify-center
                ${track.type === 'video' ? 'bg-emerald-100 text-emerald-600' :
                  track.type === 'audio' ? 'bg-blue-100 text-blue-600' :
                  'bg-purple-100 text-purple-600'}
              `}>
                {getTrackIcon(track.type)}
              </div>

              {/* 轨道信息 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-800 truncate">
                    {track.name}
                  </span>
                  {track.hasClips && (
                    <Badge variant="secondary" className="text-xs px-1 py-0">
                      {track.clipCount}
                    </Badge>
                  )}
                </div>
                <div className="text-xs text-gray-500">
                  轨道 {track.index}
                </div>
              </div>
            </div>

            {/* 轨道控制 */}
            <div className="flex items-center space-x-1">
              {/* 可见性切换 */}
              <Button
                variant="ghost"
                size="sm"
                className="w-6 h-6 p-0"
                title={track.visible ? "隐藏轨道" : "显示轨道"}
              >
                {track.visible ? (
                  <Eye className="w-3 h-3" />
                ) : (
                  <EyeOff className="w-3 h-3 text-gray-400" />
                )}
              </Button>

              {/* 静音切换（音频轨道） */}
              {track.type === 'audio' && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-6 h-6 p-0"
                  title={track.muted ? "取消静音" : "静音"}
                >
                  {track.muted ? (
                    <VolumeX className="w-3 h-3 text-red-500" />
                  ) : (
                    <Volume2 className="w-3 h-3" />
                  )}
                </Button>
              )}

              {/* 锁定切换 */}
              <Button
                variant="ghost"
                size="sm"
                className="w-6 h-6 p-0"
                title={track.locked ? "解锁轨道" : "锁定轨道"}
              >
                {track.locked ? (
                  <Lock className="w-3 h-3 text-orange-500" />
                ) : (
                  <Unlock className="w-3 h-3" />
                )}
              </Button>

              {/* 更多选项 */}
              <Button
                variant="ghost"
                size="sm"
                className="w-6 h-6 p-0"
                title="更多选项"
              >
                <MoreHorizontal className="w-3 h-3" />
              </Button>
            </div>
          </div>
        ))}

        {/* 添加轨道按钮 */}
        <div className="p-3 space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onAddTrack?.('video')}
            className="w-full justify-start text-xs"
          >
            <Plus className="w-3 h-3 mr-2" />
            添加视频轨道
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onAddTrack?.('audio')}
            className="w-full justify-start text-xs"
          >
            <Plus className="w-3 h-3 mr-2" />
            添加音频轨道
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onAddTrack?.('subtitle')}
            className="w-full justify-start text-xs"
          >
            <Plus className="w-3 h-3 mr-2" />
            添加字幕轨道
          </Button>
        </div>
      </div>
    </div>
  )
}
