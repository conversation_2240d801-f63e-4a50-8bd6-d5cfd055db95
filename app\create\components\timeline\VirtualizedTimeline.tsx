"use client"

import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react'
import { Stage, Layer, Rect, Line, Text, Group } from 'react-konva'
import Konva from 'konva'
import { useVideoEditor } from '../../stores'
import { VirtualizationManager, globalPerformanceManager } from '../../utils/performance'
import { DraggableTimelineClip, TimelineDropZone } from '../dnd'

interface VirtualizedTimelineProps {
  width: number
  height: number
  onTimeChange?: (time: number) => void
  onClipSelect?: (clipId: string) => void
  onClipMove?: (clipId: string, newStartTime: number, newTrack: number) => void
  pixelsPerSecond?: number
  trackHeight?: number
  rulerHeight?: number
  showPerformanceStats?: boolean
}

export const VirtualizedTimeline: React.FC<VirtualizedTimelineProps> = ({
  width,
  height,
  onTimeChange,
  onClipSelect,
  onClipMove,
  pixelsPerSecond = 20,
  trackHeight = 60,
  rulerHeight = 40,
  showPerformanceStats = false
}) => {
  const stageRef = useRef<Konva.Stage>(null)
  const [scrollX, setScrollX] = useState(0)
  const [scrollY, setScrollY] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [performanceStats, setPerformanceStats] = useState<any>({})

  const {
    videoClips,
    audioClips,
    subtitleClips,
    timeline,
    ui,
    setCurrentTime
  } = useVideoEditor()

  // 虚拟化管理器
  const virtualizationManager = useMemo(() => {
    return new VirtualizationManager({
      itemHeight: trackHeight,
      containerHeight: height - rulerHeight,
      overscan: 2
    })
  }, [trackHeight, height, rulerHeight])

  // 合并所有片段并按轨道分组
  const allClips = useMemo(() => {
    const clips = [
      ...videoClips.map(c => ({ ...c, type: 'video' as const })),
      ...audioClips.map(c => ({ ...c, type: 'audio' as const })),
      ...subtitleClips.map(c => ({ ...c, type: 'subtitle' as const }))
    ]
    
    // 按轨道分组
    const trackGroups = new Map<number, any[]>()
    clips.forEach(clip => {
      if (!trackGroups.has(clip.track)) {
        trackGroups.set(clip.track, [])
      }
      trackGroups.get(clip.track)!.push(clip)
    })

    return trackGroups
  }, [videoClips, audioClips, subtitleClips])

  // 计算可见时间范围
  const visibleTimeRange = useMemo(() => {
    const startTime = scrollX / pixelsPerSecond
    const endTime = (scrollX + width) / pixelsPerSecond
    return { startTime, endTime }
  }, [scrollX, width, pixelsPerSecond])

  // 计算可见轨道范围
  const visibleTrackRange = useMemo(() => {
    const maxTrack = Math.max(...Array.from(allClips.keys()), 5)
    virtualizationManager.updateViewport(scrollY, maxTrack + 1)
    return virtualizationManager.getVisibleRange()
  }, [scrollY, allClips, virtualizationManager])

  // 过滤可见片段
  const visibleClips = useMemo(() => {
    globalPerformanceManager.monitor.start('filterVisibleClips')
    
    const clips: any[] = []
    const { startTime, endTime } = visibleTimeRange
    const { start: startTrack, end: endTrack } = visibleTrackRange

    for (let track = startTrack; track <= endTrack; track++) {
      const trackClips = allClips.get(track) || []
      
      for (const clip of trackClips) {
        // 检查时间重叠
        if (clip.endTime >= startTime && clip.startTime <= endTime) {
          clips.push({
            ...clip,
            x: (clip.startTime - startTime) * pixelsPerSecond,
            y: (track - startTrack) * trackHeight + rulerHeight,
            width: (clip.endTime - clip.startTime) * pixelsPerSecond,
            height: trackHeight - 4
          })
        }
      }
    }

    globalPerformanceManager.monitor.end('filterVisibleClips')
    return clips
  }, [allClips, visibleTimeRange, visibleTrackRange, pixelsPerSecond, trackHeight, rulerHeight])

  // 渲染时间标尺
  const renderRuler = useCallback(() => {
    const elements = []
    const { startTime, endTime } = visibleTimeRange
    
    // 计算合适的时间间隔
    const timeSpan = endTime - startTime
    let interval = 1 // 秒
    
    if (timeSpan > 300) interval = 60      // 1分钟
    else if (timeSpan > 60) interval = 10  // 10秒
    else if (timeSpan > 30) interval = 5   // 5秒
    else if (timeSpan > 10) interval = 1   // 1秒
    else interval = 0.5                    // 0.5秒

    const startMark = Math.floor(startTime / interval) * interval
    const endMark = Math.ceil(endTime / interval) * interval

    for (let time = startMark; time <= endMark; time += interval) {
      const x = (time - startTime) * pixelsPerSecond
      
      if (x >= 0 && x <= width) {
        // 主刻度线
        elements.push(
          <Line
            key={`ruler-line-${time}`}
            points={[x, rulerHeight - 10, x, rulerHeight]}
            stroke="#666666"
            strokeWidth={1}
          />
        )

        // 时间标签
        const minutes = Math.floor(time / 60)
        const seconds = Math.floor(time % 60)
        const timeLabel = `${minutes}:${seconds.toString().padStart(2, '0')}`
        
        elements.push(
          <Text
            key={`ruler-text-${time}`}
            x={x + 2}
            y={rulerHeight - 25}
            text={timeLabel}
            fontSize={10}
            fill="#666666"
            fontFamily="Arial"
          />
        )

        // 次刻度线
        if (interval >= 5) {
          for (let subTime = time + interval / 5; subTime < time + interval; subTime += interval / 5) {
            const subX = (subTime - startTime) * pixelsPerSecond
            if (subX >= 0 && subX <= width) {
              elements.push(
                <Line
                  key={`ruler-subline-${subTime}`}
                  points={[subX, rulerHeight - 5, subX, rulerHeight]}
                  stroke="#999999"
                  strokeWidth={0.5}
                />
              )
            }
          }
        }
      }
    }

    return elements
  }, [visibleTimeRange, pixelsPerSecond, width, rulerHeight])

  // 渲染播放头
  const renderPlayhead = useCallback(() => {
    const playheadX = (timeline.currentTime - visibleTimeRange.startTime) * pixelsPerSecond
    
    if (playheadX >= 0 && playheadX <= width) {
      return (
        <Group key="playhead">
          <Line
            points={[playheadX, 0, playheadX, height]}
            stroke="#ff4444"
            strokeWidth={2}
          />
          <Rect
            x={playheadX - 6}
            y={0}
            width={12}
            height={rulerHeight}
            fill="#ff4444"
            cornerRadius={2}
          />
        </Group>
      )
    }
    return null
  }, [timeline.currentTime, visibleTimeRange, pixelsPerSecond, width, height, rulerHeight])

  // 渲染网格
  const renderGrid = useCallback(() => {
    if (!ui.showGrid) return []

    const elements = []
    const { startTime, endTime } = visibleTimeRange
    const { start: startTrack, end: endTrack } = visibleTrackRange

    // 垂直网格线（时间）
    const timeInterval = 5 // 5秒间隔
    const startGridTime = Math.floor(startTime / timeInterval) * timeInterval
    
    for (let time = startGridTime; time <= endTime; time += timeInterval) {
      const x = (time - startTime) * pixelsPerSecond
      if (x >= 0 && x <= width) {
        elements.push(
          <Line
            key={`grid-v-${time}`}
            points={[x, rulerHeight, x, height]}
            stroke="#e0e0e0"
            strokeWidth={0.5}
            opacity={0.5}
          />
        )
      }
    }

    // 水平网格线（轨道）
    for (let track = startTrack; track <= endTrack + 1; track++) {
      const y = (track - startTrack) * trackHeight + rulerHeight
      elements.push(
        <Line
          key={`grid-h-${track}`}
          points={[0, y, width, y]}
          stroke="#e0e0e0"
          strokeWidth={0.5}
          opacity={0.5}
        />
      )
    }

    return elements
  }, [ui.showGrid, visibleTimeRange, visibleTrackRange, pixelsPerSecond, width, height, rulerHeight, trackHeight])

  // 处理滚轮事件
  const handleWheel = useCallback((e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault()
    
    if (e.evt.ctrlKey) {
      // 缩放
      const scaleBy = 1.1
      const oldScale = timeline.zoomLevel
      const newScale = e.evt.deltaY > 0 ? oldScale / scaleBy : oldScale * scaleBy
      const clampedScale = Math.max(0.1, Math.min(newScale, 10))
      
      console.log('缩放到:', clampedScale)
    } else {
      // 滚动
      const deltaX = e.evt.deltaX || 0
      const deltaY = e.evt.deltaY || 0
      
      setScrollX(prev => Math.max(0, prev + deltaX))
      setScrollY(prev => Math.max(0, prev + deltaY))
    }
  }, [timeline.zoomLevel])

  // 处理点击事件
  const handleStageClick = useCallback((e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = e.target.getStage()
    if (!stage) return

    const pos = stage.getPointerPosition()
    if (!pos) return

    // 如果点击在标尺区域，设置播放时间
    if (pos.y <= rulerHeight) {
      const clickTime = visibleTimeRange.startTime + pos.x / pixelsPerSecond
      const clampedTime = Math.max(0, Math.min(clickTime, timeline.duration))
      setCurrentTime(clampedTime)
      onTimeChange?.(clampedTime)
    }
  }, [visibleTimeRange, pixelsPerSecond, timeline.duration, setCurrentTime, onTimeChange, rulerHeight])

  // 性能统计更新
  useEffect(() => {
    if (!showPerformanceStats) return

    const updateStats = () => {
      const fps = globalPerformanceManager.fps.update()
      const memoryStats = globalPerformanceManager.memory.getStats()
      const monitorStats = globalPerformanceManager.monitor.getAllStats()
      
      setPerformanceStats({
        fps: fps.toFixed(1),
        memory: (memoryStats.memoryUsage / 1024 / 1024).toFixed(1) + ' MB',
        visibleClips: visibleClips.length,
        totalClips: videoClips.length + audioClips.length + subtitleClips.length,
        renderTime: monitorStats.filterVisibleClips?.latest?.toFixed(2) || '0'
      })
    }

    const interval = setInterval(updateStats, 1000)
    return () => clearInterval(interval)
  }, [showPerformanceStats, visibleClips.length, videoClips.length, audioClips.length, subtitleClips.length])

  return (
    <div className="virtualized-timeline relative">
      <Stage
        ref={stageRef}
        width={width}
        height={height}
        onWheel={handleWheel}
        onClick={handleStageClick}
        draggable={false}
      >
        <Layer>
          {/* 背景 */}
          <Rect
            x={0}
            y={0}
            width={width}
            height={height}
            fill="#ffffff"
          />

          {/* 标尺背景 */}
          <Rect
            x={0}
            y={0}
            width={width}
            height={rulerHeight}
            fill="#f5f5f5"
            stroke="#e0e0e0"
            strokeWidth={1}
          />

          {/* 网格 */}
          {renderGrid()}

          {/* 时间标尺 */}
          {renderRuler()}

          {/* 播放头 */}
          {renderPlayhead()}
        </Layer>

        <Layer>
          {/* 渲染可见片段 */}
          {visibleClips.map((clip) => (
            <DraggableTimelineClip
              key={clip.id}
              clip={clip}
              x={clip.x}
              y={clip.y}
              width={clip.width}
              height={clip.height}
              isSelected={timeline.selectedClips.includes(clip.id)}
              onSelect={onClipSelect || (() => {})}
              onMove={onClipMove || (() => {})}
              pixelsPerSecond={pixelsPerSecond}
              trackHeight={trackHeight}
            />
          ))}
        </Layer>
      </Stage>

      {/* 性能统计显示 */}
      {showPerformanceStats && (
        <div className="absolute top-2 right-2 bg-black/80 text-white text-xs p-2 rounded">
          <div>FPS: {performanceStats.fps}</div>
          <div>内存: {performanceStats.memory}</div>
          <div>可见片段: {performanceStats.visibleClips}/{performanceStats.totalClips}</div>
          <div>渲染时间: {performanceStats.renderTime}ms</div>
        </div>
      )}

      {/* 滚动条 */}
      <div className="absolute bottom-0 left-0 right-0 h-4 bg-gray-200 border-t">
        <div
          className="h-full bg-blue-500 cursor-pointer"
          style={{
            width: `${Math.min(100, (width / (timeline.duration * pixelsPerSecond)) * 100)}%`,
            marginLeft: `${(scrollX / (timeline.duration * pixelsPerSecond)) * 100}%`
          }}
        />
      </div>
    </div>
  )
}
