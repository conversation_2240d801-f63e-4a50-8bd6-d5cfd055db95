# 视频编辑器集成指南

## 🎯 概述

本指南介绍如何将新的 AI 视频编辑器集成到现有的 AI 剧本创作流程中。

## 🚀 快速开始

### 1. 基础集成

最简单的集成方式是使用 `IntegratedVideoEditor` 组件：

```tsx
import { IntegratedVideoEditor } from './create/components'

function MyVideoEditingPage() {
  return (
    <IntegratedVideoEditor
      projectId="your-project-id"
      userId="your-user-id"
      shotsWithImages={yourShotsData}
      onSave={(projectData) => {
        // 保存项目数据
        console.log('保存项目:', projectData)
      }}
      onExport={(videoData, filename) => {
        // 处理视频导出
        const blob = new Blob([videoData])
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        link.click()
      }}
    />
  )
}
```

### 2. 流程集成

如果需要集成到现有的多步骤流程中，使用 `VideoEditingIntegrationExample`：

```tsx
import { VideoEditingIntegrationExample } from './create/components'

function MyCreationFlow() {
  const [currentStep, setCurrentStep] = useState(0)
  
  return (
    <VideoEditingIntegrationExample
      projectId="project-123"
      userId="user-456"
      scriptData={scriptData}
      shotsWithImages={shotsWithImages}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onSave={handleSave}
      onComplete={handleComplete}
    />
  )
}
```

## 📊 数据格式

### shotsWithImages 数据结构

```typescript
interface ShotWithImages {
  shotId?: string          // 镜头ID（用于视频缓存查询）
  shotNumber?: number      // 镜头编号（用于图片缓存查询）
  videoUrl?: string        // 视频URL（如果有的话）
  images?: Array<{         // 图片数组
    url: string
    isPrimary?: boolean    // 是否为主图片
  }>
}
```

### 缓存数据结构

视频编辑器会自动查询以下 API 端点的缓存数据：

- `POST /api/video-records/cached` - 视频缓存
- `POST /api/image-records/cached` - 图片缓存

## 🔧 配置选项

### IntegratedVideoEditor Props

```typescript
interface IntegratedVideoEditorProps {
  projectId?: string                    // 项目ID
  userId?: string                       // 用户ID
  scriptData?: any                      // 剧本数据
  shotsWithImages?: ShotWithImages[]    // 场景镜头数据
  onSave?: (projectData: any) => void   // 保存回调
  onExport?: (videoData: Uint8Array, filename: string) => void  // 导出回调
  className?: string                    // 样式类名
}
```

## 🎨 智能缓存切换

视频编辑器具有智能缓存切换功能：

1. **优先级**: 视频缓存 > 图片缓存 > 原始数据
2. **自动检测**: 自动检测可用的缓存类型
3. **状态指示**: 可视化显示缓存状态
4. **实时刷新**: 支持手动刷新缓存数据

### 缓存状态指示器

- 🎥 **蓝色徽章**: 使用视频缓存
- 🖼️ **绿色徽章**: 使用图片缓存  
- 📝 **灰色徽章**: 使用原始数据

## 🔄 状态管理

视频编辑器使用 Zustand 进行状态管理，支持：

- ✅ 撤销/重做操作
- ✅ 批量操作
- ✅ 实时状态同步
- ✅ 持久化存储

### 获取编辑器状态

```tsx
import { useVideoEditor } from './create/stores'

function MyComponent() {
  const {
    videoClips,
    audioClips,
    subtitleClips,
    timeline,
    actions
  } = useVideoEditor()
  
  // 使用状态...
}
```

## 🎬 核心功能

### 1. 智能剪辑

- 自动从场景数据生成视频片段
- 智能缓存数据利用
- 一键生成时间轴

### 2. 多轨道编辑

- 视频轨道
- 音频轨道  
- 字幕轨道
- 拖拽操作

### 3. 实时预览

- 多轨道合成预览
- 同步播放控制
- 全屏预览支持

### 4. 性能优化

- 虚拟化渲染
- 智能内存管理
- 异步处理

## 🔌 API 集成

### 缓存查询 API

视频编辑器会自动调用以下 API：

```typescript
// 查询视频缓存
POST /api/video-records/cached
{
  "projectId": "project-123",
  "userId": "user-456", 
  "shotIds": ["shot-1", "shot-2"]
}

// 查询图片缓存
POST /api/image-records/cached
{
  "projectId": "project-123",
  "userId": "user-456",
  "shotNumbers": [1, 2, 3]
}
```

### 响应格式

```typescript
{
  "cached": true,
  "tasks": {
    "shot-1": [{
      "status": "completed",
      "generated_videos": [{
        "video_url": "https://example.com/video.mp4"
      }]
    }]
  },
  "totalCompletedVideos": 5
}
```

## 🎨 样式自定义

### CSS 变量

```css
:root {
  --video-editor-primary: #3b82f6;
  --video-editor-secondary: #10b981;
  --video-editor-background: #ffffff;
  --video-editor-border: #e5e7eb;
}
```

### 主题切换

支持深色模式和高对比度模式：

```css
@media (prefers-color-scheme: dark) {
  :root {
    --video-editor-background: #0a0a0a;
    --video-editor-border: #262626;
  }
}
```

## 📱 响应式支持

视频编辑器完全支持响应式设计：

- **移动端**: 触摸优化，侧边栏抽屉
- **平板**: 可折叠面板，手势支持
- **桌面**: 完整功能，多窗口支持

## 🔍 调试和监控

### 开发模式

```tsx
<IntegratedVideoEditor
  // ... 其他 props
  showPerformanceStats={true}  // 显示性能统计
/>
```

### 控制台日志

视频编辑器会输出详细的调试信息：

```
🎬 [SMART-CLIPS] 开始智能生成视频片段
🎬 [SMART-CLIPS] 智能生成完成，共 5 个片段
🎬 [SMART-EDITING] 智能剪辑完成，生成了 5 个视频片段
```

## 🚨 常见问题

### Q: 缓存数据不显示？
A: 检查 API 端点是否正确，确保返回正确的数据格式。

### Q: 视频预览不工作？
A: 确保视频 URL 可访问，检查 CORS 设置。

### Q: 性能问题？
A: 启用虚拟化渲染，限制同时加载的媒体文件数量。

### Q: 如何自定义样式？
A: 使用 CSS 变量或传入自定义 className。

## 📚 更多资源

- [完整 API 文档](./API_REFERENCE.md)
- [组件文档](./COMPONENTS.md)
- [性能优化指南](./PERFORMANCE.md)
- [故障排除](./TROUBLESHOOTING.md)

## 🤝 支持

如有问题，请：

1. 查看控制台日志
2. 检查网络请求
3. 验证数据格式
4. 提交 Issue

---

**祝您使用愉快！** 🎉
