// 性能优化工具集

// 虚拟化渲染管理器
export class VirtualizationManager {
  private viewportStart: number = 0
  private viewportEnd: number = 0
  private itemHeight: number = 60
  private containerHeight: number = 400
  private totalItems: number = 0
  private overscan: number = 5

  constructor(options: {
    itemHeight?: number
    containerHeight?: number
    overscan?: number
  } = {}) {
    this.itemHeight = options.itemHeight || 60
    this.containerHeight = options.containerHeight || 400
    this.overscan = options.overscan || 5
  }

  // 更新视口范围
  updateViewport(scrollTop: number, totalItems: number): void {
    this.totalItems = totalItems
    
    const visibleStart = Math.floor(scrollTop / this.itemHeight)
    const visibleEnd = Math.min(
      totalItems - 1,
      Math.ceil((scrollTop + this.containerHeight) / this.itemHeight)
    )

    this.viewportStart = Math.max(0, visibleStart - this.overscan)
    this.viewportEnd = Math.min(totalItems - 1, visibleEnd + this.overscan)
  }

  // 获取可见项目范围
  getVisibleRange(): { start: number; end: number; total: number } {
    return {
      start: this.viewportStart,
      end: this.viewportEnd,
      total: this.totalItems
    }
  }

  // 计算项目位置
  getItemPosition(index: number): { top: number; height: number } {
    return {
      top: index * this.itemHeight,
      height: this.itemHeight
    }
  }

  // 计算总高度
  getTotalHeight(): number {
    return this.totalItems * this.itemHeight
  }

  // 检查项目是否在视口内
  isItemVisible(index: number): boolean {
    return index >= this.viewportStart && index <= this.viewportEnd
  }
}

// 内存管理器
export class MemoryManager {
  private cache = new Map<string, any>()
  private maxCacheSize: number = 100
  private accessTimes = new Map<string, number>()

  constructor(maxCacheSize: number = 100) {
    this.maxCacheSize = maxCacheSize
  }

  // 设置缓存项
  set(key: string, value: any): void {
    // 如果缓存已满，清理最少使用的项
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLeastRecentlyUsed()
    }

    this.cache.set(key, value)
    this.accessTimes.set(key, Date.now())
  }

  // 获取缓存项
  get(key: string): any {
    const value = this.cache.get(key)
    if (value !== undefined) {
      this.accessTimes.set(key, Date.now())
    }
    return value
  }

  // 检查是否存在
  has(key: string): boolean {
    return this.cache.has(key)
  }

  // 删除缓存项
  delete(key: string): boolean {
    this.accessTimes.delete(key)
    return this.cache.delete(key)
  }

  // 清空缓存
  clear(): void {
    this.cache.clear()
    this.accessTimes.clear()
  }

  // 清理最少使用的项
  private evictLeastRecentlyUsed(): void {
    let oldestKey = ''
    let oldestTime = Date.now()

    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
    }
  }

  // 获取缓存统计
  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    memoryUsage: number
  } {
    const memoryUsage = this.estimateMemoryUsage()
    
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: 0, // 需要额外跟踪命中率
      memoryUsage
    }
  }

  // 估算内存使用量
  private estimateMemoryUsage(): number {
    let totalSize = 0
    
    for (const [key, value] of this.cache) {
      totalSize += this.estimateObjectSize(key) + this.estimateObjectSize(value)
    }
    
    return totalSize
  }

  // 估算对象大小
  private estimateObjectSize(obj: any): number {
    if (obj === null || obj === undefined) return 0
    
    if (typeof obj === 'string') return obj.length * 2
    if (typeof obj === 'number') return 8
    if (typeof obj === 'boolean') return 4
    
    if (obj instanceof ArrayBuffer) return obj.byteLength
    if (obj instanceof Uint8Array) return obj.byteLength
    
    // 对象和数组的粗略估算
    if (typeof obj === 'object') {
      return JSON.stringify(obj).length * 2
    }
    
    return 0
  }
}

// 性能监控器
export class PerformanceMonitor {
  private metrics = new Map<string, number[]>()
  private startTimes = new Map<string, number>()
  private maxSamples: number = 100

  constructor(maxSamples: number = 100) {
    this.maxSamples = maxSamples
  }

  // 开始计时
  start(name: string): void {
    this.startTimes.set(name, performance.now())
  }

  // 结束计时
  end(name: string): number {
    const startTime = this.startTimes.get(name)
    if (startTime === undefined) {
      console.warn(`Performance timer '${name}' was not started`)
      return 0
    }

    const duration = performance.now() - startTime
    this.addMetric(name, duration)
    this.startTimes.delete(name)
    
    return duration
  }

  // 添加指标
  addMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }

    const samples = this.metrics.get(name)!
    samples.push(value)

    // 保持样本数量在限制内
    if (samples.length > this.maxSamples) {
      samples.shift()
    }
  }

  // 获取指标统计
  getStats(name: string): {
    count: number
    min: number
    max: number
    avg: number
    latest: number
  } | null {
    const samples = this.metrics.get(name)
    if (!samples || samples.length === 0) {
      return null
    }

    const min = Math.min(...samples)
    const max = Math.max(...samples)
    const avg = samples.reduce((sum, val) => sum + val, 0) / samples.length
    const latest = samples[samples.length - 1]

    return { count: samples.length, min, max, avg, latest }
  }

  // 获取所有指标
  getAllStats(): Record<string, any> {
    const stats: Record<string, any> = {}
    
    for (const name of this.metrics.keys()) {
      stats[name] = this.getStats(name)
    }
    
    return stats
  }

  // 清理指标
  clear(name?: string): void {
    if (name) {
      this.metrics.delete(name)
      this.startTimes.delete(name)
    } else {
      this.metrics.clear()
      this.startTimes.clear()
    }
  }
}

// 帧率监控器
export class FPSMonitor {
  private frameCount: number = 0
  private lastTime: number = 0
  private fps: number = 0
  private samples: number[] = []
  private maxSamples: number = 60

  constructor(maxSamples: number = 60) {
    this.maxSamples = maxSamples
    this.lastTime = performance.now()
  }

  // 更新帧率
  update(): number {
    const now = performance.now()
    this.frameCount++

    if (now - this.lastTime >= 1000) {
      this.fps = this.frameCount * 1000 / (now - this.lastTime)
      this.samples.push(this.fps)
      
      if (this.samples.length > this.maxSamples) {
        this.samples.shift()
      }
      
      this.frameCount = 0
      this.lastTime = now
    }

    return this.fps
  }

  // 获取当前帧率
  getFPS(): number {
    return this.fps
  }

  // 获取平均帧率
  getAverageFPS(): number {
    if (this.samples.length === 0) return 0
    return this.samples.reduce((sum, fps) => sum + fps, 0) / this.samples.length
  }

  // 获取帧率统计
  getStats(): {
    current: number
    average: number
    min: number
    max: number
    samples: number
  } {
    if (this.samples.length === 0) {
      return { current: 0, average: 0, min: 0, max: 0, samples: 0 }
    }

    return {
      current: this.fps,
      average: this.getAverageFPS(),
      min: Math.min(...this.samples),
      max: Math.max(...this.samples),
      samples: this.samples.length
    }
  }

  // 重置监控器
  reset(): void {
    this.frameCount = 0
    this.lastTime = performance.now()
    this.fps = 0
    this.samples = []
  }
}

// 资源池管理器
export class ResourcePool<T> {
  private available: T[] = []
  private inUse = new Set<T>()
  private factory: () => T
  private reset?: (item: T) => void
  private maxSize: number

  constructor(
    factory: () => T,
    options: {
      initialSize?: number
      maxSize?: number
      reset?: (item: T) => void
    } = {}
  ) {
    this.factory = factory
    this.reset = options.reset
    this.maxSize = options.maxSize || 50

    // 预创建初始对象
    const initialSize = options.initialSize || 5
    for (let i = 0; i < initialSize; i++) {
      this.available.push(this.factory())
    }
  }

  // 获取资源
  acquire(): T {
    let item: T

    if (this.available.length > 0) {
      item = this.available.pop()!
    } else {
      item = this.factory()
    }

    this.inUse.add(item)
    return item
  }

  // 释放资源
  release(item: T): void {
    if (!this.inUse.has(item)) {
      console.warn('Attempting to release an item that is not in use')
      return
    }

    this.inUse.delete(item)

    // 重置对象状态
    if (this.reset) {
      this.reset(item)
    }

    // 如果池未满，则返回池中
    if (this.available.length < this.maxSize) {
      this.available.push(item)
    }
  }

  // 获取池统计
  getStats(): {
    available: number
    inUse: number
    total: number
    maxSize: number
  } {
    return {
      available: this.available.length,
      inUse: this.inUse.size,
      total: this.available.length + this.inUse.size,
      maxSize: this.maxSize
    }
  }

  // 清空池
  clear(): void {
    this.available = []
    this.inUse.clear()
  }
}

// 批处理管理器
export class BatchProcessor<T> {
  private queue: T[] = []
  private processor: (items: T[]) => Promise<void>
  private batchSize: number
  private delay: number
  private timeoutId: NodeJS.Timeout | null = null

  constructor(
    processor: (items: T[]) => Promise<void>,
    options: {
      batchSize?: number
      delay?: number
    } = {}
  ) {
    this.processor = processor
    this.batchSize = options.batchSize || 10
    this.delay = options.delay || 100
  }

  // 添加项目到队列
  add(item: T): void {
    this.queue.push(item)

    // 如果达到批处理大小，立即处理
    if (this.queue.length >= this.batchSize) {
      this.flush()
    } else {
      // 否则设置延迟处理
      this.scheduleFlush()
    }
  }

  // 立即处理队列
  async flush(): Promise<void> {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
      this.timeoutId = null
    }

    if (this.queue.length === 0) return

    const items = this.queue.splice(0)
    await this.processor(items)
  }

  // 调度延迟处理
  private scheduleFlush(): void {
    if (this.timeoutId) return

    this.timeoutId = setTimeout(() => {
      this.timeoutId = null
      this.flush()
    }, this.delay)
  }

  // 获取队列状态
  getQueueSize(): number {
    return this.queue.length
  }

  // 清空队列
  clear(): void {
    this.queue = []
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
      this.timeoutId = null
    }
  }
}

// 全局性能管理器实例
export const globalPerformanceManager = {
  memory: new MemoryManager(200),
  monitor: new PerformanceMonitor(200),
  fps: new FPSMonitor(120),
  
  // 获取系统性能信息
  getSystemInfo: () => {
    const info: any = {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      hardwareConcurrency: navigator.hardwareConcurrency,
      deviceMemory: (navigator as any).deviceMemory,
      connection: (navigator as any).connection
    }

    // 内存信息
    if ('memory' in performance) {
      const memory = (performance as any).memory
      info.memory = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      }
    }

    return info
  },

  // 性能建议
  getPerformanceRecommendations: () => {
    const recommendations: string[] = []
    const systemInfo = globalPerformanceManager.getSystemInfo()
    const memoryStats = globalPerformanceManager.memory.getStats()
    const fpsStats = globalPerformanceManager.fps.getStats()

    // 内存建议
    if (memoryStats.memoryUsage > 50 * 1024 * 1024) { // 50MB
      recommendations.push('内存使用量较高，建议清理缓存或减少同时处理的媒体文件数量')
    }

    // 帧率建议
    if (fpsStats.average < 30) {
      recommendations.push('渲染帧率较低，建议降低预览质量或关闭实时特效')
    }

    // 硬件建议
    if (systemInfo.hardwareConcurrency < 4) {
      recommendations.push('CPU核心数较少，建议避免同时进行多个计算密集型操作')
    }

    if (systemInfo.deviceMemory && systemInfo.deviceMemory < 4) {
      recommendations.push('设备内存较少，建议限制同时加载的媒体文件数量')
    }

    return recommendations
  }
}
