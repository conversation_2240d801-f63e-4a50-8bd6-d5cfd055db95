import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { unstable_cache } from 'next/cache'

// 缓存函数：获取图片生成任务记录
const getCachedImageRecords = unstable_cache(
  async (projectId: string, userId: string, shotNumbers: number[]) => {
    console.log(`🔍 [CACHED-IMAGE-QUERY] 查询项目 ${projectId} 的图片生成任务`)
    console.log(`🔍 [CACHED-IMAGE-QUERY] 镜头号列表:`, shotNumbers)

    try {
      // 查询图片生成任务，按镜头号分组
      const imageTasks = await prisma.image_generation_tasks.findMany({
        where: {
          project_id: projectId,
          user_id: userId,
          // 使用task_name匹配镜头号（格式：镜头 X）
          OR: shotNumbers.map(shotNumber => ({
            task_name: {
              contains: `镜头 ${shotNumber}`
            }
          }))
        },
        include: {
          generated_images: {
            orderBy: {
              created_at: 'desc'
            }
          }
        },
        orderBy: {
          created_at: 'desc'
        }
      })

      console.log(`✅ [CACHED-IMAGE-QUERY] 找到 ${imageTasks.length} 个图片生成任务`)

      // 按镜头号组织任务数据
      const tasksByShot: Record<number, any[]> = {}
      let totalCompletedTasks = 0
      let totalCompletedImages = 0

      // 初始化所有镜头号
      shotNumbers.forEach(shotNumber => {
        tasksByShot[shotNumber] = []
      })

      // 处理每个任务
      imageTasks.forEach(task => {
        // 从task_name中提取镜头号
        const shotMatch = task.task_name?.match(/镜头 (\d+)/)
        if (shotMatch) {
          const shotNumber = parseInt(shotMatch[1])
          
          if (shotNumbers.includes(shotNumber)) {
            const taskData = {
              id: task.id,
              status: task.status,
              task_name: task.task_name,
              prompt: task.prompt,
              negative_prompt: task.negative_prompt,
              model_name: task.model_name,
              aspect_ratio: task.aspect_ratio,
              image_count: task.image_count,
              kling_task_id: task.kling_task_id,
              created_at: task.created_at,
              started_at: task.started_at,
              completed_at: task.completed_at,
              error_message: task.error_message,
              error_code: task.error_code,
              generated_images: task.generated_images.map(img => ({
                id: img.id,
                image_url: img.image_url,
                cdn_url: img.cdn_url,
                image_filename: img.image_filename,
                image_width: img.image_width,
                image_height: img.image_height,
                image_format: img.image_format,
                is_primary: img.is_primary,
                actual_prompt: img.actual_prompt,
                quality_score: img.quality_score,
                created_at: img.created_at
              }))
            }

            tasksByShot[shotNumber].push(taskData)

            // 统计完成的任务和图片
            if (task.status === 'completed') {
              totalCompletedTasks++
              totalCompletedImages += task.generated_images.length
            }
          }
        }
      })

      const result = {
        tasks: tasksByShot,
        totalShots: shotNumbers.length,
        totalCompletedTasks,
        totalCompletedImages,
        hasCompleteImages: totalCompletedImages > 0,
        queriedAt: new Date().toISOString()
      }

      console.log(`✅ [CACHED-IMAGE-QUERY] 缓存查询完成:`, {
        totalShots: result.totalShots,
        totalCompletedTasks: result.totalCompletedTasks,
        totalCompletedImages: result.totalCompletedImages
      })

      return result

    } catch (error) {
      console.error('❌ [CACHED-IMAGE-QUERY] 数据库查询失败:', error)
      throw error
    }
  },
  ['cached-image-records'],
  {
    revalidate: 300, // 5分钟缓存
    tags: ['image-records']
  }
)

// POST方法用于查询缓存的图片生成任务
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { projectId, userId, shotNumbers } = body

    console.log('🔍 [CACHED-IMAGE-QUERY] POST请求参数:', { projectId, userId, shotNumbers })

    if (!projectId || !userId || !shotNumbers || !Array.isArray(shotNumbers)) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数: projectId, userId, shotNumbers'
      }, { status: 400 })
    }

    if (shotNumbers.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'shotNumbers不能为空'
      }, { status: 400 })
    }

    console.log(`🔍 [CACHED-IMAGE-QUERY] 使用缓存查询 ${shotNumbers.length} 个镜头的图片任务:`, shotNumbers)

    // 创建缓存键
    const sortedShotNumbers = [...shotNumbers].sort((a, b) => a - b)
    const cacheKey = `${projectId}-${userId}-${sortedShotNumbers.join(',')}`

    // 使用缓存函数查询数据
    const result = await getCachedImageRecords(projectId, userId, sortedShotNumbers)

    console.log(`✅ [CACHED-IMAGE-QUERY] 缓存查询完成，找到 ${result.totalShots} 个镜头的任务`)

    return NextResponse.json({
      success: true,
      ...result,
      cached: true,
      cacheKey,
      cacheExpiry: new Date(Date.now() + 300 * 1000).toISOString()
    })

  } catch (error) {
    console.error('❌ [CACHED-IMAGE-QUERY] POST缓存查询失败:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '缓存查询失败',
      cached: false
    }, { status: 500 })
  }
}

// GET方法支持查询参数格式（兼容性）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    
    // 处理清除缓存请求
    if (action === 'clear-cache') {
      console.log('🗑️ [CACHED-IMAGE-QUERY] 请求清除缓存')
      
      return NextResponse.json({
        success: true,
        message: '缓存清除请求已处理',
        timestamp: new Date().toISOString()
      })
    }
    
    // 处理查询请求（兼容GET格式）
    const projectId = searchParams.get('projectId')
    const userId = searchParams.get('userId')
    const shotNumbersParam = searchParams.get('shotNumbers')
    
    console.log('🔍 [CACHED-IMAGE-QUERY] GET请求参数:', { projectId, userId, shotNumbersParam })
    
    if (!projectId || !userId || !shotNumbersParam) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数: projectId, userId, shotNumbers'
      }, { status: 400 })
    }
    
    const shotNumbers = shotNumbersParam.split(',').map(num => parseInt(num.trim())).filter(num => !isNaN(num))
    
    if (shotNumbers.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'shotNumbers不能为空或格式不正确'
      }, { status: 400 })
    }
    
    console.log(`🔍 [CACHED-IMAGE-QUERY] 使用缓存查询 ${shotNumbers.length} 个镜头的图片任务:`, shotNumbers)
    
    // 创建缓存键
    const sortedShotNumbers = [...shotNumbers].sort((a, b) => a - b)
    const cacheKey = `${projectId}-${userId}-${sortedShotNumbers.join(',')}`
    
    // 使用缓存函数查询数据
    const result = await getCachedImageRecords(projectId, userId, sortedShotNumbers)
    
    console.log(`✅ [CACHED-IMAGE-QUERY] 缓存查询完成，找到 ${result.totalShots} 个镜头的任务`)
    
    return NextResponse.json({
      success: true,
      ...result,
      cached: true,
      cacheKey,
      cacheExpiry: new Date(Date.now() + 300 * 1000).toISOString()
    })
    
  } catch (error) {
    console.error('❌ [CACHED-IMAGE-QUERY] GET缓存查询失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '缓存查询失败',
      cached: false
    }, { status: 500 })
  }
}
