# 视频记录缓存实现测试指南

## 测试步骤

### 1. 启动应用
```bash
npm run dev
```

### 2. 打开开发者工具
- 按 F12 打开浏览器开发者工具
- 切换到 Console 标签页
- 确保显示所有日志级别

### 3. 访问视频生成页面
- 导航到包含 VideoGenerationStep 组件的页面
- 确保有项目ID、用户ID和镜头数据

### 4. 观察缓存行为

#### 初始加载测试
观察控制台日志，应该看到：
```
🔄 [AUTO-REFRESH] 开始使用缓存优化的批量刷新
🔍 [AUTO-REFRESH] 使用缓存查询 X 个镜头的视频状态
✅ [AUTO-REFRESH] 缓存查询成功，找到 X 个镜头的任务
📊 [AUTO-REFRESH] 缓存状态: 新查询 (首次) 或 命中缓存 (后续)
✅ [PERIODIC-REFRESH] 定期刷新机制已启动，间隔5分钟
```

#### 缓存状态显示测试
在页面左侧场景管理区域的标题栏，应该看到：
- 💾 缓存 Xs 的黄色徽章
- 鼠标悬停显示详细的缓存信息

#### 定期刷新测试
等待5分钟后，观察控制台日志：
```
🔄 [PERIODIC-REFRESH] 执行定期缓存刷新
✅ [PERIODIC-REFRESH] 定期刷新成功，缓存状态: 命中缓存/新查询
```

### 5. 测试回退机制

#### 模拟缓存API失败
1. 在网络标签页中阻止对 `/api/video-records/cached` 的请求
2. 刷新页面
3. 观察是否自动回退到原始API：
```
❌ [AUTO-REFRESH] 缓存优化的批量自动刷新失败
🔄 [AUTO-REFRESH] 回退到原始批量查询API
✅ [AUTO-REFRESH] 回退查询成功
```

### 6. 性能测试

#### 响应时间对比
1. **首次查询**：记录响应时间（通常 > 500ms）
2. **缓存命中**：记录响应时间（通常 < 100ms）
3. **计算性能提升**：(首次时间 - 缓存时间) / 首次时间 * 100%

#### 网络请求监控
在 Network 标签页中观察：
- 首次加载：应该看到对缓存API的POST请求
- 5分钟内再次访问：应该看到更少的网络请求
- 缓存过期后：应该看到新的API请求

## 预期结果

### 成功指标
- ✅ 缓存状态徽章正常显示
- ✅ 控制台日志显示缓存命中
- ✅ 5分钟内重复访问不产生新的数据库查询
- ✅ 定期刷新机制正常工作
- ✅ 回退机制在缓存失败时正常工作

### 性能指标
- 📈 缓存命中时响应时间 < 100ms
- 📈 数据库查询减少 80% 以上
- 📈 页面加载速度提升 50% 以上

## 故障排除

### 常见问题

#### 1. 缓存状态不显示
**可能原因**：
- projectId 或 userId 为空
- scenes 数组为空
- 组件未正确初始化

**解决方案**：
- 检查控制台错误日志
- 确认必要的props已传递
- 检查数据加载状态

#### 2. 缓存不生效
**可能原因**：
- Next.js 缓存配置问题
- API路由未正确实现
- 缓存键生成错误

**解决方案**：
- 检查 `/api/video-records/cached` 端点是否可访问
- 验证缓存键格式
- 查看服务器端日志

#### 3. 定期刷新不工作
**可能原因**：
- 定时器被清除
- 组件卸载
- 内存泄漏

**解决方案**：
- 检查组件生命周期
- 确认清理函数正确执行
- 监控内存使用情况

### 调试技巧

#### 1. 启用详细日志
在组件中添加更多日志：
```typescript
console.log('🔍 [DEBUG] Cache status:', cacheStatus)
console.log('🔍 [DEBUG] Last query time:', lastCacheQuery.current)
console.log('🔍 [DEBUG] Scenes count:', scenes.length)
```

#### 2. 手动触发缓存清除
在浏览器控制台中执行：
```javascript
fetch('/api/video-records/cached?action=clear-cache')
  .then(r => r.json())
  .then(console.log)
```

#### 3. 监控缓存键
在网络请求中查看实际的缓存键：
```
POST /api/video-records/cached?cacheKey=project123-user456-shot1,shot2,shot3
```

## 性能基准测试

### 测试场景
- 项目包含 10 个镜头
- 每个镜头有 1-3 个视频任务
- 模拟 100 次页面访问

### 预期结果
| 指标 | 优化前 | 优化后 | 改进 |
|------|--------|--------|------|
| 平均响应时间 | 800ms | 150ms | 81% ↓ |
| 数据库查询次数 | 100 | 20 | 80% ↓ |
| 服务器CPU使用 | 高 | 低 | 60% ↓ |
| 内存使用 | 稳定 | 稳定 | 无变化 |

## 监控建议

### 生产环境监控
1. **缓存命中率**：目标 > 80%
2. **响应时间**：目标 < 200ms
3. **错误率**：目标 < 1%
4. **内存使用**：监控缓存大小

### 告警设置
- 缓存命中率 < 70%
- 平均响应时间 > 500ms
- 错误率 > 5%
- 内存使用 > 阈值

## 总结

通过以上测试步骤，可以验证视频记录缓存优化实现是否正常工作。关键是要观察缓存状态显示、控制台日志和网络请求，确保缓存机制按预期运行，并在出现问题时能够正确回退到原始API。
