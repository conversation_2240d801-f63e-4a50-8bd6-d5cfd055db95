import { VideoClip, AudioClip, SubtitleClip, ViewportState } from './types'

// 时间轴工具函数集合
export class TimelineUtils {
  private viewport: ViewportState
  private snapThreshold: number
  private snapToGrid: boolean

  constructor(viewport: ViewportState, snapThreshold = 0.1, snapToGrid = true) {
    this.viewport = viewport
    this.snapThreshold = snapThreshold
    this.snapToGrid = snapToGrid
  }

  // 时间转像素
  timeToPixels(time: number): number {
    return time * this.viewport.pixelsPerSecond
  }

  // 像素转时间
  pixelsToTime(pixels: number): number {
    return pixels / this.viewport.pixelsPerSecond
  }

  // 网格吸附
  snapTimeToGrid(time: number, gridInterval = 1): number {
    if (!this.snapToGrid) return time
    
    const snappedTime = Math.round(time / gridInterval) * gridInterval
    
    // 如果在阈值范围内，则吸附
    if (Math.abs(time - snappedTime) <= this.snapThreshold) {
      return snappedTime
    }
    return time
  }

  // 吸附到其他片段边缘
  snapToClipEdges(
    time: number, 
    clips: (VideoClip | AudioClip | SubtitleClip)[], 
    excludeIds: string[] = []
  ): number {
    if (!this.snapToGrid) return time

    let closestSnapTime = time
    let minDistance = this.snapThreshold

    clips.forEach(clip => {
      if (excludeIds.includes(clip.id)) return

      // 检查开始时间
      const startDistance = Math.abs(time - clip.startTime)
      if (startDistance < minDistance) {
        minDistance = startDistance
        closestSnapTime = clip.startTime
      }

      // 检查结束时间
      const endDistance = Math.abs(time - clip.endTime)
      if (endDistance < minDistance) {
        minDistance = endDistance
        closestSnapTime = clip.endTime
      }
    })

    return closestSnapTime
  }

  // 获取指定位置的片段
  getClipAtPosition(
    track: number, 
    time: number, 
    clips: (VideoClip | AudioClip | SubtitleClip)[]
  ): (VideoClip | AudioClip | SubtitleClip) | null {
    return clips.find(clip => 
      clip.track === track && 
      time >= clip.startTime && 
      time <= clip.endTime
    ) || null
  }

  // 获取重叠的片段
  getOverlappingClips(
    targetClip: VideoClip | AudioClip | SubtitleClip,
    clips: (VideoClip | AudioClip | SubtitleClip)[],
    excludeIds: string[] = []
  ): (VideoClip | AudioClip | SubtitleClip)[] {
    return clips.filter(clip => {
      if (excludeIds.includes(clip.id) || clip.id === targetClip.id) return false
      if (clip.track !== targetClip.track) return false
      
      // 检查时间重叠
      return !(
        targetClip.endTime <= clip.startTime || 
        targetClip.startTime >= clip.endTime
      )
    })
  }

  // 检查片段是否可以放置在指定位置
  canPlaceClip(
    clip: VideoClip | AudioClip | SubtitleClip,
    track: number,
    startTime: number,
    clips: (VideoClip | AudioClip | SubtitleClip)[],
    excludeIds: string[] = []
  ): boolean {
    const duration = 'duration' in clip ? clip.duration : (clip.endTime - clip.startTime)
    const testClip = {
      ...clip,
      track,
      startTime,
      endTime: startTime + duration
    }

    const overlapping = this.getOverlappingClips(testClip, clips, excludeIds)
    return overlapping.length === 0
  }

  // 查找可用的放置位置
  findAvailablePosition(
    clip: VideoClip | AudioClip | SubtitleClip,
    preferredTrack: number,
    preferredTime: number,
    clips: (VideoClip | AudioClip | SubtitleClip)[],
    maxTracks = 10
  ): { track: number; time: number } | null {
    const duration = 'duration' in clip ? clip.duration : (clip.endTime - clip.startTime)

    // 首先尝试首选位置
    if (this.canPlaceClip(clip, preferredTrack, preferredTime, clips)) {
      return { track: preferredTrack, time: preferredTime }
    }

    // 在首选轨道上寻找最近的可用位置
    const trackClips = clips.filter(c => c.track === preferredTrack)
      .sort((a, b) => a.startTime - b.startTime)

    // 尝试在片段之间的空隙中放置
    for (let i = 0; i <= trackClips.length; i++) {
      let gapStart: number
      let gapEnd: number

      if (i === 0) {
        gapStart = 0
        gapEnd = trackClips.length > 0 ? trackClips[0].startTime : Infinity
      } else if (i === trackClips.length) {
        gapStart = trackClips[i - 1].endTime
        gapEnd = Infinity
      } else {
        gapStart = trackClips[i - 1].endTime
        gapEnd = trackClips[i].startTime
      }

      if (gapEnd - gapStart >= duration) {
        const time = Math.max(gapStart, preferredTime)
        if (time + duration <= gapEnd) {
          return { track: preferredTrack, time }
        }
      }
    }

    // 如果首选轨道没有空间，尝试其他轨道
    for (let track = 0; track < maxTracks; track++) {
      if (track === preferredTrack) continue
      
      if (this.canPlaceClip(clip, track, preferredTime, clips)) {
        return { track, time: preferredTime }
      }
    }

    return null
  }

  // 计算可见时间范围内的片段
  getVisibleClips(clips: (VideoClip | AudioClip | SubtitleClip)[]): (VideoClip | AudioClip | SubtitleClip)[] {
    const { start, end } = this.viewport.visibleTimeRange
    return clips.filter(clip => 
      !(clip.endTime < start || clip.startTime > end)
    )
  }

  // 计算时间轴刻度
  calculateTimeMarks(duration: number): { major: number[], minor: number[] } {
    const { pixelsPerSecond } = this.viewport
    const minPixelsBetweenMarks = 50 // 最小刻度间距（像素）
    const minTimeBetweenMarks = minPixelsBetweenMarks / pixelsPerSecond

    // 确定合适的刻度间隔
    let majorInterval: number
    let minorInterval: number

    if (minTimeBetweenMarks <= 0.1) {
      majorInterval = 1
      minorInterval = 0.1
    } else if (minTimeBetweenMarks <= 0.5) {
      majorInterval = 5
      minorInterval = 1
    } else if (minTimeBetweenMarks <= 1) {
      majorInterval = 10
      minorInterval = 2
    } else if (minTimeBetweenMarks <= 5) {
      majorInterval = 30
      minorInterval = 10
    } else if (minTimeBetweenMarks <= 10) {
      majorInterval = 60
      minorInterval = 15
    } else {
      majorInterval = 300
      minorInterval = 60
    }

    const major: number[] = []
    const minor: number[] = []

    // 生成主刻度
    for (let time = 0; time <= duration; time += majorInterval) {
      major.push(time)
    }

    // 生成次刻度
    for (let time = 0; time <= duration; time += minorInterval) {
      if (!major.includes(time)) {
        minor.push(time)
      }
    }

    return { major, minor }
  }

  // 格式化时间显示
  formatTime(seconds: number, showMilliseconds = false): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)

    if (hours > 0) {
      return showMilliseconds 
        ? `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
        : `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    } else {
      return showMilliseconds
        ? `${minutes}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
        : `${minutes}:${secs.toString().padStart(2, '0')}`
    }
  }

  // 解析时间字符串
  parseTime(timeString: string): number {
    const parts = timeString.split(':')
    let seconds = 0

    if (parts.length === 3) {
      // HH:MM:SS 或 HH:MM:SS.mmm
      seconds += parseInt(parts[0]) * 3600
      seconds += parseInt(parts[1]) * 60
      
      const secParts = parts[2].split('.')
      seconds += parseInt(secParts[0])
      if (secParts[1]) {
        seconds += parseInt(secParts[1]) / 1000
      }
    } else if (parts.length === 2) {
      // MM:SS 或 MM:SS.mmm
      seconds += parseInt(parts[0]) * 60
      
      const secParts = parts[1].split('.')
      seconds += parseInt(secParts[0])
      if (secParts[1]) {
        seconds += parseInt(secParts[1]) / 1000
      }
    }

    return seconds
  }

  // 计算选择区域内的片段
  getClipsInSelection(
    clips: (VideoClip | AudioClip | SubtitleClip)[],
    startTime: number,
    endTime: number,
    tracks?: number[]
  ): (VideoClip | AudioClip | SubtitleClip)[] {
    return clips.filter(clip => {
      // 检查轨道过滤
      if (tracks && !tracks.includes(clip.track)) return false
      
      // 检查时间重叠
      return !(clip.endTime <= startTime || clip.startTime >= endTime)
    })
  }

  // 更新视口设置
  updateViewport(updates: Partial<ViewportState>): void {
    Object.assign(this.viewport, updates)
  }

  // 设置吸附参数
  setSnapSettings(snapToGrid: boolean, snapThreshold: number): void {
    this.snapToGrid = snapToGrid
    this.snapThreshold = snapThreshold
  }
}

// 创建时间轴工具实例的工厂函数
export const createTimelineUtils = (
  viewport: ViewportState,
  snapThreshold = 0.1,
  snapToGrid = true
): TimelineUtils => {
  return new TimelineUtils(viewport, snapThreshold, snapToGrid)
}

// 常用的时间轴计算函数
export const timelineHelpers = {
  // 计算两个时间点之间的距离（像素）
  getPixelDistance: (time1: number, time2: number, pixelsPerSecond: number): number => {
    return Math.abs(time2 - time1) * pixelsPerSecond
  },

  // 检查两个时间范围是否重叠
  timeRangesOverlap: (
    start1: number, end1: number,
    start2: number, end2: number
  ): boolean => {
    return !(end1 <= start2 || start1 >= end2)
  },

  // 计算时间范围的交集
  getTimeRangeIntersection: (
    start1: number, end1: number,
    start2: number, end2: number
  ): { start: number; end: number } | null => {
    if (!timelineHelpers.timeRangesOverlap(start1, end1, start2, end2)) {
      return null
    }
    
    return {
      start: Math.max(start1, start2),
      end: Math.min(end1, end2)
    }
  },

  // 计算时间范围的并集
  getTimeRangeUnion: (
    start1: number, end1: number,
    start2: number, end2: number
  ): { start: number; end: number } => {
    return {
      start: Math.min(start1, start2),
      end: Math.max(end1, end2)
    }
  }
}
