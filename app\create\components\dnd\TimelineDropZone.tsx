"use client"

import React, { useRef } from 'react'
import { useDrop } from 'react-dnd'
import { Rect, Group, Line } from 'react-konva'
import { DragTypes, DragItem, DropResult } from './DragDropProvider'

interface TimelineDropZoneProps {
  x: number
  y: number
  width: number
  height: number
  track: number
  pixelsPerSecond: number
  trackHeight: number
  rulerHeight: number
  onDrop: (item: DragItem, track: number, time: number) => void
  snapToGrid?: boolean
  snapThreshold?: number
  showDropIndicator?: boolean
}

export const TimelineDropZone: React.FC<TimelineDropZoneProps> = ({
  x,
  y,
  width,
  height,
  track,
  pixelsPerSecond,
  trackHeight,
  rulerHeight,
  onDrop,
  snapToGrid = true,
  snapThreshold = 0.1,
  showDropIndicator = true
}) => {
  const groupRef = useRef<any>(null)

  // 时间吸附函数
  const snapTime = (time: number): number => {
    if (!snapToGrid) return time
    
    const gridInterval = 1 // 1秒网格
    const snappedTime = Math.round(time / gridInterval) * gridInterval
    
    if (Math.abs(time - snappedTime) <= snapThreshold) {
      return snappedTime
    }
    return time
  }

  // 设置放置目标
  const [{ isOver, canDrop, draggedItem }, drop] = useDrop({
    accept: [
      DragTypes.VIDEO_CLIP, 
      DragTypes.AUDIO_CLIP, 
      DragTypes.SUBTITLE_CLIP, 
      DragTypes.MEDIA_ITEM
    ],
    drop: (item: DragItem, monitor): DropResult => {
      const clientOffset = monitor.getClientOffset()
      if (!clientOffset || !groupRef.current) {
        return { track, time: 0, dropZone: 'timeline' }
      }

      // 获取舞台位置
      const stage = groupRef.current.getStage()
      const stagePos = stage.getPointerPosition()
      
      // 计算相对于时间轴的时间位置
      const relativeX = stagePos.x - x
      const dropTime = Math.max(0, relativeX / pixelsPerSecond)
      const snappedTime = snapToGrid ? snapTime(dropTime) : dropTime

      // 调用回调
      onDrop(item, track, snappedTime)

      return {
        track,
        time: snappedTime,
        dropZone: 'timeline'
      }
    },
    hover: (item: DragItem, monitor) => {
      // 可以在这里实现悬停预览效果
      const clientOffset = monitor.getClientOffset()
      if (clientOffset && groupRef.current) {
        const stage = groupRef.current.getStage()
        const stagePos = stage.getPointerPosition()
        
        // 计算预览位置
        const relativeX = stagePos.x - x
        const hoverTime = Math.max(0, relativeX / pixelsPerSecond)
        const snappedTime = snapToGrid ? snapTime(hoverTime) : hoverTime
        
        // 这里可以触发预览更新事件
        // onHover?.(item, track, snappedTime)
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
      draggedItem: monitor.getItem() as DragItem
    }),
  })

  // 计算拖拽预览位置
  const getPreviewPosition = (): { x: number; width: number } | null => {
    if (!isOver || !canDrop || !draggedItem || !groupRef.current) {
      return null
    }

    const stage = groupRef.current.getStage()
    const stagePos = stage.getPointerPosition()
    
    if (!stagePos) return null

    const relativeX = stagePos.x - x
    const dropTime = Math.max(0, relativeX / pixelsPerSecond)
    const snappedTime = snapToGrid ? snapTime(dropTime) : dropTime
    
    const previewX = snappedTime * pixelsPerSecond
    const previewWidth = draggedItem.duration * pixelsPerSecond

    return { x: previewX, width: previewWidth }
  }

  const previewPos = getPreviewPosition()

  // 合并引用
  const combinedRef = (node: any) => {
    groupRef.current = node
    drop(node)
  }

  // 获取轨道类型颜色
  const getTrackColor = () => {
    if (!draggedItem) return 'rgba(156, 163, 175, 0.1)' // 默认灰色
    
    switch (draggedItem.clipType) {
      case 'video':
        return 'rgba(16, 185, 129, 0.1)' // 绿色
      case 'audio':
        return 'rgba(99, 102, 241, 0.1)' // 蓝色
      case 'subtitle':
        return 'rgba(139, 92, 246, 0.1)' // 紫色
      default:
        return 'rgba(156, 163, 175, 0.1)'
    }
  }

  const getTrackStrokeColor = () => {
    if (!draggedItem) return '#9ca3af'
    
    switch (draggedItem.clipType) {
      case 'video':
        return '#10b981'
      case 'audio':
        return '#6366f1'
      case 'subtitle':
        return '#8b5cf6'
      default:
        return '#9ca3af'
    }
  }

  return (
    <Group ref={combinedRef} x={x} y={y}>
      {/* 放置区域背景 */}
      <Rect
        width={width}
        height={height}
        fill={isOver && canDrop ? getTrackColor() : 'transparent'}
        stroke={isOver && canDrop ? getTrackStrokeColor() : 'transparent'}
        strokeWidth={isOver && canDrop ? 1 : 0}
        dash={isOver && canDrop ? [5, 5] : []}
        opacity={0.8}
      />

      {/* 拖拽预览指示器 */}
      {showDropIndicator && isOver && canDrop && previewPos && (
        <>
          {/* 预览片段 */}
          <Rect
            x={previewPos.x}
            y={2}
            width={previewPos.width}
            height={height - 4}
            fill={getTrackColor()}
            stroke={getTrackStrokeColor()}
            strokeWidth={2}
            cornerRadius={4}
            opacity={0.6}
            dash={[3, 3]}
          />
          
          {/* 吸附指示线 */}
          {snapToGrid && (
            <Line
              points={[previewPos.x, 0, previewPos.x, height]}
              stroke={getTrackStrokeColor()}
              strokeWidth={1}
              opacity={0.8}
            />
          )}
          
          {/* 时间标签 */}
          <Rect
            x={previewPos.x - 20}
            y={-20}
            width={40}
            height={16}
            fill="rgba(0, 0, 0, 0.8)"
            cornerRadius={2}
          />
          
          {/* 时间文本 */}
          {/* 注意：Konva 的 Text 组件需要单独处理 */}
        </>
      )}

      {/* 网格线（可选） */}
      {snapToGrid && isOver && canDrop && (
        <>
          {/* 垂直网格线 */}
          {Array.from({ length: Math.floor(width / pixelsPerSecond) + 1 }, (_, i) => (
            <Line
              key={`grid-${i}`}
              points={[i * pixelsPerSecond, 0, i * pixelsPerSecond, height]}
              stroke="rgba(156, 163, 175, 0.3)"
              strokeWidth={0.5}
            />
          ))}
        </>
      )}
    </Group>
  )
}

// 轨道标签组件
export const TrackDropZoneLabel: React.FC<{
  track: number
  trackType: 'video' | 'audio' | 'subtitle'
  isActive: boolean
}> = ({ track, trackType, isActive }) => {
  const getTrackIcon = () => {
    switch (trackType) {
      case 'video':
        return '🎥'
      case 'audio':
        return '🎵'
      case 'subtitle':
        return '📝'
      default:
        return '📄'
    }
  }

  const getTrackColor = () => {
    switch (trackType) {
      case 'video':
        return isActive ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-600'
      case 'audio':
        return isActive ? 'bg-blue-100 text-blue-800' : 'bg-blue-50 text-blue-600'
      case 'subtitle':
        return isActive ? 'bg-purple-100 text-purple-800' : 'bg-purple-50 text-purple-600'
      default:
        return isActive ? 'bg-gray-100 text-gray-800' : 'bg-gray-50 text-gray-600'
    }
  }

  return (
    <div className={`
      flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors
      ${getTrackColor()}
      ${isActive ? 'shadow-sm border-2 border-current' : 'border border-transparent'}
    `}>
      <span className="text-lg">{getTrackIcon()}</span>
      <div>
        <div className="text-sm font-medium">
          {trackType.charAt(0).toUpperCase() + trackType.slice(1)} {track + 1}
        </div>
        <div className="text-xs opacity-75">
          轨道 {track}
        </div>
      </div>
    </div>
  )
}
