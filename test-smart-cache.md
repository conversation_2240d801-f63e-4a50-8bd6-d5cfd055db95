# 简化缓存功能测试指南

## 核心测试场景

### 场景1：首次查询测试
**目标**：验证首次进入页面时会查询数据库

**步骤**：
1. 清除浏览器缓存或使用无痕模式
2. 进入视频生成页面
3. 观察控制台日志和缓存状态

**预期结果**：
```
🔄 [FIRST-QUERY] 首次查询数据库获取镜头信息
```

**UI预期**：
- 缓存状态显示：🔄 待缓存 (蓝色徽章)
- 会执行网络请求查询数据库

### 场景2：后续刷新缓存测试
**目标**：验证首次查询后，所有刷新操作都使用缓存

**步骤**：
1. 完成首次查询（场景1）
2. 刷新页面或重新进入页面
3. 观察控制台日志

**预期结果**：
```
💾 [CACHE] 使用缓存数据，跳过数据库查询
```

**UI预期**：
- 缓存状态显示：✅ 已缓存 X视频 (绿色徽章)
- 无网络请求，页面立即加载

### 场景3：手动刷新缓存测试
**目标**：验证手动刷新按钮也使用缓存

**步骤**：
1. 完成首次查询
2. 点击场景的刷新按钮
3. 观察弹窗提示

**预期结果**：
```
💾 [CACHE-REFRESH] 场景 "XXX" 使用缓存数据，不查询数据库
```

**UI预期**：
- 弹窗显示：💾 使用缓存数据，无需重新查询数据库

### 场景4：定期刷新智能缓存测试
**目标**：验证定期刷新也会使用智能缓存检查

**步骤**：
1. 确保所有场景都有视频
2. 等待5分钟触发定期刷新
3. 观察控制台日志

**预期结果**：
```
🔄 [PERIODIC-REFRESH] 执行定期缓存刷新
🎯 [SMART-CACHE] 检测到完整视频缓存: 3/3 场景，共 5 个视频
✅ [PERIODIC-REFRESH] 智能缓存命中，跳过定期刷新
```

## 性能测试

### 响应时间测试
使用浏览器开发者工具的Performance标签页：

1. **完整缓存场景**：
   - 预期响应时间：< 50ms
   - 无网络请求
   - 页面立即渲染

2. **部分缓存场景**：
   - 预期响应时间：< 100ms
   - 1个网络请求到缓存API
   - 快速页面更新

3. **无缓存场景**：
   - 预期响应时间：< 500ms
   - 1个网络请求到缓存API
   - 正常页面加载

### 网络请求监控
在Network标签页中观察：

1. **完整缓存**：无额外网络请求
2. **部分缓存**：1个POST请求到 `/api/video-records/cached`
3. **缓存失败**：回退到 `/api/save-video-generation/batch`

## 缓存状态UI测试

### 状态指示器测试
观察场景管理区域标题栏的缓存状态徽章：

1. **完整缓存**：
   - 颜色：绿色 (from-green-100 to-emerald-100)
   - 图标：✅
   - 文本：缓存 {数量}视频 {时间}s

2. **部分缓存**：
   - 颜色：黄色 (from-yellow-100 to-orange-100)
   - 图标：💾
   - 文本：缓存 {数量}视频 {时间}s

3. **悬停提示**：
   - 缓存状态：完整缓存/部分缓存
   - 缓存视频数：实际数量
   - 上次查询时间
   - 下次刷新时间

### 实时更新测试
观察缓存状态的实时更新：

1. 时间计数器每秒更新
2. 新视频生成后状态立即更新
3. 定期刷新后状态正确更新

## 错误处理测试

### 缓存API失败测试
1. 在Network标签页中阻止 `/api/video-records/cached` 请求
2. 刷新页面
3. 观察是否正确回退到原始API

**预期结果**：
```
❌ [AUTO-REFRESH] 缓存优化的批量自动刷新失败
🔄 [AUTO-REFRESH] 回退到原始批量查询API
✅ [AUTO-REFRESH] 回退查询成功
```

### 数据不一致测试
1. 手动修改场景的generatedVideos数组
2. 观察智能缓存检查是否正确工作
3. 验证缓存状态显示是否准确

## 调试技巧

### 1. 强制触发智能缓存检查
在浏览器控制台中执行：
```javascript
// 查看当前场景状态
console.log('Scenes:', window.scenes)

// 手动触发缓存检查
window.shouldSkipQuery && console.log(window.shouldSkipQuery(window.scenes))
```

### 2. 监控缓存状态变化
```javascript
// 监控缓存状态
setInterval(() => {
  console.log('Cache Status:', window.cacheStatus)
}, 5000)
```

### 3. 清除缓存测试
```javascript
// 清除服务端缓存
fetch('/api/video-records/cached?action=clear-cache')
  .then(r => r.json())
  .then(console.log)
```

## 成功指标

### 功能指标
- ✅ 智能缓存检查正确识别完整/部分缓存
- ✅ 完整缓存时跳过所有网络请求
- ✅ 部分缓存时执行缓存查询
- ✅ 缓存状态UI正确显示和更新
- ✅ 定期刷新使用智能缓存检查
- ✅ 错误回退机制正常工作

### 性能指标
- 📈 完整缓存响应时间 < 50ms
- 📈 部分缓存响应时间 < 100ms
- 📈 网络请求减少 90% 以上
- 📈 页面加载速度提升 80% 以上

### 用户体验指标
- 🎯 缓存状态清晰可见
- 🎯 视频数量准确显示
- 🎯 页面响应迅速
- 🎯 状态更新及时

## 故障排除

### 常见问题
1. **智能缓存不生效**：检查scenes数组和generatedVideos数据
2. **缓存状态不更新**：检查setCacheStatus调用
3. **网络请求仍然发送**：检查shouldSkipQuery逻辑
4. **UI状态不正确**：检查cacheStatus.hasCompleteVideos值

### 解决方案
1. 检查控制台错误日志
2. 验证数据结构完整性
3. 确认组件状态同步
4. 测试API端点可用性

通过以上测试，可以全面验证智能缓存功能是否按预期工作，确保在有完整视频信息时能够跳过查询，直接使用缓存渲染页面。
