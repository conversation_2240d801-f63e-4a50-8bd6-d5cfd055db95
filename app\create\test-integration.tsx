"use client"

import React from 'react'
import { IntegratedVideoEditor } from './components/IntegratedVideoEditor'

// 测试数据
const mockShotsWithImages = [
  {
    shotId: "shot-1",
    shotNumber: 1,
    videoUrl: undefined, // 模拟没有视频缓存
    images: [
      { url: "https://picsum.photos/800/600?random=1", isPrimary: true }
    ]
  },
  {
    shotId: "shot-2", 
    shotNumber: 2,
    videoUrl: undefined, // 模拟没有视频缓存
    images: [
      { url: "https://picsum.photos/800/600?random=2", isPrimary: true }
    ]
  },
  {
    shotId: "shot-3",
    shotNumber: 3,
    videoUrl: undefined, // 模拟没有视频缓存
    images: [
      { url: "https://picsum.photos/800/600?random=3", isPrimary: true }
    ]
  },
  {
    shotId: "shot-4",
    shotNumber: 4,
    videoUrl: undefined, // 模拟没有视频缓存
    images: [
      { url: "https://picsum.photos/800/600?random=4", isPrimary: true }
    ]
  },
  {
    shotId: "shot-5",
    shotNumber: 5,
    videoUrl: undefined, // 模拟没有视频缓存
    images: [
      { url: "https://picsum.photos/800/600?random=5", isPrimary: true }
    ]
  }
]

const mockScriptData = {
  title: "测试短剧",
  style: "realistic",
  totalDuration: 25,
  shotCount: 5,
  shots: [
    {
      shotNumber: 1,
      duration: 5,
      shotType: "中景",
      location: "客厅",
      characters: ["主角"],
      action: "主角坐在沙发上思考",
      dialogue: "今天真是奇怪的一天...",
      cameraMovement: "静止",
      lighting: "自然光",
      props: ["沙发", "茶几"],
      mood: "沉思",
      soundEffect: "环境音",
      transition: "淡入"
    },
    {
      shotNumber: 2,
      duration: 5,
      shotType: "特写",
      location: "客厅",
      characters: ["主角"],
      action: "主角拿起手机",
      dialogue: "让我看看有什么消息...",
      cameraMovement: "推进",
      lighting: "自然光",
      props: ["手机"],
      mood: "好奇",
      soundEffect: "手机震动",
      transition: "切换"
    },
    {
      shotNumber: 3,
      duration: 5,
      shotType: "中景",
      location: "客厅",
      characters: ["主角"],
      action: "主角站起身来",
      dialogue: "不可能！这怎么可能？",
      cameraMovement: "跟随",
      lighting: "自然光",
      props: ["沙发"],
      mood: "震惊",
      soundEffect: "惊讶音效",
      transition: "切换"
    },
    {
      shotNumber: 4,
      duration: 5,
      shotType: "远景",
      location: "客厅",
      characters: ["主角"],
      action: "主角在房间里踱步",
      dialogue: "我需要冷静下来思考...",
      cameraMovement: "摇摆",
      lighting: "自然光",
      props: ["整个客厅"],
      mood: "焦虑",
      soundEffect: "脚步声",
      transition: "切换"
    },
    {
      shotNumber: 5,
      duration: 5,
      shotType: "特写",
      location: "客厅",
      characters: ["主角"],
      action: "主角做出决定",
      dialogue: "好吧，我知道该怎么做了。",
      cameraMovement: "静止",
      lighting: "自然光",
      props: [],
      mood: "坚定",
      soundEffect: "决心音效",
      transition: "淡出"
    }
  ]
}

export default function TestIntegrationPage() {
  const handleSave = (projectData: any) => {
    console.log('🎬 [TEST] 保存项目数据:', projectData)
    alert('项目数据已保存到控制台，请查看开发者工具')
  }

  const handleExport = (videoData: Uint8Array, filename: string) => {
    console.log('🎬 [TEST] 导出视频:', filename, '大小:', videoData.length, 'bytes')
    
    // 创建下载链接（在实际环境中）
    try {
      const blob = new Blob([videoData], { type: 'video/mp4' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      alert(`视频 "${filename}" 导出成功！`)
    } catch (error) {
      console.error('导出视频时出错:', error)
      alert('视频导出失败，请查看控制台了解详情')
    }
  }

  return (
    <div className="h-screen">
      <IntegratedVideoEditor
        projectId="test-project-123"
        userId="test-user-456"
        scriptData={mockScriptData}
        shotsWithImages={mockShotsWithImages}
        onSave={handleSave}
        onExport={handleExport}
        className="h-full"
      />
    </div>
  )
}

// 使用说明组件
export function IntegrationTestInstructions() {
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-bold text-blue-800 mb-4">🧪 集成测试页面</h2>
        <p className="text-blue-700 mb-4">
          这个页面用于测试 IntegratedVideoEditor 组件的集成效果。
        </p>
        
        <div className="space-y-3">
          <h3 className="font-semibold text-blue-800">测试功能：</h3>
          <ul className="list-disc list-inside space-y-1 text-blue-700">
            <li>✅ 智能缓存切换（模拟图片缓存数据）</li>
            <li>✅ 视频片段自动生成</li>
            <li>✅ 三栏响应式布局</li>
            <li>✅ 实时预览功能</li>
            <li>✅ 时间轴编辑器</li>
            <li>✅ 项目保存和导出</li>
          </ul>
        </div>

        <div className="mt-4 p-4 bg-white rounded border border-blue-200">
          <h4 className="font-semibold text-blue-800 mb-2">测试步骤：</h4>
          <ol className="list-decimal list-inside space-y-1 text-blue-700 text-sm">
            <li>页面加载后会自动生成 5 个视频片段</li>
            <li>左侧媒体库显示片段列表和缓存状态</li>
            <li>中央预览区域显示多轨道预览</li>
            <li>底部时间轴显示片段时间线</li>
            <li>右侧属性面板提供编辑控制</li>
            <li>点击"智能剪辑"按钮重新生成片段</li>
            <li>点击"保存项目"测试保存功能</li>
            <li>点击"导出视频"测试导出功能</li>
          </ol>
        </div>

        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
          <h4 className="font-semibold text-yellow-800 mb-2">⚠️ 注意事项：</h4>
          <ul className="list-disc list-inside space-y-1 text-yellow-700 text-sm">
            <li>这是测试环境，使用模拟数据</li>
            <li>实际的缓存 API 调用可能会失败（正常现象）</li>
            <li>视频导出功能需要 FFmpeg.wasm 支持</li>
            <li>请在浏览器开发者工具中查看详细日志</li>
          </ul>
        </div>
      </div>

      <div className="text-center">
        <a 
          href="/create/test-integration" 
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
        >
          🚀 开始测试集成
        </a>
      </div>
    </div>
  )
}
