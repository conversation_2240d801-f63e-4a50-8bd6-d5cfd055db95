# AI 视频编辑器

一个基于 React 和现代 Web 技术构建的高性能视频编辑器，支持智能剪辑、多轨道编辑、实时预览等功能。

## 🚀 核心特性

### 📹 智能视频编辑
- **AI 内容生成集成**: 自动从场景数据生成视频片段
- **智能缓存管理**: 支持视频和图片缓存的智能切换
- **多格式支持**: MP4, AVI, MOV, WebM 等主流视频格式

### 🧠 智能缓存切换系统
- **优先级策略**: 视频缓存 > 图片缓存 > 原始数据
- **自动检测**: 实时检测可用的缓存类型和状态
- **可视化指示**: 直观显示每个片段的缓存来源
- **一键刷新**: 支持手动刷新缓存数据
- **SWR 集成**: 使用 SWR 进行高效的数据获取和缓存

### 🎵 专业音频处理
- **波形可视化**: 基于 WaveSurfer.js 的高质量音频波形显示
- **实时音频编辑**: 音量调节、淡入淡出、音频滤镜
- **多轨道混音**: 支持多个音频轨道的混合和同步

### 📝 强大字幕系统
- **多格式支持**: SRT, VTT, ASS 字幕格式的导入导出
- **实时字幕编辑**: 所见即所得的字幕编辑体验
- **样式自定义**: 字体、颜色、位置、特效等全面自定义

### ⚡ 高性能时间轴
- **基于 Konva.js**: 硬件加速的 2D 渲染引擎
- **虚拟化渲染**: 处理大量片段时的性能优化
- **精确时间控制**: 毫秒级的时间精度和吸附对齐

### 🎯 直观拖拽操作
- **React-DnD 集成**: 流畅的拖拽体验
- **智能碰撞检测**: 自动避免片段重叠
- **多选操作**: 批量编辑和移动片段

### 🔧 浏览器端视频处理
- **FFmpeg.wasm**: 无需服务器的视频处理能力
- **格式转换**: 支持多种视频格式的相互转换
- **实时导出**: 直接在浏览器中生成最终视频

## 🏗️ 技术架构

### 状态管理
```typescript
// 使用 Zustand 进行状态管理
import { useVideoEditor } from './stores'

const MyComponent = () => {
  const {
    videoClips,
    audioClips,
    subtitleClips,
    timeline,
    actions
  } = useVideoEditor()
  
  // 使用编辑器功能
  const handleAddClip = () => {
    actions.addVideoClip({
      id: 'clip-1',
      name: '视频片段',
      startTime: 0,
      endTime: 5,
      track: 0
    })
  }
}
```

### 时间轴系统
```typescript
// 时间轴组件使用
import { Timeline } from './components/timeline'

<Timeline
  onClipSelect={(clipId) => console.log('选中片段:', clipId)}
  onTimeChange={(time) => console.log('时间变化:', time)}
/>
```

### 音频处理
```typescript
// 音频波形可视化
import { WaveformVisualization } from './components/audio'

<WaveformVisualization
  audioUrl="/path/to/audio.mp3"
  height={128}
  waveColor="#6366f1"
  onReady={(wavesurfer) => console.log('音频加载完成')}
/>
```

### 字幕编辑
```typescript
// 字幕解析和编辑
import { SubtitleParser, SubtitleEditor } from './components/subtitle'

// 解析字幕文件
const subtitles = SubtitleParser.parseSRT(srtContent)

// 字幕编辑器
<SubtitleEditor
  selectedClip={selectedSubtitleClip}
  onClipUpdate={(id, updates) => updateSubtitle(id, updates)}
/>
```

### 视频处理
```typescript
// FFmpeg.wasm 视频处理
import { ffmpegProcessor } from './components/ffmpeg'

// 视频格式转换
const convertedVideo = await ffmpegProcessor.convertVideo(
  inputFile,
  'mp4',
  {
    width: 1920,
    height: 1080,
    quality: 'high'
  }
)
```

## 📦 安装和使用

### 1. 安装依赖
```bash
npm install @ffmpeg/ffmpeg @ffmpeg/util
npm install konva react-konva
npm install react-dnd react-dnd-html5-backend
npm install wavesurfer.js
npm install zustand
npm install swr
```

### 2. 基本使用
```typescript
import { VideoEditor } from './create/components'

function App() {
  return (
    <VideoEditor
      projectId="my-project"
      userId="user-123"
      onSave={(projectData) => {
        console.log('保存项目:', projectData)
      }}
      onExport={(videoData, filename) => {
        console.log('导出视频:', filename)
        // 下载视频文件
        const blob = new Blob([videoData])
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        link.click()
      }}
    />
  )
}
```

### 3. 集成到现有流程（推荐）
```typescript
// 使用集成版本，支持智能缓存切换
import { IntegratedVideoEditor } from './create/components'

function MyCreationFlow() {
  return (
    <IntegratedVideoEditor
      projectId="project-123"
      userId="user-456"
      shotsWithImages={shotsWithImages}
      onSave={(projectData) => {
        // 保存到数据库
        saveProjectData(projectData)
      }}
      onExport={(videoData, filename) => {
        // 处理视频导出
        downloadVideo(videoData, filename)
      }}
    />
  )
}
```

### 4. 智能剪辑集成
```typescript
// 与 AI 内容生成系统集成，支持智能缓存
<IntegratedVideoEditor
  projectId="ai-project-123"
  userId="user-456"
  shotsWithImages={[
    {
      shotId: 'shot-1',        // 用于视频缓存查询
      shotNumber: 1,           // 用于图片缓存查询
      videoUrl: '/generated/video1.mp4',  // 可选：直接视频URL
      images: [
        { url: '/generated/image1.jpg', isPrimary: true }
      ]
    }
  ]}
  onSave={handleSave}
  onExport={handleExport}
/>
```

### 5. 流程集成示例
```typescript
// 完整的创作流程集成
import { VideoEditingIntegrationExample } from './create/components'

function CreationWorkflow() {
  const [currentStep, setCurrentStep] = useState(0)

  return (
    <VideoEditingIntegrationExample
      projectId="workflow-project"
      userId="user-123"
      scriptData={scriptData}
      shotsWithImages={shotsWithImages}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onSave={handleSave}
      onComplete={handleComplete}
    />
  )
}
```

## 🎨 自定义主题

编辑器支持主题自定义：

```css
/* 自定义时间轴颜色 */
.timeline-canvas {
  --primary-color: #3b82f6;
  --secondary-color: #10b981;
  --background-color: #ffffff;
  --grid-color: #e5e7eb;
}

/* 自定义音频波形颜色 */
.waveform-visualization {
  --wave-color: #6366f1;
  --progress-color: #3b82f6;
  --cursor-color: #ef4444;
}
```

## 🔧 配置选项

### 编辑器配置
```typescript
const editorConfig = {
  // 时间轴设置
  timeline: {
    pixelsPerSecond: 20,
    trackHeight: 60,
    snapToGrid: true,
    snapThreshold: 0.1
  },
  
  // 音频设置
  audio: {
    showWaveforms: true,
    sampleRate: 44100,
    channels: 2
  },
  
  // 视频设置
  video: {
    defaultResolution: { width: 1920, height: 1080 },
    defaultFrameRate: 30,
    showThumbnails: true
  },
  
  // 字幕设置
  subtitle: {
    defaultStyle: {
      fontFamily: 'Arial',
      fontSize: 24,
      color: '#ffffff',
      backgroundColor: 'transparent'
    }
  }
}
```

## 🚀 性能优化

### 虚拟化渲染
- 时间轴只渲染可见区域的片段
- 大量片段时自动启用虚拟化
- 智能缓存和内存管理

### 异步处理
- 视频处理任务异步执行
- 进度回调和错误处理
- 可取消的长时间操作

### 内存管理
- 自动清理未使用的资源
- 缩略图懒加载
- 音频波形数据压缩

## 🐛 故障排除

### 常见问题

1. **FFmpeg 初始化失败**
   ```typescript
   // 确保正确的 CDN 配置
   const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd'
   ```

2. **音频波形不显示**
   ```typescript
   // 检查音频文件格式和 CORS 设置
   <WaveformVisualization
     audioUrl="/path/to/audio.mp3"
     backend="WebAudio"  // 或 "MediaElement"
   />
   ```

3. **拖拽功能异常**
   ```typescript
   // 确保 DragDropProvider 正确包装
   <DragDropProvider>
     <VideoEditor />
   </DragDropProvider>
   ```

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请通过以下方式联系：
- GitHub Issues
- 邮箱: <EMAIL>
- 文档: https://docs.example.com
