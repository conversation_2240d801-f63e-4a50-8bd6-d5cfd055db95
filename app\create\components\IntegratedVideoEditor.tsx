"use client"

import React, { useState, useC<PERSON>back, useEffect, useMemo } from 'react'
import useS<PERSON> from 'swr'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import {
  Video,
  Download,
  Upload,
  Save,
  Sparkles,
  RefreshCw,
  Loader2,
  CheckCircle,
  AlertCircle,
  Settings,
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX
} from "lucide-react"

// 导入现有的组件和工具
import { DragDropProvider } from './dnd'
import { Timeline } from './timeline'
import { MultiTrackPreview } from './preview'
import { useVideoEditor, VideoClip } from '../stores'

// SWR fetchers for cache data
const fetchVideoTaskStatus = async (key: string) => {
  const [, projectId, userId, shotIdsStr] = key.split('|')
  const shotIds = shotIdsStr.split(',')

  const response = await fetch('/api/video-records/cached', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ projectId, userId, shotIds })
  })

  if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
  return response.json()
}

const fetchImageTaskStatus = async (key: string) => {
  const [, projectId, userId, shotNumbersStr] = key.split('|')
  const shotNumbers = shotNumbersStr.split(',').map(num => parseInt(num))

  const response = await fetch('/api/image-records/cached', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ projectId, userId, shotNumbers })
  })

  if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
  return response.json()
}

interface IntegratedVideoEditorProps {
  projectId?: string
  userId?: string
  scriptData?: any
  shotsWithImages?: any[]
  onSave?: (projectData: any) => void
  onExport?: (videoData: Uint8Array, filename: string) => void
  className?: string
}

export const IntegratedVideoEditor: React.FC<IntegratedVideoEditorProps> = ({
  projectId,
  userId,
  scriptData,
  shotsWithImages,
  onSave,
  onExport,
  className = ''
}) => {
  const {
    videoClips,
    audioClips,
    subtitleClips,
    timeline,
    project,
    actions,
    addVideoClip,
    removeVideoClip
  } = useVideoEditor()

  // 状态管理
  const [selectedClipId, setSelectedClipId] = useState<string | null>(null)
  const [isSmartMode, setIsSmartMode] = useState(true)
  const [isInitialized, setIsInitialized] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [volume, setVolume] = useState([80])
  const [isMuted, setIsMuted] = useState(false)

  // SWR 缓存数据获取
  const shotIds = useMemo(() => {
    return shotsWithImages?.map(shot => shot.shotId).filter(Boolean) as string[] || []
  }, [shotsWithImages])

  const shotNumbers = useMemo(() => {
    return shotsWithImages?.map(shot => shot.shotNumber).filter(Boolean) as number[] || []
  }, [shotsWithImages])

  const videoCacheKey = projectId && userId && shotIds?.length
    ? `video-tasks|${projectId}|${userId}|${shotIds.join(',')}`
    : null

  const imageCacheKey = projectId && userId && shotNumbers?.length
    ? `image-tasks|${projectId}|${userId}|${shotNumbers.join(',')}`
    : null

  const { data: videoTaskData, isLoading: isVideoLoading, mutate: mutateVideo } = useSWR(
    videoCacheKey,
    fetchVideoTaskStatus,
    {
      dedupingInterval: 5 * 60 * 1000,
      revalidateOnFocus: true,
      refreshInterval: 2 * 60 * 1000,
      errorRetryCount: 3
    }
  )

  const { data: imageTaskData, isLoading: isImageLoading, mutate: mutateImage } = useSWR(
    imageCacheKey,
    fetchImageTaskStatus,
    {
      dedupingInterval: 5 * 60 * 1000,
      revalidateOnFocus: true,
      refreshInterval: 2 * 60 * 1000,
      errorRetryCount: 3
    }
  )

  // 智能生成视频片段：优先使用视频缓存，其次使用图片缓存
  const generateSmartVideoClips = useCallback((): VideoClip[] => {
    if (!shotsWithImages || shotsWithImages.length === 0) {
      console.log('没有场景数据可用于生成视频片段')
      return []
    }

    console.log('🎬 [SMART-CLIPS] 开始智能生成视频片段')

    const newVideoClips: VideoClip[] = shotsWithImages.map((shot, index) => {
      const duration = 5
      const startTime = index * duration

      // 检查视频缓存
      let hasVideoCache = false
      let videoUrl: string | undefined
      let videoTaskStatus: string | undefined

      if (videoTaskData?.tasks && shot.shotId) {
        const videoTasks = videoTaskData.tasks[shot.shotId]
        if (videoTasks && videoTasks.length > 0) {
          const latestVideoTask = videoTasks[0]
          videoTaskStatus = latestVideoTask.status

          if (latestVideoTask.status === 'completed' && latestVideoTask.generated_videos) {
            hasVideoCache = true
            const firstVideo = latestVideoTask.generated_videos[0]
            if (firstVideo) {
              videoUrl = firstVideo.video_url
            }
          }
        }
      }

      // 检查图片缓存
      let hasImageCache = false
      let thumbnailUrl = '/placeholder.svg'
      let imageTaskStatus: string | undefined

      if (imageTaskData?.tasks && shot.shotNumber) {
        const imageTasks = imageTaskData.tasks[shot.shotNumber]
        if (imageTasks && imageTasks.length > 0) {
          const latestImageTask = imageTasks[0]
          imageTaskStatus = latestImageTask.status

          if (latestImageTask.status === 'completed' && latestImageTask.generated_images) {
            hasImageCache = true
            const firstImage = latestImageTask.generated_images[0]
            if (firstImage) {
              thumbnailUrl = firstImage.cdn_url || firstImage.image_url
            }
          }
        }
      }

      // 如果没有缓存数据，使用原始的shotsWithImages数据
      if (!hasVideoCache && !hasImageCache) {
        const primaryImage = shot.images?.find((img: any) => img.isPrimary) || shot.images?.[0]
        if (primaryImage) {
          thumbnailUrl = primaryImage.url
          hasImageCache = true
        }
        videoUrl = shot.videoUrl
      }

      // 确定缓存源：优先视频，其次图片
      let cacheSource: 'video' | 'image' | 'none' = 'none'
      if (hasVideoCache) {
        cacheSource = 'video'
      } else if (hasImageCache) {
        cacheSource = 'image'
      }

      return {
        id: `smart-clip-${shot.shotId || shot.shotNumber || index}`,
        name: `镜头 ${shot.shotNumber || index + 1}`,
        duration: duration,
        thumbnail: thumbnailUrl,
        videoUrl: videoUrl,
        startTime: startTime,
        endTime: startTime + duration,
        track: 0,
        sceneData: shot,
        hasVideoCache,
        hasImageCache,
        videoTaskStatus,
        imageTaskStatus,
        cacheSource,
        lastCacheCheck: Date.now()
      }
    })

    console.log(`🎬 [SMART-CLIPS] 智能生成完成，共 ${newVideoClips.length} 个片段`)
    return newVideoClips
  }, [shotsWithImages, videoTaskData, imageTaskData])

  // 智能剪辑处理
  const handleSmartEditing = useCallback(() => {
    console.log('🎬 [SMART-EDITING] 开始智能剪辑')
    
    const newVideoClips = generateSmartVideoClips()
    
    actions.startBatch()
    // 清空现有片段
    videoClips.forEach(clip => removeVideoClip(clip.id))
    // 添加新片段
    newVideoClips.forEach(clip => addVideoClip(clip))
    actions.endBatch()

    console.log('🎬 [SMART-EDITING] 智能剪辑完成，生成了', newVideoClips.length, '个视频片段')
  }, [generateSmartVideoClips, actions, videoClips, removeVideoClip, addVideoClip])

  // 刷新缓存数据
  const refreshTaskStatus = useCallback(() => {
    const promises = []
    if (mutateVideo) promises.push(mutateVideo())
    if (mutateImage) promises.push(mutateImage())
    return Promise.all(promises)
  }, [mutateVideo, mutateImage])

  // 初始化智能剪辑
  useEffect(() => {
    if (shotsWithImages && shotsWithImages.length > 0 && !isVideoLoading && !isImageLoading && !isInitialized) {
      const clips = generateSmartVideoClips()
      
      actions.startBatch()
      clips.forEach(clip => addVideoClip(clip))
      actions.endBatch()
      
      setIsInitialized(true)
      console.log('🎬 [INIT] 智能初始化完成，生成了', clips.length, '个视频片段')
    }
  }, [shotsWithImages, videoTaskData, imageTaskData, isVideoLoading, isImageLoading, isInitialized, generateSmartVideoClips, actions, addVideoClip])

  // 保存处理
  const handleSave = useCallback(() => {
    if (onSave) {
      onSave({
        projectId,
        videoClips,
        audioClips,
        subtitleClips,
        timeline
      })
    }
  }, [onSave, projectId, videoClips, audioClips, subtitleClips, timeline])

  // 播放控制
  const handlePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying)
  }, [isPlaying])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <DragDropProvider>
      <div className={`h-full flex bg-gradient-to-br from-slate-50/50 to-blue-50/30 ${className}`}>
        {/* 左侧媒体库 */}
        <div className="w-80 bg-white/80 backdrop-blur-sm border-r border-slate-200/60 overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center">
                <Video className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-slate-800">智能剪辑</h2>
                <p className="text-sm text-slate-600">AI内容生成项目</p>
              </div>
            </div>

            {/* 智能缓存状态总览 */}
            {shotsWithImages && shotsWithImages.length > 0 && (
              <div className="mb-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl shadow-sm">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center">
                      {isVideoLoading || isImageLoading ? (
                        <RefreshCw className="w-3 h-3 text-indigo-600 animate-spin" />
                      ) : (videoTaskData || imageTaskData) ? (
                        <CheckCircle className="w-3 h-3 text-indigo-600" />
                      ) : (
                        <AlertCircle className="w-3 h-3 text-indigo-600" />
                      )}
                    </div>
                    <span className="text-sm font-semibold text-indigo-800">
                      {isVideoLoading || isImageLoading ? '正在加载缓存...' : '智能缓存状态'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {(videoTaskData?.cached || imageTaskData?.cached) && (
                      <Badge className="bg-green-100 text-green-800 border-green-200 text-xs px-2 py-1">
                        来自缓存
                      </Badge>
                    )}
                  </div>
                </div>
                {(videoTaskData || imageTaskData) && (
                  <div className="mt-2 text-xs text-indigo-600">
                    视频: {videoTaskData?.totalCompletedVideos || 0} 个 |
                    图片: {imageTaskData?.totalCompletedImages || 0} 张
                  </div>
                )}
              </div>
            )}

            {/* 操作按钮 */}
            <div className="mb-6 space-y-2">
              <Button
                className="w-full bg-emerald-500 hover:bg-emerald-600"
                onClick={handleSmartEditing}
                disabled={isVideoLoading || isImageLoading}
              >
                <Sparkles className="w-4 h-4 mr-2" />
                智能剪辑
              </Button>
              
              <Button
                variant="outline"
                className="w-full"
                onClick={refreshTaskStatus}
                disabled={isVideoLoading || isImageLoading}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${(isVideoLoading || isImageLoading) ? 'animate-spin' : ''}`} />
                刷新缓存
              </Button>
            </div>

            {/* 视频素材列表 */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-semibold text-slate-700">视频素材</h3>
                <Badge variant="secondary" className="text-xs">
                  {videoClips.length} 个
                </Badge>
              </div>
              <div className="space-y-3">
                {videoClips.map((clip) => (
                  <div
                    key={clip.id}
                    className="group relative bg-slate-50 rounded-lg p-3 border border-slate-200 hover:border-emerald-300 hover:bg-emerald-50/50 transition-all cursor-pointer"
                    onClick={() => setSelectedClipId(clip.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-16 h-12 bg-gradient-to-br from-slate-300 to-slate-400 rounded-md flex items-center justify-center overflow-hidden">
                        {clip.thumbnail ? (
                          <img 
                            src={clip.thumbnail} 
                            alt={clip.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Video className="w-4 h-4 text-slate-600" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-slate-800 truncate">
                            {clip.name}
                          </p>
                          {/* 缓存状态指示器 */}
                          <Badge
                            className={`ml-2 font-semibold px-2 py-1 text-xs ${
                              clip.cacheSource === 'video'
                                ? "bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-blue-200"
                                : clip.cacheSource === 'image'
                                ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200"
                                : "bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200"
                            }`}
                          >
                            {clip.cacheSource === 'video' ? '🎥' : clip.cacheSource === 'image' ? '🖼️' : '📝'}
                          </Badge>
                        </div>
                        <p className="text-xs text-slate-500">
                          {Math.round(clip.duration)}s | 1920x1080
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 中央内容区域 */}
        <div className="flex-1 flex flex-col">
          {/* 预览区域 */}
          <div className="flex-1 p-6">
            <MultiTrackPreview
              width={1920}
              height={1080}
              onTimeChange={setCurrentTime}
              onPlayStateChange={setIsPlaying}
              className="h-full"
            />
          </div>

          {/* 时间轴区域 */}
          <div className="h-64 p-6 pt-0">
            <Card className="h-full">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">时间轴编辑器</CardTitle>
              </CardHeader>
              <CardContent className="pt-0 h-full">
                <Timeline
                  width={800}
                  height={200}
                  onTimeChange={setCurrentTime}
                  onClipSelect={setSelectedClipId}
                  onClipMove={(clipId, newStartTime, newTrack) => {
                    console.log('片段移动:', clipId, newStartTime, newTrack)
                  }}
                />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 右侧属性面板 */}
        <div className="w-80 bg-white/80 backdrop-blur-sm border-l border-slate-200/60 overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Settings className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-slate-800">属性面板</h2>
                <p className="text-sm text-slate-600">编辑参数设置</p>
              </div>
            </div>

            {/* 智能模式切换 */}
            <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-semibold text-blue-800">智能模式</Label>
                  <p className="text-xs text-blue-600">自动优化剪辑效果</p>
                </div>
                <Switch
                  checked={isSmartMode}
                  onCheckedChange={setIsSmartMode}
                />
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <Button variant="outline" className="w-full" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                保存项目
              </Button>
              <Button className="w-full bg-green-500 hover:bg-green-600">
                <Download className="w-4 h-4 mr-2" />
                导出视频
              </Button>
            </div>
          </div>
        </div>
      </div>
    </DragDropProvider>
  )
}
