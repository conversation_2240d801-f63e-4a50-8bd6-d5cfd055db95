// 字幕组件统一导出

export { SubtitleParser } from './SubtitleParser'
export { SubtitleEditor } from './SubtitleEditor'
export { SubtitleRenderer, SubtitlePreview, SubtitleTrackRenderer } from './SubtitleRenderer'

// 类型导出
export type { SubtitleEntry, SubtitleStyle, SubtitlePosition } from './SubtitleParser'

// 字幕工具函数
export const subtitleUtils = {
  // 格式化时间显示
  formatTime: (seconds: number, format: 'srt' | 'vtt' | 'simple' = 'simple'): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)

    switch (format) {
      case 'srt':
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
      case 'vtt':
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
      case 'simple':
      default:
        return hours > 0 
          ? `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
          : `${minutes}:${secs.toString().padStart(2, '0')}`
    }
  },

  // 解析时间字符串
  parseTime: (timeString: string): number => {
    // 支持多种时间格式
    const formats = [
      // HH:MM:SS,mmm (SRT)
      /^(\d{1,2}):(\d{2}):(\d{2}),(\d{3})$/,
      // HH:MM:SS.mmm (VTT)
      /^(\d{1,2}):(\d{2}):(\d{2})\.(\d{3})$/,
      // MM:SS.mmm
      /^(\d{1,2}):(\d{2})\.(\d{3})$/,
      // MM:SS
      /^(\d{1,2}):(\d{2})$/,
      // SS.mmm
      /^(\d{1,3})\.(\d{3})$/,
      // SS
      /^(\d{1,3})$/
    ]

    for (const format of formats) {
      const match = timeString.match(format)
      if (match) {
        const groups = match.slice(1).map(g => parseInt(g) || 0)
        
        if (groups.length === 4) {
          // HH:MM:SS,mmm or HH:MM:SS.mmm
          return groups[0] * 3600 + groups[1] * 60 + groups[2] + groups[3] / 1000
        } else if (groups.length === 3) {
          // MM:SS.mmm
          return groups[0] * 60 + groups[1] + groups[2] / 1000
        } else if (groups.length === 2) {
          if (timeString.includes('.')) {
            // SS.mmm
            return groups[0] + groups[1] / 1000
          } else {
            // MM:SS
            return groups[0] * 60 + groups[1]
          }
        } else if (groups.length === 1) {
          // SS
          return groups[0]
        }
      }
    }

    return 0
  },

  // 检查字幕时间重叠
  hasTimeOverlap: (
    subtitle1: { startTime: number; endTime: number },
    subtitle2: { startTime: number; endTime: number }
  ): boolean => {
    return !(subtitle1.endTime <= subtitle2.startTime || subtitle1.startTime >= subtitle2.endTime)
  },

  // 计算字幕重叠时长
  calculateOverlapDuration: (
    subtitle1: { startTime: number; endTime: number },
    subtitle2: { startTime: number; endTime: number }
  ): number => {
    if (!subtitleUtils.hasTimeOverlap(subtitle1, subtitle2)) {
      return 0
    }
    
    const overlapStart = Math.max(subtitle1.startTime, subtitle2.startTime)
    const overlapEnd = Math.min(subtitle1.endTime, subtitle2.endTime)
    return overlapEnd - overlapStart
  },

  // 自动调整字幕时间以避免重叠
  adjustTimingToAvoidOverlap: (
    subtitles: Array<{ id: string; startTime: number; endTime: number }>,
    minGap: number = 0.1
  ): Array<{ id: string; startTime: number; endTime: number }> => {
    const sorted = [...subtitles].sort((a, b) => a.startTime - b.startTime)
    const adjusted = []

    for (let i = 0; i < sorted.length; i++) {
      const current = { ...sorted[i] }
      
      if (i > 0) {
        const previous = adjusted[i - 1]
        if (current.startTime < previous.endTime + minGap) {
          current.startTime = previous.endTime + minGap
          
          // 如果调整后开始时间超过了结束时间，也调整结束时间
          if (current.startTime >= current.endTime) {
            current.endTime = current.startTime + 1 // 最少1秒持续时间
          }
        }
      }
      
      adjusted.push(current)
    }

    return adjusted
  },

  // 分割长字幕
  splitLongSubtitle: (
    subtitle: { text: string; startTime: number; endTime: number },
    maxLength: number = 50,
    maxDuration: number = 5
  ): Array<{ text: string; startTime: number; endTime: number }> => {
    const { text, startTime, endTime } = subtitle
    const duration = endTime - startTime

    // 如果字幕不长且持续时间不长，直接返回
    if (text.length <= maxLength && duration <= maxDuration) {
      return [subtitle]
    }

    const words = text.split(' ')
    const parts = []
    let currentPart = ''
    
    for (const word of words) {
      if ((currentPart + ' ' + word).length > maxLength && currentPart.length > 0) {
        parts.push(currentPart.trim())
        currentPart = word
      } else {
        currentPart += (currentPart ? ' ' : '') + word
      }
    }
    
    if (currentPart) {
      parts.push(currentPart.trim())
    }

    // 如果只有一部分，检查是否需要按时间分割
    if (parts.length === 1 && duration > maxDuration) {
      const halfTime = startTime + duration / 2
      return [
        { text: parts[0], startTime, endTime: halfTime - 0.1 },
        { text: parts[0], startTime: halfTime, endTime }
      ]
    }

    // 按部分分配时间
    const partDuration = duration / parts.length
    return parts.map((part, index) => ({
      text: part,
      startTime: startTime + index * partDuration,
      endTime: startTime + (index + 1) * partDuration
    }))
  },

  // 合并相邻的相似字幕
  mergeSimilarSubtitles: (
    subtitles: Array<{ id: string; text: string; startTime: number; endTime: number }>,
    maxGap: number = 0.5,
    similarityThreshold: number = 0.8
  ): Array<{ id: string; text: string; startTime: number; endTime: number }> => {
    const sorted = [...subtitles].sort((a, b) => a.startTime - b.startTime)
    const merged = []

    for (let i = 0; i < sorted.length; i++) {
      const current = sorted[i]
      let merged_current = { ...current }

      // 检查是否可以与下一个字幕合并
      while (i + 1 < sorted.length) {
        const next = sorted[i + 1]
        const gap = next.startTime - merged_current.endTime
        const similarity = subtitleUtils.calculateTextSimilarity(merged_current.text, next.text)

        if (gap <= maxGap && similarity >= similarityThreshold) {
          // 合并字幕
          merged_current.text = merged_current.text + ' ' + next.text
          merged_current.endTime = next.endTime
          i++ // 跳过已合并的字幕
        } else {
          break
        }
      }

      merged.push(merged_current)
    }

    return merged
  },

  // 计算文本相似度
  calculateTextSimilarity: (text1: string, text2: string): number => {
    const words1 = text1.toLowerCase().split(/\s+/)
    const words2 = text2.toLowerCase().split(/\s+/)
    
    const allWords = new Set([...words1, ...words2])
    const vector1 = Array.from(allWords).map(word => words1.filter(w => w === word).length)
    const vector2 = Array.from(allWords).map(word => words2.filter(w => w === word).length)
    
    // 计算余弦相似度
    const dotProduct = vector1.reduce((sum, a, i) => sum + a * vector2[i], 0)
    const magnitude1 = Math.sqrt(vector1.reduce((sum, a) => sum + a * a, 0))
    const magnitude2 = Math.sqrt(vector2.reduce((sum, a) => sum + a * a, 0))
    
    if (magnitude1 === 0 || magnitude2 === 0) return 0
    return dotProduct / (magnitude1 * magnitude2)
  },

  // 验证字幕格式
  validateSubtitle: (subtitle: {
    text: string
    startTime: number
    endTime: number
  }): { isValid: boolean; errors: string[] } => {
    const errors = []

    if (!subtitle.text || subtitle.text.trim().length === 0) {
      errors.push('字幕文本不能为空')
    }

    if (subtitle.startTime < 0) {
      errors.push('开始时间不能为负数')
    }

    if (subtitle.endTime <= subtitle.startTime) {
      errors.push('结束时间必须大于开始时间')
    }

    if (subtitle.endTime - subtitle.startTime < 0.1) {
      errors.push('字幕持续时间至少需要0.1秒')
    }

    if (subtitle.text.length > 200) {
      errors.push('字幕文本过长，建议分割')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  // 生成字幕ID
  generateSubtitleId: (prefix: string = 'subtitle'): string => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  },

  // 估算字幕阅读时间
  estimateReadingTime: (text: string, wordsPerMinute: number = 200): number => {
    const wordCount = text.split(/\s+/).length
    return (wordCount / wordsPerMinute) * 60 // 转换为秒
  }
}
