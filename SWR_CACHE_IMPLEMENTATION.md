# SWR缓存机制实现说明

## 概述

在 `VideoGenerationStep.tsx` 组件中实现了基于 SWR (stale-while-revalidate) 的缓存机制，用于优化镜头任务状态的查询和管理，减少重复的数据库访问。

## 核心特性

### 1. SWR缓存策略
- **自动缓存**: 使用SWR自动缓存API响应数据
- **后台更新**: 在显示缓存数据的同时，后台自动更新最新数据
- **智能重新验证**: 在页面焦点、网络重连时自动刷新数据
- **错误重试**: 自动重试失败的请求，提高可靠性

### 2. 缓存配置
```typescript
{
  // 缓存5分钟，避免重复请求
  dedupingInterval: 5 * 60 * 1000,
  // 页面获得焦点时重新验证
  revalidateOnFocus: true,
  // 网络重连时重新验证
  revalidateOnReconnect: true,
  // 每2分钟自动刷新（适用于处理中的任务）
  refreshInterval: 2 * 60 * 1000,
  // 错误重试配置
  errorRetryCount: 3,
  errorRetryInterval: 5000,
}
```

### 3. 自定义Hook: `useShotTaskStatus`
- **参数**: `projectId`, `userId`, `shotIds[]`
- **返回值**:
  - `taskData`: 缓存的任务数据
  - `isLoading`: 加载状态
  - `error`: 错误信息
  - `updateShotTaskStatus`: 更新特定镜头状态
  - `refreshTaskStatus`: 手动刷新缓存
  - `mutate`: SWR原生mutate函数

### 4. 数据流优化

#### 场景生成集成
- 在 `generateScenesFromShotsWithImages` 中直接使用缓存数据
- 自动将任务状态和视频信息集成到场景对象中
- 减少组件渲染时的数据查询

#### 实时更新
- 视频生成成功后自动刷新缓存
- 手动刷新时使用缓存数据而非直接API调用
- 乐观更新支持，提升用户体验

### 5. UI状态指示器

#### 总览状态
- 显示缓存加载状态
- 显示已完成任务统计
- 显示数据来源（缓存/实时）
- 显示查询时间戳

#### 场景卡片状态
- 💾 已缓存: 数据已从缓存加载
- 🔄 加载中: 正在从缓存加载
- 📝 待缓存: 未找到缓存数据
- 悬停提示显示详细缓存信息

## 技术优势

### 1. 性能优化
- **减少数据库查询**: 避免重复的批量查询
- **快速响应**: 优先显示缓存数据，后台更新
- **智能刷新**: 只在必要时进行数据更新

### 2. 用户体验
- **即时加载**: 缓存数据立即显示
- **实时更新**: 后台自动获取最新状态
- **状态透明**: 清晰的缓存状态指示

### 3. 可靠性
- **错误处理**: 自动重试机制
- **离线支持**: 缓存数据在离线时可用
- **一致性**: 多个组件间数据同步

## API端点

### 缓存查询端点
- **路径**: `/api/video-records/cached`
- **方法**: GET
- **参数**: `projectId`, `userId`, `shotIds`
- **缓存**: 5分钟服务端缓存 + SWR客户端缓存

### 数据格式
```typescript
interface CachedTaskData {
  tasks: Record<string, TaskData[]>
  totalShots: number
  totalCompletedTasks: number
  totalCompletedVideos: number
  hasCompleteVideos: boolean
  queriedAt: string
  cached: boolean
  cacheKey: string
  cacheExpiry: string
}
```

## 使用示例

### 基本用法
```typescript
const { taskData, isLoading, refreshTaskStatus } = useShotTaskStatus(
  projectId, 
  userId, 
  shotIds
)
```

### 手动刷新
```typescript
// 刷新所有缓存数据
await refreshTaskStatus()

// 更新特定镜头状态
await updateShotTaskStatus(shotId, newTaskData)
```

### 状态检查
```typescript
// 检查镜头是否有缓存数据
const hasCachedData = taskData?.tasks?.[shotId]

// 获取最新任务状态
const latestTask = taskData?.tasks?.[shotId]?.[0]
```

## 最佳实践

1. **合理使用刷新**: 避免频繁手动刷新，信任SWR的自动更新机制
2. **错误处理**: 检查 `error` 状态，提供用户友好的错误提示
3. **加载状态**: 使用 `isLoading` 提供适当的加载指示器
4. **缓存失效**: 在数据变更后调用 `refreshTaskStatus()` 确保数据一致性

## 监控和调试

### 开发工具
- 浏览器控制台显示详细的缓存日志
- SWR DevTools 支持（如果安装）
- 网络面板查看API调用频率

### 性能指标
- 缓存命中率
- API调用减少量
- 页面加载速度提升
- 用户交互响应时间

## 未来扩展

1. **离线支持**: 集成Service Worker实现完全离线缓存
2. **实时同步**: WebSocket集成实现实时状态更新
3. **智能预取**: 基于用户行为预取相关数据
4. **缓存分析**: 添加缓存使用分析和优化建议
