// 集成状态检查工具
// 用于验证所有组件和依赖是否正确导入

export const checkIntegrationStatus = () => {
  const results = {
    components: {},
    stores: {},
    dependencies: {},
    apis: {},
    overall: 'unknown' as 'success' | 'warning' | 'error' | 'unknown'
  }

  console.log('🔍 [INTEGRATION-CHECK] 开始检查集成状态...')

  // 检查核心组件
  try {
    // 检查 IntegratedVideoEditor
    import('./components/IntegratedVideoEditor').then(() => {
      results.components['IntegratedVideoEditor'] = 'success'
      console.log('✅ IntegratedVideoEditor 组件导入成功')
    }).catch((error) => {
      results.components['IntegratedVideoEditor'] = 'error'
      console.error('❌ IntegratedVideoEditor 组件导入失败:', error)
    })

    // 检查其他核心组件
    const componentChecks = [
      'VideoEditor',
      'Timeline', 
      'MultiTrackPreview',
      'DragDropProvider',
      'AudioEditor',
      'SubtitleEditor',
      'VideoExporter'
    ]

    componentChecks.forEach(componentName => {
      try {
        import('./components').then((module) => {
          if (module[componentName]) {
            results.components[componentName] = 'success'
            console.log(`✅ ${componentName} 组件可用`)
          } else {
            results.components[componentName] = 'warning'
            console.warn(`⚠️ ${componentName} 组件未找到`)
          }
        }).catch((error) => {
          results.components[componentName] = 'error'
          console.error(`❌ ${componentName} 组件检查失败:`, error)
        })
      } catch (error) {
        results.components[componentName] = 'error'
        console.error(`❌ ${componentName} 组件检查异常:`, error)
      }
    })

  } catch (error) {
    console.error('❌ 组件检查过程中发生错误:', error)
  }

  // 检查状态管理
  try {
    import('./stores').then((storeModule) => {
      if (storeModule.useVideoEditor) {
        results.stores['useVideoEditor'] = 'success'
        console.log('✅ useVideoEditor store 可用')
      } else {
        results.stores['useVideoEditor'] = 'error'
        console.error('❌ useVideoEditor store 未找到')
      }
    }).catch((error) => {
      results.stores['useVideoEditor'] = 'error'
      console.error('❌ stores 模块导入失败:', error)
    })
  } catch (error) {
    results.stores['useVideoEditor'] = 'error'
    console.error('❌ stores 检查异常:', error)
  }

  // 检查关键依赖
  const dependencies = [
    'react',
    'swr',
    'zustand',
    'konva',
    'react-konva',
    'react-dnd',
    'react-dnd-html5-backend'
  ]

  dependencies.forEach(dep => {
    try {
      // 注意：在浏览器环境中，这种检查方式可能不适用
      // 这里主要用于开发时的静态检查
      results.dependencies[dep] = 'unknown'
      console.log(`📦 ${dep} 依赖状态: 需要运行时检查`)
    } catch (error) {
      results.dependencies[dep] = 'error'
      console.error(`❌ ${dep} 依赖检查失败:`, error)
    }
  })

  // 检查 API 端点（模拟检查）
  const apiEndpoints = [
    '/api/video-records/cached',
    '/api/image-records/cached'
  ]

  apiEndpoints.forEach(endpoint => {
    // 这里只是记录需要检查的端点，实际检查需要在运行时进行
    results.apis[endpoint] = 'unknown'
    console.log(`🌐 API 端点 ${endpoint} 需要运行时检查`)
  })

  // 计算总体状态
  const componentStatuses = Object.values(results.components)
  const storeStatuses = Object.values(results.stores)
  
  const hasErrors = [...componentStatuses, ...storeStatuses].some(status => status === 'error')
  const hasWarnings = [...componentStatuses, ...storeStatuses].some(status => status === 'warning')
  
  if (hasErrors) {
    results.overall = 'error'
    console.log('🔴 集成状态: 存在错误')
  } else if (hasWarnings) {
    results.overall = 'warning'
    console.log('🟡 集成状态: 存在警告')
  } else {
    results.overall = 'success'
    console.log('🟢 集成状态: 良好')
  }

  console.log('🔍 [INTEGRATION-CHECK] 检查完成:', results)
  return results
}

// 运行时 API 检查
export const checkApiEndpoints = async () => {
  console.log('🌐 [API-CHECK] 开始检查 API 端点...')
  
  const results = {
    videoCache: 'unknown' as 'success' | 'error' | 'unknown',
    imageCache: 'unknown' as 'success' | 'error' | 'unknown'
  }

  // 检查视频缓存 API
  try {
    const response = await fetch('/api/video-records/cached', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        projectId: 'test-project',
        userId: 'test-user',
        shotIds: ['test-shot']
      })
    })
    
    if (response.ok) {
      results.videoCache = 'success'
      console.log('✅ 视频缓存 API 可用')
    } else {
      results.videoCache = 'error'
      console.log(`⚠️ 视频缓存 API 返回状态: ${response.status}`)
    }
  } catch (error) {
    results.videoCache = 'error'
    console.log('❌ 视频缓存 API 检查失败:', error)
  }

  // 检查图片缓存 API
  try {
    const response = await fetch('/api/image-records/cached', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        projectId: 'test-project',
        userId: 'test-user',
        shotNumbers: [1]
      })
    })
    
    if (response.ok) {
      results.imageCache = 'success'
      console.log('✅ 图片缓存 API 可用')
    } else {
      results.imageCache = 'error'
      console.log(`⚠️ 图片缓存 API 返回状态: ${response.status}`)
    }
  } catch (error) {
    results.imageCache = 'error'
    console.log('❌ 图片缓存 API 检查失败:', error)
  }

  console.log('🌐 [API-CHECK] API 检查完成:', results)
  return results
}

// 集成健康检查
export const performHealthCheck = async () => {
  console.log('🏥 [HEALTH-CHECK] 开始集成健康检查...')
  
  const integrationStatus = checkIntegrationStatus()
  const apiStatus = await checkApiEndpoints()
  
  const healthReport = {
    timestamp: new Date().toISOString(),
    integration: integrationStatus,
    apis: apiStatus,
    recommendations: [] as string[]
  }

  // 生成建议
  if (integrationStatus.overall === 'error') {
    healthReport.recommendations.push('请检查组件导入和依赖安装')
  }
  
  if (apiStatus.videoCache === 'error') {
    healthReport.recommendations.push('请确保视频缓存 API 端点正常工作')
  }
  
  if (apiStatus.imageCache === 'error') {
    healthReport.recommendations.push('请确保图片缓存 API 端点正常工作')
  }

  if (healthReport.recommendations.length === 0) {
    healthReport.recommendations.push('集成状态良好，可以正常使用')
  }

  console.log('🏥 [HEALTH-CHECK] 健康检查完成:', healthReport)
  return healthReport
}

// 导出检查函数供外部使用
export default {
  checkIntegrationStatus,
  checkApiEndpoints,
  performHealthCheck
}
