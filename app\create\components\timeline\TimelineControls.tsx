"use client"

import React from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>lider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  ZoomIn,
  ZoomOut,
  Grid3X3,
  Waveform,
  Image,
  Scissors,
  Move,
  RotateCcw,
  RotateCw,
  Settings
} from "lucide-react"
import { useVideoEditor } from '../../stores'

interface TimelineControlsProps {
  onPlay?: () => void
  onPause?: () => void
  onStop?: () => void
  onPrevious?: () => void
  onNext?: () => void
}

export const TimelineControls: React.FC<TimelineControlsProps> = ({
  onPlay,
  onPause,
  onStop,
  onPrevious,
  onNext
}) => {
  const {
    timeline,
    ui,
    actions,
    setPlaying,
    setZoomLevel,
    setPlaybackRate,
    toggleGrid,
    toggleWaveforms,
    toggleThumbnails,
    toggleSnapToGrid,
    setSelectedTool,
    setSnapThreshold,
    canUndo,
    canRedo
  } = useVideoEditor()

  const handlePlayPause = () => {
    if (timeline.isPlaying) {
      setPlaying(false)
      onPause?.()
    } else {
      setPlaying(true)
      onPlay?.()
    }
  }

  const handleZoomIn = () => {
    const newZoom = Math.min(timeline.zoomLevel * 1.2, 10)
    setZoomLevel(newZoom)
  }

  const handleZoomOut = () => {
    const newZoom = Math.max(timeline.zoomLevel / 1.2, 0.1)
    setZoomLevel(newZoom)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="timeline-controls bg-white border-b border-gray-200 p-4">
      <div className="flex items-center justify-between">
        {/* 左侧：播放控制 */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              onPrevious?.()
            }}
            className="w-8 h-8 p-0"
          >
            <SkipBack className="w-4 h-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handlePlayPause}
            className="w-10 h-10 p-0"
          >
            {timeline.isPlaying ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5" />
            )}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setPlaying(false)
              onStop?.()
            }}
            className="w-8 h-8 p-0"
          >
            <div className="w-3 h-3 bg-current" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              onNext?.()
            }}
            className="w-8 h-8 p-0"
          >
            <SkipForward className="w-4 h-4" />
          </Button>

          {/* 时间显示 */}
          <div className="flex items-center space-x-2 ml-4">
            <span className="text-sm font-mono text-gray-600">
              {formatTime(timeline.currentTime)}
            </span>
            <span className="text-sm text-gray-400">/</span>
            <span className="text-sm font-mono text-gray-600">
              {formatTime(timeline.duration)}
            </span>
          </div>

          {/* 播放速度 */}
          <div className="flex items-center space-x-2 ml-4">
            <Label className="text-xs text-gray-600">速度</Label>
            <Select
              value={timeline.playbackRate.toString()}
              onValueChange={(value) => setPlaybackRate(parseFloat(value))}
            >
              <SelectTrigger className="w-16 h-7 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0.25">0.25x</SelectItem>
                <SelectItem value="0.5">0.5x</SelectItem>
                <SelectItem value="1">1x</SelectItem>
                <SelectItem value="1.5">1.5x</SelectItem>
                <SelectItem value="2">2x</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 中间：工具选择 */}
        <div className="flex items-center space-x-1">
          <Button
            variant={ui.selectedTool === 'select' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedTool('select')}
            className="w-8 h-8 p-0"
            title="选择工具"
          >
            <Move className="w-4 h-4" />
          </Button>

          <Button
            variant={ui.selectedTool === 'cut' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedTool('cut')}
            className="w-8 h-8 p-0"
            title="剪切工具"
          >
            <Scissors className="w-4 h-4" />
          </Button>

          <Button
            variant={ui.selectedTool === 'zoom' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedTool('zoom')}
            className="w-8 h-8 p-0"
            title="缩放工具"
          >
            <ZoomIn className="w-4 h-4" />
          </Button>

          <Button
            variant={ui.selectedTool === 'hand' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedTool('hand')}
            className="w-8 h-8 p-0"
            title="手型工具"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>

        {/* 右侧：视图控制和设置 */}
        <div className="flex items-center space-x-4">
          {/* 撤销重做 */}
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={actions.undo}
              disabled={!canUndo}
              className="w-8 h-8 p-0"
              title="撤销 (Ctrl+Z)"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={actions.redo}
              disabled={!canRedo}
              className="w-8 h-8 p-0"
              title="重做 (Ctrl+Y)"
            >
              <RotateCw className="w-4 h-4" />
            </Button>
          </div>

          {/* 缩放控制 */}
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleZoomOut}
              className="w-8 h-8 p-0"
            >
              <ZoomOut className="w-4 h-4" />
            </Button>

            <div className="w-20">
              <Slider
                value={[timeline.zoomLevel]}
                onValueChange={([value]) => setZoomLevel(value)}
                min={0.1}
                max={10}
                step={0.1}
                className="w-full"
              />
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleZoomIn}
              className="w-8 h-8 p-0"
            >
              <ZoomIn className="w-4 h-4" />
            </Button>

            <span className="text-xs text-gray-600 min-w-[40px]">
              {Math.round(timeline.zoomLevel * 100)}%
            </span>
          </div>

          {/* 显示选项 */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-1">
              <Switch
                checked={ui.showGrid}
                onCheckedChange={toggleGrid}
                className="data-[state=checked]:bg-blue-500"
              />
              <Grid3X3 className="w-4 h-4 text-gray-600" />
            </div>

            <div className="flex items-center space-x-1">
              <Switch
                checked={ui.showWaveforms}
                onCheckedChange={toggleWaveforms}
                className="data-[state=checked]:bg-blue-500"
              />
              <Waveform className="w-4 h-4 text-gray-600" />
            </div>

            <div className="flex items-center space-x-1">
              <Switch
                checked={ui.showThumbnails}
                onCheckedChange={toggleThumbnails}
                className="data-[state=checked]:bg-blue-500"
              />
              <Image className="w-4 h-4 text-gray-600" />
            </div>
          </div>

          {/* 吸附设置 */}
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <Switch
                checked={ui.snapToGrid}
                onCheckedChange={toggleSnapToGrid}
                className="data-[state=checked]:bg-green-500"
              />
              <Label className="text-xs text-gray-600">吸附</Label>
            </div>

            {ui.snapToGrid && (
              <div className="flex items-center space-x-1">
                <Label className="text-xs text-gray-600">阈值</Label>
                <div className="w-16">
                  <Slider
                    value={[ui.snapThreshold]}
                    onValueChange={([value]) => setSnapThreshold(value)}
                    min={0.01}
                    max={1}
                    step={0.01}
                    className="w-full"
                  />
                </div>
                <span className="text-xs text-gray-600 min-w-[30px]">
                  {Math.round(ui.snapThreshold * 100)}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 第二行：额外控制 */}
      <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
        <div className="flex items-center space-x-4">
          {/* 选中片段信息 */}
          {timeline.selectedClips.length > 0 && (
            <div className="text-sm text-gray-600">
              已选择 {timeline.selectedClips.length} 个片段
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* 智能模式 */}
          <div className="flex items-center space-x-2">
            <Switch
              checked={ui.isSmartMode}
              onCheckedChange={() => {
                // toggleSmartMode()
              }}
              className="data-[state=checked]:bg-emerald-500"
            />
            <Label className="text-sm text-gray-600">智能模式</Label>
          </div>

          {/* 时间轴统计信息 */}
          <div className="text-xs text-gray-500">
            缩放: {Math.round(timeline.zoomLevel * 100)}% | 
            像素/秒: {Math.round(timeline.zoomLevel * 20)}
          </div>
        </div>
      </div>
    </div>
  )
}
