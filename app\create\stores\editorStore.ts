import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { 
  EditorState, 
  VideoClip, 
  AudioClip, 
  SubtitleClip, 
  TimelineState, 
  ViewportState, 
  ProjectSettings,
  DragState,
  UIState,
  CacheState
} from './types'

// 初始状态
const initialTimelineState: TimelineState = {
  currentTime: 0,
  duration: 100,
  isPlaying: false,
  playbackRate: 1,
  zoomLevel: 1,
  scrollPosition: 0,
  selectedClips: [],
  clipboardClips: []
}

const initialViewportState: ViewportState = {
  width: 1200,
  height: 400,
  pixelsPerSecond: 20,
  visibleTimeRange: {
    start: 0,
    end: 60
  }
}

const initialProjectSettings: ProjectSettings = {
  name: 'AI生成内容',
  description: '',
  resolution: {
    width: 1920,
    height: 1080
  },
  frameRate: 30,
  outputFormat: 'mp4',
  quality: 'high'
}

const initialDragState: DragState = {
  isDragging: false,
  dragType: null,
  draggedItems: [],
  dropTarget: null,
  previewPosition: null
}

const initialUIState: UIState = {
  selectedTool: 'select',
  showWaveforms: true,
  showThumbnails: true,
  showGrid: true,
  snapToGrid: true,
  snapThreshold: 0.1,
  isSmartMode: true,
  sidebarWidth: 320,
  timelineHeight: 400,
  previewPanelHeight: 400
}

const initialCacheState: CacheState = {
  videoTaskData: null,
  imageTaskData: null,
  isVideoLoading: false,
  isImageLoading: false,
  videoError: null,
  imageError: null,
  lastRefresh: 0
}

interface EditorStore {
  // 状态
  videoClips: VideoClip[]
  audioClips: AudioClip[]
  subtitleClips: SubtitleClip[]
  timeline: TimelineState
  viewport: ViewportState
  project: ProjectSettings
  drag: DragState
  ui: UIState
  cache: CacheState

  // 视频片段操作
  addVideoClip: (clip: VideoClip) => void
  removeVideoClip: (id: string) => void
  updateVideoClip: (id: string, updates: Partial<VideoClip>) => void
  duplicateVideoClip: (id: string) => void

  // 音频片段操作
  addAudioClip: (clip: AudioClip) => void
  removeAudioClip: (id: string) => void
  updateAudioClip: (id: string, updates: Partial<AudioClip>) => void

  // 字幕片段操作
  addSubtitleClip: (clip: SubtitleClip) => void
  removeSubtitleClip: (id: string) => void
  updateSubtitleClip: (id: string, updates: Partial<SubtitleClip>) => void

  // 时间轴操作
  setCurrentTime: (time: number) => void
  setPlaying: (playing: boolean) => void
  setPlaybackRate: (rate: number) => void
  setZoomLevel: (level: number) => void
  setScrollPosition: (position: number) => void

  // 选择操作
  selectClips: (ids: string[]) => void
  addToSelection: (id: string) => void
  removeFromSelection: (id: string) => void
  clearSelection: () => void

  // 剪贴板操作
  copySelectedClips: () => void
  cutSelectedClips: () => void
  pasteClips: (time: number, track?: number) => void

  // 拖拽操作
  startDrag: (type: 'video' | 'audio' | 'subtitle', items: string[]) => void
  updateDragPreview: (track: number, time: number) => void
  endDrag: (track: number, time: number) => void
  cancelDrag: () => void

  // UI 操作
  setSelectedTool: (tool: UIState['selectedTool']) => void
  toggleWaveforms: () => void
  toggleThumbnails: () => void
  toggleGrid: () => void
  toggleSnapToGrid: () => void
  setSnapThreshold: (threshold: number) => void
  toggleSmartMode: () => void

  // 缓存操作
  setCacheData: (videoData: any, imageData: any) => void
  setCacheLoading: (videoLoading: boolean, imageLoading: boolean) => void
  setCacheError: (videoError: any, imageError: any) => void
  refreshCache: () => void

  // 项目操作
  updateProject: (updates: Partial<ProjectSettings>) => void
  resetProject: () => void

  // 工具函数
  getClipById: (id: string) => VideoClip | AudioClip | SubtitleClip | null
  getSelectedClips: () => (VideoClip | AudioClip | SubtitleClip)[]
  calculateTotalDuration: () => number
  timeToPixels: (time: number) => number
  pixelsToTime: (pixels: number) => number
  snapToGrid: (time: number) => number
}

export const useEditorStore = create<EditorStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // 初始状态
      videoClips: [],
      audioClips: [],
      subtitleClips: [],
      timeline: initialTimelineState,
      viewport: initialViewportState,
      project: initialProjectSettings,
      drag: initialDragState,
      ui: initialUIState,
      cache: initialCacheState,

      // 视频片段操作
      addVideoClip: (clip) => set((state) => {
        state.videoClips.push(clip)
        // 自动更新总时长
        const maxEndTime = Math.max(...state.videoClips.map(c => c.endTime), state.timeline.duration)
        state.timeline.duration = maxEndTime
      }),

      removeVideoClip: (id) => set((state) => {
        state.videoClips = state.videoClips.filter(clip => clip.id !== id)
        state.timeline.selectedClips = state.timeline.selectedClips.filter(clipId => clipId !== id)
      }),

      updateVideoClip: (id, updates) => set((state) => {
        const clipIndex = state.videoClips.findIndex(clip => clip.id === id)
        if (clipIndex !== -1) {
          Object.assign(state.videoClips[clipIndex], updates)
        }
      }),

      duplicateVideoClip: (id) => set((state) => {
        const clip = state.videoClips.find(c => c.id === id)
        if (clip) {
          const newClip = {
            ...clip,
            id: `${clip.id}-copy-${Date.now()}`,
            startTime: clip.endTime,
            endTime: clip.endTime + clip.duration
          }
          state.videoClips.push(newClip)
        }
      }),

      // 音频片段操作
      addAudioClip: (clip) => set((state) => {
        state.audioClips.push(clip)
      }),

      removeAudioClip: (id) => set((state) => {
        state.audioClips = state.audioClips.filter(clip => clip.id !== id)
        state.timeline.selectedClips = state.timeline.selectedClips.filter(clipId => clipId !== id)
      }),

      updateAudioClip: (id, updates) => set((state) => {
        const clipIndex = state.audioClips.findIndex(clip => clip.id === id)
        if (clipIndex !== -1) {
          Object.assign(state.audioClips[clipIndex], updates)
        }
      }),

      // 字幕片段操作
      addSubtitleClip: (clip) => set((state) => {
        state.subtitleClips.push(clip)
      }),

      removeSubtitleClip: (id) => set((state) => {
        state.subtitleClips = state.subtitleClips.filter(clip => clip.id !== id)
        state.timeline.selectedClips = state.timeline.selectedClips.filter(clipId => clipId !== id)
      }),

      updateSubtitleClip: (id, updates) => set((state) => {
        const clipIndex = state.subtitleClips.findIndex(clip => clip.id === id)
        if (clipIndex !== -1) {
          Object.assign(state.subtitleClips[clipIndex], updates)
        }
      }),

      // 时间轴操作
      setCurrentTime: (time) => set((state) => {
        state.timeline.currentTime = Math.max(0, Math.min(time, state.timeline.duration))
      }),

      setPlaying: (playing) => set((state) => {
        state.timeline.isPlaying = playing
      }),

      setPlaybackRate: (rate) => set((state) => {
        state.timeline.playbackRate = Math.max(0.1, Math.min(rate, 4))
      }),

      setZoomLevel: (level) => set((state) => {
        state.timeline.zoomLevel = Math.max(0.1, Math.min(level, 10))
        // 更新视口的像素每秒比例
        state.viewport.pixelsPerSecond = 20 * level
        // 更新可见时间范围
        const visibleDuration = state.viewport.width / state.viewport.pixelsPerSecond
        state.viewport.visibleTimeRange.end = state.viewport.visibleTimeRange.start + visibleDuration
      }),

      setScrollPosition: (position) => set((state) => {
        state.timeline.scrollPosition = Math.max(0, position)
        // 更新可见时间范围
        const visibleDuration = state.viewport.width / state.viewport.pixelsPerSecond
        state.viewport.visibleTimeRange.start = position / state.viewport.pixelsPerSecond
        state.viewport.visibleTimeRange.end = state.viewport.visibleTimeRange.start + visibleDuration
      }),

      // 选择操作
      selectClips: (ids) => set((state) => {
        state.timeline.selectedClips = ids
      }),

      addToSelection: (id) => set((state) => {
        if (!state.timeline.selectedClips.includes(id)) {
          state.timeline.selectedClips.push(id)
        }
      }),

      removeFromSelection: (id) => set((state) => {
        state.timeline.selectedClips = state.timeline.selectedClips.filter(clipId => clipId !== id)
      }),

      clearSelection: () => set((state) => {
        state.timeline.selectedClips = []
      }),

      // 剪贴板操作
      copySelectedClips: () => set((state) => {
        const selectedClips = get().getSelectedClips()
        state.timeline.clipboardClips = selectedClips.map(clip => ({ ...clip }))
      }),

      cutSelectedClips: () => set((state) => {
        const selectedIds = state.timeline.selectedClips
        get().copySelectedClips()
        
        // 删除选中的片段
        state.videoClips = state.videoClips.filter(clip => !selectedIds.includes(clip.id))
        state.audioClips = state.audioClips.filter(clip => !selectedIds.includes(clip.id))
        state.subtitleClips = state.subtitleClips.filter(clip => !selectedIds.includes(clip.id))
        state.timeline.selectedClips = []
      }),

      pasteClips: (time, track = 0) => set((state) => {
        const clipboardClips = state.timeline.clipboardClips
        if (clipboardClips.length === 0) return

        clipboardClips.forEach((clip, index) => {
          const newClip = {
            ...clip,
            id: `${clip.id}-paste-${Date.now()}-${index}`,
            startTime: time + (index * 0.1), // 稍微错开时间避免重叠
            endTime: time + (index * 0.1) + ('duration' in clip ? clip.duration : 5),
            track: track
          }

          if ('videoUrl' in clip) {
            state.videoClips.push(newClip as VideoClip)
          } else if ('audioUrl' in clip) {
            state.audioClips.push(newClip as AudioClip)
          } else if ('text' in clip) {
            state.subtitleClips.push(newClip as SubtitleClip)
          }
        })
      }),

      // 拖拽操作
      startDrag: (type, items) => set((state) => {
        state.drag.isDragging = true
        state.drag.dragType = type
        state.drag.draggedItems = items
      }),

      updateDragPreview: (track, time) => set((state) => {
        state.drag.previewPosition = { track, time }
      }),

      endDrag: (track, time) => set((state) => {
        if (state.drag.isDragging && state.drag.draggedItems.length > 0) {
          // 移动拖拽的片段到新位置
          const { draggedItems, dragType } = state.drag
          
          draggedItems.forEach((id, index) => {
            const offsetTime = time + (index * 0.1)
            
            if (dragType === 'video') {
              get().updateVideoClip(id, { 
                startTime: offsetTime, 
                endTime: offsetTime + (get().getClipById(id) as VideoClip)?.duration || 5,
                track 
              })
            } else if (dragType === 'audio') {
              get().updateAudioClip(id, { 
                startTime: offsetTime, 
                endTime: offsetTime + (get().getClipById(id) as AudioClip)?.duration || 5,
                track 
              })
            } else if (dragType === 'subtitle') {
              get().updateSubtitleClip(id, { 
                startTime: offsetTime, 
                endTime: offsetTime + 5,
                track 
              })
            }
          })
        }
        
        // 重置拖拽状态
        state.drag = initialDragState
      }),

      cancelDrag: () => set((state) => {
        state.drag = initialDragState
      }),

      // UI 操作
      setSelectedTool: (tool) => set((state) => {
        state.ui.selectedTool = tool
      }),

      toggleWaveforms: () => set((state) => {
        state.ui.showWaveforms = !state.ui.showWaveforms
      }),

      toggleThumbnails: () => set((state) => {
        state.ui.showThumbnails = !state.ui.showThumbnails
      }),

      toggleGrid: () => set((state) => {
        state.ui.showGrid = !state.ui.showGrid
      }),

      toggleSnapToGrid: () => set((state) => {
        state.ui.snapToGrid = !state.ui.snapToGrid
      }),

      setSnapThreshold: (threshold) => set((state) => {
        state.ui.snapThreshold = Math.max(0.01, Math.min(threshold, 1))
      }),

      toggleSmartMode: () => set((state) => {
        state.ui.isSmartMode = !state.ui.isSmartMode
      }),

      // 缓存操作
      setCacheData: (videoData, imageData) => set((state) => {
        state.cache.videoTaskData = videoData
        state.cache.imageTaskData = imageData
        state.cache.lastRefresh = Date.now()
      }),

      setCacheLoading: (videoLoading, imageLoading) => set((state) => {
        state.cache.isVideoLoading = videoLoading
        state.cache.isImageLoading = imageLoading
      }),

      setCacheError: (videoError, imageError) => set((state) => {
        state.cache.videoError = videoError
        state.cache.imageError = imageError
      }),

      refreshCache: () => set((state) => {
        state.cache.lastRefresh = Date.now()
      }),

      // 项目操作
      updateProject: (updates) => set((state) => {
        Object.assign(state.project, updates)
      }),

      resetProject: () => set((state) => {
        state.videoClips = []
        state.audioClips = []
        state.subtitleClips = []
        state.timeline = initialTimelineState
        state.project = initialProjectSettings
        state.drag = initialDragState
        state.ui = initialUIState
      }),

      // 工具函数
      getClipById: (id) => {
        const state = get()
        return state.videoClips.find(c => c.id === id) ||
               state.audioClips.find(c => c.id === id) ||
               state.subtitleClips.find(c => c.id === id) ||
               null
      },

      getSelectedClips: () => {
        const state = get()
        const selectedIds = state.timeline.selectedClips
        return [
          ...state.videoClips.filter(c => selectedIds.includes(c.id)),
          ...state.audioClips.filter(c => selectedIds.includes(c.id)),
          ...state.subtitleClips.filter(c => selectedIds.includes(c.id))
        ]
      },

      calculateTotalDuration: () => {
        const state = get()
        const videoMaxTime = Math.max(0, ...state.videoClips.map(c => c.endTime))
        const audioMaxTime = Math.max(0, ...state.audioClips.map(c => c.endTime))
        const subtitleMaxTime = Math.max(0, ...state.subtitleClips.map(c => c.endTime))
        return Math.max(videoMaxTime, audioMaxTime, subtitleMaxTime, 10) // 最少10秒
      },

      timeToPixels: (time) => {
        const state = get()
        return time * state.viewport.pixelsPerSecond
      },

      pixelsToTime: (pixels) => {
        const state = get()
        return pixels / state.viewport.pixelsPerSecond
      },

      snapToGrid: (time) => {
        const state = get()
        if (!state.ui.snapToGrid) return time
        
        const gridInterval = 1 / state.timeline.zoomLevel // 网格间隔随缩放调整
        const snappedTime = Math.round(time / gridInterval) * gridInterval
        
        // 如果在阈值范围内，则吸附
        if (Math.abs(time - snappedTime) <= state.ui.snapThreshold) {
          return snappedTime
        }
        return time
      }
    }))
  )
)
