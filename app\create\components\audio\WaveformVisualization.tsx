"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react'
import WaveSurfer from 'wavesurfer.js'
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Play, Pause, Volume2, VolumeX, SkipBack, SkipForward } from "lucide-react"

interface WaveformVisualizationProps {
  audioUrl?: string
  audioBuffer?: ArrayBuffer
  width?: number
  height?: number
  waveColor?: string
  progressColor?: string
  cursorColor?: string
  backgroundColor?: string
  responsive?: boolean
  normalize?: boolean
  interact?: boolean
  hideScrollbar?: boolean
  onReady?: (wavesurfer: WaveSurfer) => void
  onPlay?: () => void
  onPause?: () => void
  onSeek?: (position: number) => void
  onTimeUpdate?: (currentTime: number) => void
  onFinish?: () => void
  className?: string
}

export const WaveformVisualization: React.FC<WaveformVisualizationProps> = ({
  audioUrl,
  audioBuffer,
  width = 800,
  height = 128,
  waveColor = '#6366f1',
  progressColor = '#3b82f6',
  cursorColor = '#ef4444',
  backgroundColor = 'transparent',
  responsive = true,
  normalize = true,
  interact = true,
  hideScrollbar = true,
  onReady,
  onPlay,
  onPause,
  onSeek,
  onTimeUpdate,
  onFinish,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const wavesurferRef = useRef<WaveSurfer | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [volume, setVolume] = useState([80])
  const [isMuted, setIsMuted] = useState(false)

  // 初始化 WaveSurfer
  useEffect(() => {
    if (!containerRef.current) return

    const wavesurfer = WaveSurfer.create({
      container: containerRef.current,
      width: responsive ? undefined : width,
      height,
      waveColor,
      progressColor,
      cursorColor,
      backgroundColor,
      responsive,
      normalize,
      interact,
      hideScrollbar,
      barWidth: 2,
      barGap: 1,
      barRadius: 1,
      cursorWidth: 2,
      backend: 'WebAudio',
      mediaControls: false,
      autoplay: false,
      scrollParent: true,
      fillParent: true
    })

    wavesurferRef.current = wavesurfer

    // 事件监听
    wavesurfer.on('ready', () => {
      setIsLoading(false)
      setDuration(wavesurfer.getDuration())
      onReady?.(wavesurfer)
    })

    wavesurfer.on('loading', (percent) => {
      setIsLoading(percent < 100)
    })

    wavesurfer.on('play', () => {
      setIsPlaying(true)
      onPlay?.()
    })

    wavesurfer.on('pause', () => {
      setIsPlaying(false)
      onPause?.()
    })

    wavesurfer.on('seek', (position) => {
      const time = position * wavesurfer.getDuration()
      setCurrentTime(time)
      onSeek?.(position)
    })

    wavesurfer.on('audioprocess', (time) => {
      setCurrentTime(time)
      onTimeUpdate?.(time)
    })

    wavesurfer.on('finish', () => {
      setIsPlaying(false)
      onFinish?.()
    })

    wavesurfer.on('error', (error) => {
      console.error('WaveSurfer error:', error)
      setIsLoading(false)
    })

    // 清理函数
    return () => {
      if (wavesurferRef.current) {
        wavesurferRef.current.destroy()
        wavesurferRef.current = null
      }
    }
  }, [
    width, height, waveColor, progressColor, cursorColor, backgroundColor,
    responsive, normalize, interact, hideScrollbar, onReady, onPlay, onPause,
    onSeek, onTimeUpdate, onFinish
  ])

  // 加载音频
  useEffect(() => {
    if (!wavesurferRef.current) return

    if (audioUrl) {
      setIsLoading(true)
      wavesurferRef.current.load(audioUrl)
    } else if (audioBuffer) {
      setIsLoading(true)
      wavesurferRef.current.loadBlob(new Blob([audioBuffer]))
    }
  }, [audioUrl, audioBuffer])

  // 音量控制
  useEffect(() => {
    if (wavesurferRef.current) {
      const volumeValue = isMuted ? 0 : volume[0] / 100
      wavesurferRef.current.setVolume(volumeValue)
    }
  }, [volume, isMuted])

  // 播放/暂停
  const handlePlayPause = useCallback(() => {
    if (wavesurferRef.current) {
      wavesurferRef.current.playPause()
    }
  }, [])

  // 停止
  const handleStop = useCallback(() => {
    if (wavesurferRef.current) {
      wavesurferRef.current.stop()
      setIsPlaying(false)
      setCurrentTime(0)
    }
  }, [])

  // 跳转
  const handleSeek = useCallback((time: number) => {
    if (wavesurferRef.current && duration > 0) {
      const position = Math.max(0, Math.min(time / duration, 1))
      wavesurferRef.current.seekTo(position)
    }
  }, [duration])

  // 跳转到开始/结束
  const handleSkipToStart = useCallback(() => {
    handleSeek(0)
  }, [handleSeek])

  const handleSkipToEnd = useCallback(() => {
    handleSeek(duration)
  }, [handleSeek, duration])

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className={`waveform-visualization ${className}`}>
      {/* 波形容器 */}
      <div className="relative">
        <div
          ref={containerRef}
          className="waveform-container border border-gray-200 rounded-lg bg-white"
          style={{ minHeight: height }}
        />
        
        {/* 加载指示器 */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-600">加载音频...</span>
            </div>
          </div>
        )}

        {/* 无音频提示 */}
        {!audioUrl && !audioBuffer && !isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="text-4xl mb-2">🎵</div>
              <p className="text-sm text-gray-500">暂无音频文件</p>
            </div>
          </div>
        )}
      </div>

      {/* 控制面板 */}
      <div className="mt-4 flex items-center justify-between">
        {/* 播放控制 */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleSkipToStart}
            disabled={!wavesurferRef.current || isLoading}
            className="w-8 h-8 p-0"
          >
            <SkipBack className="w-4 h-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handlePlayPause}
            disabled={!wavesurferRef.current || isLoading}
            className="w-10 h-10 p-0"
          >
            {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleStop}
            disabled={!wavesurferRef.current || isLoading}
            className="w-8 h-8 p-0"
          >
            <div className="w-3 h-3 bg-current" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleSkipToEnd}
            disabled={!wavesurferRef.current || isLoading}
            className="w-8 h-8 p-0"
          >
            <SkipForward className="w-4 h-4" />
          </Button>
        </div>

        {/* 时间显示 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-mono text-gray-600">
            {formatTime(currentTime)}
          </span>
          <span className="text-sm text-gray-400">/</span>
          <span className="text-sm font-mono text-gray-600">
            {formatTime(duration)}
          </span>
        </div>

        {/* 音量控制 */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsMuted(!isMuted)}
            className="w-8 h-8 p-0"
          >
            {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
          </Button>

          <div className="w-20">
            <Slider
              value={volume}
              onValueChange={setVolume}
              max={100}
              step={1}
              className="w-full"
              disabled={isMuted}
            />
          </div>

          <span className="text-xs text-gray-500 min-w-[30px]">
            {isMuted ? '0%' : `${volume[0]}%`}
          </span>
        </div>
      </div>

      {/* 音频信息 */}
      {duration > 0 && (
        <div className="mt-2 text-xs text-gray-500">
          时长: {formatTime(duration)} | 
          采样率: 44.1kHz | 
          声道: 立体声
        </div>
      )}
    </div>
  )
}

// 导出 WaveSurfer 实例类型
export type { WaveSurfer }
