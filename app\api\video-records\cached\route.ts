import { NextRequest, NextResponse } from 'next/server'
import { getVideoGenerationTasksByProjectUserAndShots } from '@/lib/prisma-video-operations'
import { unstable_cache } from 'next/cache'

// 创建缓存函数，每5分钟revalidate一次
const getCachedVideoRecords = unstable_cache(
  async (projectId: string, userId: string, shotIds: string[]) => {
    console.log(`🔍 [CACHED-VIDEO-QUERY] 执行数据库查询: ${shotIds.length} 个镜头`)
    
    // 批量查询所有镜头的最新任务
    const tasks = await getVideoGenerationTasksByProjectUserAndShots(projectId, userId, shotIds)
    
    console.log(`✅ [CACHED-VIDEO-QUERY] 数据库查询成功，找到 ${tasks?.length || 0} 个任务`)
    
    // 按镜头ID分组，每个镜头只保留最新的任务
    const tasksByShot: Record<string, any[]> = {}
    
    if (tasks) {
      for (const task of tasks) {
        const shotId = task.shot_id
        if (!tasksByShot[shotId]) {
          tasksByShot[shotId] = []
        }
        tasksByShot[shotId].push(task)
      }

      // 每个镜头只保留最新的任务（已经按created_at降序排列）
      for (const shotId in tasksByShot) {
        tasksByShot[shotId] = [tasksByShot[shotId][0]]
      }
    }

    // 统计缓存中的视频信息
    let totalCompletedVideos = 0
    let totalCompletedTasks = 0

    for (const shotId in tasksByShot) {
      const shotTasks = tasksByShot[shotId]
      if (shotTasks && shotTasks.length > 0) {
        const latestTask = shotTasks[0]
        if (latestTask.status === 'completed' && latestTask.generated_videos) {
          totalCompletedTasks++
          totalCompletedVideos += latestTask.generated_videos.length
        }
      }
    }

    return {
      tasks: tasksByShot,
      totalShots: Object.keys(tasksByShot).length,
      totalCompletedTasks,
      totalCompletedVideos,
      hasCompleteVideos: totalCompletedVideos > 0,
      queriedAt: new Date().toISOString()
    }
  },
  ['video-records'], // 缓存键前缀
  {
    revalidate: 300, // 5分钟缓存
    tags: ['video-records'] // 缓存标签，用于手动清除缓存
  }
)

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { projectId, userId, shotIds } = body

    console.log('🔍 [CACHED-VIDEO-QUERY] 缓存查询参数:', { projectId, userId, shotIds })

    if (!projectId || !userId || !shotIds) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数: projectId, userId, shotIds'
      }, { status: 400 })
    }

    if (!Array.isArray(shotIds) || shotIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'shotIds必须是非空数组'
      }, { status: 400 })
    }

    console.log(`🔍 [CACHED-VIDEO-QUERY] 使用缓存查询 ${shotIds.length} 个镜头的任务:`, shotIds)

    // 创建缓存键，包含项目ID、用户ID和镜头ID列表
    const sortedShotIds = [...shotIds].sort()
    const cacheKey = `${projectId}-${userId}-${sortedShotIds.join(',')}`
    
    // 使用缓存函数查询数据
    const result = await getCachedVideoRecords(projectId, userId, sortedShotIds)

    console.log(`✅ [CACHED-VIDEO-QUERY] 缓存查询完成，找到 ${result.totalShots} 个镜头的任务`)

    return NextResponse.json({
      success: true,
      ...result,
      cached: true, // 标识这是缓存查询
      cacheKey,
      cacheExpiry: new Date(Date.now() + 300 * 1000).toISOString() // 5分钟后过期
    })

  } catch (error) {
    console.error('❌ [CACHED-VIDEO-QUERY] 缓存查询失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '缓存查询失败',
      cached: false
    }, { status: 500 })
  }
}

// GET方法支持查询参数格式（兼容性）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    // 处理清除缓存请求
    if (action === 'clear-cache') {
      console.log('🗑️ [CACHED-VIDEO-QUERY] 请求清除缓存')

      return NextResponse.json({
        success: true,
        message: '缓存清除请求已处理',
        timestamp: new Date().toISOString()
      })
    }

    // 处理查询请求（兼容GET格式）
    const projectId = searchParams.get('projectId')
    const userId = searchParams.get('userId')
    const shotIdsParam = searchParams.get('shotIds')

    console.log('🔍 [CACHED-VIDEO-QUERY] GET请求参数:', { projectId, userId, shotIdsParam })

    if (!projectId || !userId || !shotIdsParam) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数: projectId, userId, shotIds'
      }, { status: 400 })
    }

    const shotIds = shotIdsParam.split(',').filter(Boolean)

    if (shotIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'shotIds不能为空'
      }, { status: 400 })
    }

    console.log(`🔍 [CACHED-VIDEO-QUERY] 使用缓存查询 ${shotIds.length} 个镜头的任务:`, shotIds)

    // 创建缓存键，包含项目ID、用户ID和镜头ID列表
    const sortedShotIds = [...shotIds].sort()
    const cacheKey = `${projectId}-${userId}-${sortedShotIds.join(',')}`

    // 使用缓存函数查询数据
    const result = await getCachedVideoRecords(projectId, userId, sortedShotIds)

    console.log(`✅ [CACHED-VIDEO-QUERY] 缓存查询完成，找到 ${result.totalShots} 个镜头的任务`)

    return NextResponse.json({
      success: true,
      ...result,
      cached: true, // 标识这是缓存查询
      cacheKey,
      cacheExpiry: new Date(Date.now() + 300 * 1000).toISOString() // 5分钟后过期
    })

  } catch (error) {
    console.error('❌ [CACHED-VIDEO-QUERY] GET缓存查询失败:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '缓存查询失败',
      cached: false
    }, { status: 500 })
  }
}
