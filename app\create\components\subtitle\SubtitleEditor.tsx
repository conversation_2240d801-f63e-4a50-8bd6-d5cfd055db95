"use client"

import React, { useState, use<PERSON>allback, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import {
  Type,
  Upload,
  Download,
  Plus,
  Trash2,
  Copy,
  Scissors,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Underline,
  Palette,
  Move
} from "lucide-react"
import { SubtitleClip, useVideoEditor } from '../../stores'
import { SubtitleParser, SubtitleEntry } from './SubtitleParser'

interface SubtitleEditorProps {
  selectedClip?: SubtitleClip
  onClipUpdate?: (clipId: string, updates: Partial<SubtitleClip>) => void
  onClipDelete?: (clipId: string) => void
  onClipDuplicate?: (clipId: string) => void
  onAddClip?: (clip: Omit<SubtitleClip, 'id'>) => void
  className?: string
}

export const SubtitleEditor: React.FC<SubtitleEditorProps> = ({
  selectedClip,
  onClipUpdate,
  onClipDelete,
  onClipDuplicate,
  onAddClip,
  className = ''
}) => {
  const { subtitleClips, timeline } = useVideoEditor()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [showStyleEditor, setShowStyleEditor] = useState(false)

  // 文本编辑
  const handleTextChange = useCallback((text: string) => {
    if (selectedClip && onClipUpdate) {
      onClipUpdate(selectedClip.id, { text })
    }
  }, [selectedClip, onClipUpdate])

  // 时间调整
  const handleTimeChange = useCallback((field: 'startTime' | 'endTime', value: number) => {
    if (selectedClip && onClipUpdate) {
      const updates: Partial<SubtitleClip> = { [field]: value }
      
      // 确保时间逻辑正确
      if (field === 'startTime' && value >= selectedClip.endTime) {
        updates.endTime = value + 2 // 默认2秒持续时间
      } else if (field === 'endTime' && value <= selectedClip.startTime) {
        updates.startTime = value - 2
      }
      
      onClipUpdate(selectedClip.id, updates)
    }
  }, [selectedClip, onClipUpdate])

  // 样式更新
  const handleStyleChange = useCallback((styleUpdates: Partial<SubtitleClip['style']>) => {
    if (selectedClip && onClipUpdate) {
      onClipUpdate(selectedClip.id, {
        style: { ...selectedClip.style, ...styleUpdates }
      })
    }
  }, [selectedClip, onClipUpdate])

  // 位置更新
  const handlePositionChange = useCallback((positionUpdates: Partial<SubtitleClip['position']>) => {
    if (selectedClip && onClipUpdate) {
      onClipUpdate(selectedClip.id, {
        position: { ...selectedClip.position, ...positionUpdates }
      })
    }
  }, [selectedClip, onClipUpdate])

  // 轨道调整
  const handleTrackChange = useCallback((track: number) => {
    if (selectedClip && onClipUpdate) {
      onClipUpdate(selectedClip.id, { track })
    }
  }, [selectedClip, onClipUpdate])

  // 文件上传处理
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      if (!content) return

      try {
        const entries = SubtitleParser.parse(content)
        
        // 将解析的字幕条目转换为字幕片段
        entries.forEach((entry, index) => {
          const clip: Omit<SubtitleClip, 'id'> = {
            text: entry.text,
            startTime: entry.startTime,
            endTime: entry.endTime,
            track: 0,
            style: {
              fontFamily: entry.style?.fontFamily || 'Arial',
              fontSize: entry.style?.fontSize || 24,
              color: entry.style?.color || '#ffffff',
              backgroundColor: entry.style?.backgroundColor || 'transparent',
              bold: entry.style?.bold || false,
              italic: entry.style?.italic || false,
              underline: entry.style?.underline || false,
              shadow: entry.style?.shadow || true
            },
            position: {
              x: entry.position?.x || 50,
              y: entry.position?.y || 85
            }
          }
          
          onAddClip?.(clip)
        })

        console.log(`导入了 ${entries.length} 个字幕条目`)
      } catch (error) {
        console.error('字幕文件解析失败:', error)
        alert('字幕文件格式不支持或文件损坏')
      }
    }

    reader.readAsText(file)
    
    // 重置文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [onAddClip])

  // 导出字幕
  const handleExport = useCallback((format: 'srt' | 'vtt') => {
    const entries: SubtitleEntry[] = subtitleClips.map(clip => ({
      id: clip.id,
      startTime: clip.startTime,
      endTime: clip.endTime,
      text: clip.text,
      style: clip.style,
      position: clip.position
    }))

    let content: string
    let filename: string

    if (format === 'srt') {
      content = SubtitleParser.exportToSRT(entries)
      filename = 'subtitles.srt'
    } else {
      content = SubtitleParser.exportToVTT(entries)
      filename = 'subtitles.vtt'
    }

    // 创建下载链接
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }, [subtitleClips])

  // 添加新字幕
  const handleAddSubtitle = useCallback(() => {
    const newClip: Omit<SubtitleClip, 'id'> = {
      text: '新字幕',
      startTime: timeline.currentTime,
      endTime: timeline.currentTime + 3,
      track: 0,
      style: {
        fontFamily: 'Arial',
        fontSize: 24,
        color: '#ffffff',
        backgroundColor: 'transparent',
        bold: false,
        italic: false,
        underline: false,
        shadow: true
      },
      position: {
        x: 50,
        y: 85
      }
    }
    
    onAddClip?.(newClip)
  }, [timeline.currentTime, onAddClip])

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
  }

  return (
    <div className={`subtitle-editor ${className}`}>
      {selectedClip ? (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center space-x-2">
                <Type className="w-5 h-5 text-purple-600" />
                <span>字幕编辑器</span>
              </CardTitle>
              <Badge variant="secondary" className="text-xs">
                轨道 {selectedClip.track}
              </Badge>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* 文本编辑 */}
            <div>
              <Label className="text-sm font-medium mb-2 block">字幕文本</Label>
              <Textarea
                value={selectedClip.text}
                onChange={(e) => handleTextChange(e.target.value)}
                placeholder="输入字幕文本..."
                className="min-h-[80px] resize-none"
                rows={3}
              />
            </div>

            <Separator />

            {/* 时间控制 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm">开始时间</Label>
                <div className="mt-1">
                  <Input
                    type="number"
                    value={selectedClip.startTime.toFixed(3)}
                    onChange={(e) => handleTimeChange('startTime', parseFloat(e.target.value) || 0)}
                    step="0.001"
                    min="0"
                    className="text-sm"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formatTime(selectedClip.startTime)}
                  </p>
                </div>
              </div>
              
              <div>
                <Label className="text-sm">结束时间</Label>
                <div className="mt-1">
                  <Input
                    type="number"
                    value={selectedClip.endTime.toFixed(3)}
                    onChange={(e) => handleTimeChange('endTime', parseFloat(e.target.value) || 0)}
                    step="0.001"
                    min={selectedClip.startTime + 0.001}
                    className="text-sm"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formatTime(selectedClip.endTime)}
                  </p>
                </div>
              </div>
            </div>

            {/* 位置控制 */}
            <div>
              <Label className="text-sm font-medium mb-2 block">位置设置</Label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs">水平位置 (%)</Label>
                  <Input
                    type="number"
                    value={selectedClip.position.x}
                    onChange={(e) => handlePositionChange({ x: parseFloat(e.target.value) || 0 })}
                    min="0"
                    max="100"
                    className="text-sm"
                  />
                </div>
                <div>
                  <Label className="text-xs">垂直位置 (%)</Label>
                  <Input
                    type="number"
                    value={selectedClip.position.y}
                    onChange={(e) => handlePositionChange({ y: parseFloat(e.target.value) || 0 })}
                    min="0"
                    max="100"
                    className="text-sm"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* 样式编辑 */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <Label className="text-sm font-medium">样式设置</Label>
                <Switch
                  checked={showStyleEditor}
                  onCheckedChange={setShowStyleEditor}
                />
              </div>

              {showStyleEditor && (
                <div className="space-y-4">
                  {/* 字体设置 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-xs">字体</Label>
                      <Select
                        value={selectedClip.style.fontFamily}
                        onValueChange={(value) => handleStyleChange({ fontFamily: value })}
                      >
                        <SelectTrigger className="text-sm">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Arial">Arial</SelectItem>
                          <SelectItem value="Helvetica">Helvetica</SelectItem>
                          <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                          <SelectItem value="Courier New">Courier New</SelectItem>
                          <SelectItem value="SimHei">黑体</SelectItem>
                          <SelectItem value="SimSun">宋体</SelectItem>
                          <SelectItem value="Microsoft YaHei">微软雅黑</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label className="text-xs">字号</Label>
                      <Input
                        type="number"
                        value={selectedClip.style.fontSize}
                        onChange={(e) => handleStyleChange({ fontSize: parseInt(e.target.value) || 24 })}
                        min="8"
                        max="72"
                        className="text-sm"
                      />
                    </div>
                  </div>

                  {/* 颜色设置 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-xs">文字颜色</Label>
                      <div className="flex items-center space-x-2 mt-1">
                        <input
                          type="color"
                          value={selectedClip.style.color}
                          onChange={(e) => handleStyleChange({ color: e.target.value })}
                          className="w-8 h-8 rounded border border-gray-200"
                        />
                        <Input
                          value={selectedClip.style.color}
                          onChange={(e) => handleStyleChange({ color: e.target.value })}
                          className="text-sm flex-1"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-xs">背景颜色</Label>
                      <div className="flex items-center space-x-2 mt-1">
                        <input
                          type="color"
                          value={selectedClip.style.backgroundColor === 'transparent' ? '#000000' : selectedClip.style.backgroundColor}
                          onChange={(e) => handleStyleChange({ backgroundColor: e.target.value })}
                          className="w-8 h-8 rounded border border-gray-200"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleStyleChange({ backgroundColor: 'transparent' })}
                          className="text-xs"
                        >
                          透明
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* 文字样式 */}
                  <div>
                    <Label className="text-xs mb-2 block">文字样式</Label>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant={selectedClip.style.bold ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleStyleChange({ bold: !selectedClip.style.bold })}
                        className="w-8 h-8 p-0"
                      >
                        <Bold className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant={selectedClip.style.italic ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleStyleChange({ italic: !selectedClip.style.italic })}
                        className="w-8 h-8 p-0"
                      >
                        <Italic className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant={selectedClip.style.underline ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleStyleChange({ underline: !selectedClip.style.underline })}
                        className="w-8 h-8 p-0"
                      >
                        <Underline className="w-4 h-4" />
                      </Button>

                      <div className="flex items-center space-x-1 ml-4">
                        <Switch
                          checked={selectedClip.style.shadow}
                          onCheckedChange={(shadow) => handleStyleChange({ shadow })}
                        />
                        <Label className="text-xs">阴影</Label>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* 轨道选择 */}
            <div>
              <Label className="text-sm">字幕轨道</Label>
              <div className="mt-1">
                <Select
                  value={selectedClip.track.toString()}
                  onValueChange={(value) => handleTrackChange(parseInt(value))}
                >
                  <SelectTrigger className="text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 5 }, (_, i) => (
                      <SelectItem key={i} value={i.toString()}>
                        字幕轨道 {i + 1}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            {/* 操作按钮 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onClipDuplicate?.(selectedClip.id)}
                >
                  <Copy className="w-4 h-4 mr-1" />
                  复制
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                >
                  <Scissors className="w-4 h-4 mr-1" />
                  分割
                </Button>
              </div>

              <Button
                variant="destructive"
                size="sm"
                onClick={() => onClipDelete?.(selectedClip.id)}
              >
                <Trash2 className="w-4 h-4 mr-1" />
                删除
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* 无选中字幕时的状态 */
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-6xl mb-4">📝</div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">字幕编辑器</h3>
            <p className="text-sm text-gray-600 mb-6">
              选择一个字幕片段开始编辑，或创建新的字幕
            </p>
            
            <div className="space-y-3">
              <Button onClick={handleAddSubtitle} className="w-full">
                <Plus className="w-4 h-4 mr-2" />
                添加字幕
              </Button>
              
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="flex-1"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  导入字幕
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => handleExport('srt')}
                  disabled={subtitleClips.length === 0}
                  className="flex-1"
                >
                  <Download className="w-4 h-4 mr-2" />
                  导出SRT
                </Button>
              </div>
              
              <p className="text-xs text-gray-500">
                支持 SRT, VTT, ASS 格式
              </p>
            </div>

            {/* 隐藏的文件输入 */}
            <input
              ref={fileInputRef}
              type="file"
              accept=".srt,.vtt,.ass,.ssa"
              onChange={handleFileUpload}
              className="hidden"
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
