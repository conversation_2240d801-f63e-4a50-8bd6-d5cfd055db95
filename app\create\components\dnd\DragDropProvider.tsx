"use client"

import React from 'react'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'

interface DragDropProviderProps {
  children: React.ReactNode
}

export const DragDropProvider: React.FC<DragDropProviderProps> = ({ children }) => {
  return (
    <DndProvider backend={HTML5Backend}>
      {children}
    </DndProvider>
  )
}

// 拖拽项目类型定义
export const DragTypes = {
  VIDEO_CLIP: 'video_clip',
  AUDIO_CLIP: 'audio_clip',
  SUBTITLE_CLIP: 'subtitle_clip',
  MEDIA_ITEM: 'media_item'
} as const

export type DragType = typeof DragTypes[keyof typeof DragTypes]

// 拖拽数据接口
export interface DragItem {
  type: DragType
  id: string
  clipType: 'video' | 'audio' | 'subtitle'
  originalTrack?: number
  originalStartTime?: number
  duration: number
  name: string
  thumbnail?: string
  data?: any
}

// 拖拽结果接口
export interface DropResult {
  track: number
  time: number
  dropZone: 'timeline' | 'trash' | 'library'
}

// 拖拽预览组件
export const DragPreview: React.FC<{ item: DragItem; isDragging: boolean }> = ({ 
  item, 
  isDragging 
}) => {
  if (!isDragging) return null

  return (
    <div className="fixed pointer-events-none z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-2 opacity-90">
      <div className="flex items-center space-x-2">
        {item.thumbnail ? (
          <img 
            src={item.thumbnail} 
            alt={item.name}
            className="w-8 h-6 object-cover rounded"
          />
        ) : (
          <div className={`w-8 h-6 rounded flex items-center justify-center text-xs ${
            item.clipType === 'video' ? 'bg-emerald-100 text-emerald-600' :
            item.clipType === 'audio' ? 'bg-blue-100 text-blue-600' :
            'bg-purple-100 text-purple-600'
          }`}>
            {item.clipType === 'video' ? '🎥' :
             item.clipType === 'audio' ? '🎵' : '📝'}
          </div>
        )}
        <div>
          <div className="text-sm font-medium text-gray-800">{item.name}</div>
          <div className="text-xs text-gray-500">{item.duration}s</div>
        </div>
      </div>
    </div>
  )
}
