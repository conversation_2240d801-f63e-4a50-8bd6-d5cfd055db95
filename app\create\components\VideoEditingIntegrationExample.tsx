"use client"

import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  ArrowLeft, 
  ArrowRight, 
  Video, 
  FileText, 
  Image, 
  Sparkles,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react"

// 导入集成的视频编辑器
import { IntegratedVideoEditor } from './IntegratedVideoEditor'

interface VideoEditingIntegrationExampleProps {
  // 从现有流程传入的数据
  projectId?: string
  userId?: string
  scriptData?: any
  shotsWithImages?: any[]
  
  // 流程控制
  currentStep?: number
  onStepChange?: (step: number) => void
  onSave?: (data: any) => void
  onComplete?: () => void
  
  className?: string
}

export const VideoEditingIntegrationExample: React.FC<VideoEditingIntegrationExampleProps> = ({
  projectId,
  userId,
  scriptData,
  shotsWithImages,
  currentStep = 0,
  onStepChange,
  onSave,
  onComplete,
  className = ''
}) => {
  const [videoProjectData, setVideoProjectData] = useState<any>(null)
  const [isVideoEditorActive, setIsVideoEditorActive] = useState(false)

  // 步骤定义
  const steps = [
    {
      id: 'script',
      title: '剧本创作',
      description: '编写和完善剧本内容',
      icon: FileText,
      status: scriptData ? 'completed' : 'pending'
    },
    {
      id: 'shots',
      title: '分镜生成',
      description: '生成场景和镜头',
      icon: Image,
      status: shotsWithImages?.length ? 'completed' : 'pending'
    },
    {
      id: 'video-editing',
      title: '视频剪辑',
      description: '智能视频剪辑和编辑',
      icon: Video,
      status: videoProjectData ? 'completed' : currentStep === 2 ? 'active' : 'pending'
    },
    {
      id: 'finalize',
      title: '完成制作',
      description: '最终审核和导出',
      icon: CheckCircle,
      status: 'pending'
    }
  ]

  // 处理视频编辑器保存
  const handleVideoEditorSave = useCallback((projectData: any) => {
    console.log('🎬 [INTEGRATION] 视频编辑器保存数据:', projectData)
    setVideoProjectData(projectData)
    
    if (onSave) {
      onSave({
        type: 'video-project',
        data: projectData,
        timestamp: Date.now()
      })
    }
  }, [onSave])

  // 处理视频导出
  const handleVideoExport = useCallback((videoData: Uint8Array, filename: string) => {
    console.log('🎬 [INTEGRATION] 导出视频:', filename)
    
    // 创建下载链接
    const blob = new Blob([videoData], { type: 'video/mp4' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.click()
    
    // 清理资源
    URL.revokeObjectURL(url)
    
    // 标记完成
    if (onComplete) {
      onComplete()
    }
  }, [onComplete])

  // 进入视频编辑模式
  const enterVideoEditor = useCallback(() => {
    setIsVideoEditorActive(true)
    if (onStepChange) {
      onStepChange(2)
    }
  }, [onStepChange])

  // 退出视频编辑模式
  const exitVideoEditor = useCallback(() => {
    setIsVideoEditorActive(false)
  }, [])

  // 如果处于视频编辑模式，显示完整的视频编辑器
  if (isVideoEditorActive) {
    return (
      <div className={`h-screen ${className}`}>
        <IntegratedVideoEditor
          projectId={projectId}
          userId={userId}
          scriptData={scriptData}
          shotsWithImages={shotsWithImages}
          onSave={handleVideoEditorSave}
          onExport={handleVideoExport}
          className="h-full"
        />
        
        {/* 退出按钮 */}
        <div className="fixed top-4 left-4 z-50">
          <Button
            variant="outline"
            onClick={exitVideoEditor}
            className="bg-white/90 backdrop-blur-sm"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回流程
          </Button>
        </div>
      </div>
    )
  }

  // 显示流程概览界面
  return (
    <div className={`p-6 max-w-6xl mx-auto ${className}`}>
      {/* 流程标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">AI 剧本到视频制作流程</h1>
        <p className="text-gray-600">从剧本创作到视频制作的完整工作流程</p>
      </div>

      {/* 流程步骤 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const Icon = step.icon
            const isActive = currentStep === index
            const isCompleted = step.status === 'completed'
            const isPending = step.status === 'pending'
            
            return (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all ${
                      isCompleted
                        ? 'bg-green-500 border-green-500 text-white'
                        : isActive
                        ? 'bg-blue-500 border-blue-500 text-white'
                        : isPending
                        ? 'bg-gray-100 border-gray-300 text-gray-400'
                        : 'bg-yellow-100 border-yellow-300 text-yellow-600'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                  </div>
                  <div className="mt-2 text-center">
                    <p className={`text-sm font-medium ${
                      isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-400">{step.description}</p>
                  </div>
                </div>
                
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-4 ${
                    index < currentStep ? 'bg-green-500' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* 当前步骤内容 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：项目信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5" />
              <span>项目信息</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-gray-700">项目ID</Label>
              <p className="text-sm text-gray-600">{projectId || '未设置'}</p>
            </div>
            
            <div>
              <Label className="text-sm font-medium text-gray-700">用户ID</Label>
              <p className="text-sm text-gray-600">{userId || '未设置'}</p>
            </div>
            
            <Separator />
            
            <div>
              <Label className="text-sm font-medium text-gray-700">剧本状态</Label>
              <div className="flex items-center space-x-2 mt-1">
                {scriptData ? (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-green-600">已完成</span>
                  </>
                ) : (
                  <>
                    <Clock className="w-4 h-4 text-yellow-500" />
                    <span className="text-sm text-yellow-600">待完成</span>
                  </>
                )}
              </div>
            </div>
            
            <div>
              <Label className="text-sm font-medium text-gray-700">场景镜头</Label>
              <div className="flex items-center space-x-2 mt-1">
                {shotsWithImages?.length ? (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-green-600">{shotsWithImages.length} 个场景</span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="w-4 h-4 text-red-500" />
                    <span className="text-sm text-red-600">无场景数据</span>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 右侧：视频编辑入口 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Video className="w-5 h-5" />
              <span>视频剪辑</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">AI 智能剪辑</h3>
              <p className="text-sm text-gray-600 mb-6">
                基于场景数据自动生成视频片段，支持智能缓存和实时预览
              </p>
              
              {/* 功能特性 */}
              <div className="space-y-2 mb-6">
                <Badge variant="secondary" className="mr-2">智能缓存切换</Badge>
                <Badge variant="secondary" className="mr-2">多轨道编辑</Badge>
                <Badge variant="secondary" className="mr-2">实时预览</Badge>
                <Badge variant="secondary">一键导出</Badge>
              </div>
              
              <Button
                onClick={enterVideoEditor}
                disabled={!shotsWithImages?.length}
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
              >
                <Video className="w-4 h-4 mr-2" />
                开始视频剪辑
              </Button>
              
              {!shotsWithImages?.length && (
                <p className="text-xs text-red-500 mt-2">
                  请先完成剧本创作和场景生成
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 底部操作栏 */}
      <div className="mt-8 flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => onStepChange?.(Math.max(0, currentStep - 1))}
          disabled={currentStep === 0}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          上一步
        </Button>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            步骤 {currentStep + 1} / {steps.length}
          </span>
        </div>

        <Button
          onClick={() => onStepChange?.(Math.min(steps.length - 1, currentStep + 1))}
          disabled={currentStep === steps.length - 1}
        >
          下一步
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </div>
  )
}

// 使用示例组件
export const VideoEditingIntegrationUsageExample = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [projectData, setProjectData] = useState<any>(null)

  // 模拟数据
  const mockShotsWithImages = [
    {
      shotId: "shot-1",
      shotNumber: 1,
      videoUrl: "/mock-video-1.mp4",
      images: [
        { url: "/mock-image-1.jpg", isPrimary: true }
      ]
    },
    {
      shotId: "shot-2", 
      shotNumber: 2,
      videoUrl: "/mock-video-2.mp4",
      images: [
        { url: "/mock-image-2.jpg", isPrimary: true }
      ]
    }
  ]

  return (
    <VideoEditingIntegrationExample
      projectId="demo-project-123"
      userId="user-456"
      scriptData={{ title: "示例剧本", content: "剧本内容..." }}
      shotsWithImages={mockShotsWithImages}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onSave={(data) => {
        console.log('保存数据:', data)
        setProjectData(data)
      }}
      onComplete={() => {
        console.log('制作完成!')
        alert('视频制作完成!')
      }}
    />
  )
}
