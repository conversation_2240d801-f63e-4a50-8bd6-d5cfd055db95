// 统一导出所有 stores 和相关工具

// 类型定义
export * from './types'

// 主要的状态管理 stores
export { useEditorStore } from './editorStore'
export { useHistoryStore, historyUtils, useAutoSaveHistory, useHistoryKeyboard } from './historyStore'

// 工具函数
export { TimelineUtils, createTimelineUtils, timelineHelpers } from './timelineUtils'

// React hooks for easier store usage
import { useEditorStore } from './editorStore'
import { useHistoryStore, historyUtils } from './historyStore'
import { createTimelineUtils } from './timelineUtils'
import React from 'react'

// 组合 hook：同时使用编辑器和历史记录
export const useVideoEditor = () => {
  const editorStore = useEditorStore()
  const historyStore = useHistoryStore()
  
  // 创建时间轴工具实例
  const timelineUtils = React.useMemo(() => {
    return createTimelineUtils(
      editorStore.viewport,
      editorStore.ui.snapThreshold,
      editorStore.ui.snapToGrid
    )
  }, [editorStore.viewport, editorStore.ui.snapThreshold, editorStore.ui.snapToGrid])

  // 包装编辑器操作以自动保存历史记录
  const wrappedActions = React.useMemo(() => ({
    // 视频片段操作（带历史记录）
    addVideoClip: (clip: Parameters<typeof editorStore.addVideoClip>[0]) => {
      const currentState = {
        videoClips: editorStore.videoClips,
        audioClips: editorStore.audioClips,
        subtitleClips: editorStore.subtitleClips,
        timeline: editorStore.timeline,
        viewport: editorStore.viewport,
        project: editorStore.project
      }
      historyStore.saveState(historyUtils.createSnapshot(currentState))
      editorStore.addVideoClip(clip)
    },

    removeVideoClip: (id: string) => {
      const currentState = {
        videoClips: editorStore.videoClips,
        audioClips: editorStore.audioClips,
        subtitleClips: editorStore.subtitleClips,
        timeline: editorStore.timeline,
        viewport: editorStore.viewport,
        project: editorStore.project
      }
      historyStore.saveState(historyUtils.createSnapshot(currentState))
      editorStore.removeVideoClip(id)
    },

    updateVideoClip: (id: string, updates: Parameters<typeof editorStore.updateVideoClip>[1]) => {
      const currentState = {
        videoClips: editorStore.videoClips,
        audioClips: editorStore.audioClips,
        subtitleClips: editorStore.subtitleClips,
        timeline: editorStore.timeline,
        viewport: editorStore.viewport,
        project: editorStore.project
      }
      historyStore.saveState(historyUtils.createSnapshot(currentState))
      editorStore.updateVideoClip(id, updates)
    },

    // 音频片段操作（带历史记录）
    addAudioClip: (clip: Parameters<typeof editorStore.addAudioClip>[0]) => {
      const currentState = {
        videoClips: editorStore.videoClips,
        audioClips: editorStore.audioClips,
        subtitleClips: editorStore.subtitleClips,
        timeline: editorStore.timeline,
        viewport: editorStore.viewport,
        project: editorStore.project
      }
      historyStore.saveState(historyUtils.createSnapshot(currentState))
      editorStore.addAudioClip(clip)
    },

    removeAudioClip: (id: string) => {
      const currentState = {
        videoClips: editorStore.videoClips,
        audioClips: editorStore.audioClips,
        subtitleClips: editorStore.subtitleClips,
        timeline: editorStore.timeline,
        viewport: editorStore.viewport,
        project: editorStore.project
      }
      historyStore.saveState(historyUtils.createSnapshot(currentState))
      editorStore.removeAudioClip(id)
    },

    // 字幕片段操作（带历史记录）
    addSubtitleClip: (clip: Parameters<typeof editorStore.addSubtitleClip>[0]) => {
      const currentState = {
        videoClips: editorStore.videoClips,
        audioClips: editorStore.audioClips,
        subtitleClips: editorStore.subtitleClips,
        timeline: editorStore.timeline,
        viewport: editorStore.viewport,
        project: editorStore.project
      }
      historyStore.saveState(historyUtils.createSnapshot(currentState))
      editorStore.addSubtitleClip(clip)
    },

    // 批量操作
    startBatch: historyStore.startBatch,
    endBatch: historyStore.endBatch,

    // 撤销重做
    undo: () => {
      const previousState = historyStore.undo()
      if (previousState) {
        // 恢复编辑器状态
        editorStore.videoClips.splice(0, editorStore.videoClips.length, ...previousState.videoClips)
        editorStore.audioClips.splice(0, editorStore.audioClips.length, ...previousState.audioClips)
        editorStore.subtitleClips.splice(0, editorStore.subtitleClips.length, ...previousState.subtitleClips)
        Object.assign(editorStore.timeline, previousState.timeline)
        Object.assign(editorStore.viewport, previousState.viewport)
        Object.assign(editorStore.project, previousState.project)
      }
    },

    redo: () => {
      const nextState = historyStore.redo()
      if (nextState) {
        // 恢复编辑器状态
        editorStore.videoClips.splice(0, editorStore.videoClips.length, ...nextState.videoClips)
        editorStore.audioClips.splice(0, editorStore.audioClips.length, ...nextState.audioClips)
        editorStore.subtitleClips.splice(0, editorStore.subtitleClips.length, ...nextState.subtitleClips)
        Object.assign(editorStore.timeline, nextState.timeline)
        Object.assign(editorStore.viewport, nextState.viewport)
        Object.assign(editorStore.project, nextState.project)
      }
    }
  }), [editorStore, historyStore])

  return {
    // 编辑器状态
    ...editorStore,
    
    // 历史记录状态
    canUndo: historyStore.canUndo,
    canRedo: historyStore.canRedo,
    
    // 工具函数
    timelineUtils,
    
    // 包装的操作方法
    actions: wrappedActions
  }
}

// 选择器 hooks（用于性能优化）
export const useVideoClips = () => useEditorStore(state => state.videoClips)
export const useAudioClips = () => useEditorStore(state => state.audioClips)
export const useSubtitleClips = () => useEditorStore(state => state.subtitleClips)
export const useTimeline = () => useEditorStore(state => state.timeline)
export const useViewport = () => useEditorStore(state => state.viewport)
export const useProject = () => useEditorStore(state => state.project)
export const useDragState = () => useEditorStore(state => state.drag)
export const useUIState = () => useEditorStore(state => state.ui)
export const useCacheState = () => useEditorStore(state => state.cache)

// 计算属性 hooks
export const useSelectedClips = () => {
  return useEditorStore(state => {
    const selectedIds = state.timeline.selectedClips
    return [
      ...state.videoClips.filter(c => selectedIds.includes(c.id)),
      ...state.audioClips.filter(c => selectedIds.includes(c.id)),
      ...state.subtitleClips.filter(c => selectedIds.includes(c.id))
    ]
  })
}

export const useTotalDuration = () => {
  return useEditorStore(state => {
    const videoMaxTime = Math.max(0, ...state.videoClips.map(c => c.endTime))
    const audioMaxTime = Math.max(0, ...state.audioClips.map(c => c.endTime))
    const subtitleMaxTime = Math.max(0, ...state.subtitleClips.map(c => c.endTime))
    return Math.max(videoMaxTime, audioMaxTime, subtitleMaxTime, 10)
  })
}

export const useVisibleClips = () => {
  return useEditorStore(state => {
    const { start, end } = state.viewport.visibleTimeRange
    const allClips = [
      ...state.videoClips,
      ...state.audioClips,
      ...state.subtitleClips
    ]
    return allClips.filter(clip => 
      !(clip.endTime < start || clip.startTime > end)
    )
  })
}

// 调试工具
export const useStoreDebug = () => {
  const editorStore = useEditorStore()
  const historyStore = useHistoryStore()
  
  return {
    logState: () => {
      console.group('🎬 Video Editor State')
      console.log('📹 Video Clips:', editorStore.videoClips)
      console.log('🎵 Audio Clips:', editorStore.audioClips)
      console.log('📝 Subtitle Clips:', editorStore.subtitleClips)
      console.log('⏱️ Timeline:', editorStore.timeline)
      console.log('🖼️ Viewport:', editorStore.viewport)
      console.log('📋 Project:', editorStore.project)
      console.log('🎯 UI:', editorStore.ui)
      console.log('💾 Cache:', editorStore.cache)
      console.log('📚 History:', {
        canUndo: historyStore.canUndo,
        canRedo: historyStore.canRedo,
        pastLength: historyStore.past.length,
        futureLength: historyStore.future.length
      })
      console.groupEnd()
    },
    
    exportState: () => {
      return {
        editor: {
          videoClips: editorStore.videoClips,
          audioClips: editorStore.audioClips,
          subtitleClips: editorStore.subtitleClips,
          timeline: editorStore.timeline,
          viewport: editorStore.viewport,
          project: editorStore.project,
          ui: editorStore.ui,
          cache: editorStore.cache
        },
        history: {
          canUndo: historyStore.canUndo,
          canRedo: historyStore.canRedo,
          pastLength: historyStore.past.length,
          futureLength: historyStore.future.length
        }
      }
    }
  }
}
