"use client"

import React, { useState, useCallback, useRef } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Video,
  Download,
  Upload,
  Settings,
  Play,
  Scissors,
  Merge,
  FileVideo,
  Loader2,
  CheckCircle,
  AlertCircle,
  Info
} from "lucide-react"
import { ffmpegProcessor, VideoProcessingOptions } from './FFmpegProcessor'

interface VideoProcessorProps {
  onProcessComplete?: (result: Uint8Array, filename: string) => void
  onError?: (error: string) => void
  className?: string
}

export const VideoProcessor: React.FC<VideoProcessorProps> = ({
  onProcessComplete,
  onError,
  className = ''
}) => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentTask, setCurrentTask] = useState('')
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [processingOptions, setProcessingOptions] = useState<VideoProcessingOptions>({
    width: 1920,
    height: 1080,
    fps: 30,
    quality: 'medium',
    format: 'mp4'
  })
  const [operation, setOperation] = useState<'convert' | 'trim' | 'merge' | 'thumbnail'>('convert')
  const [trimOptions, setTrimOptions] = useState({ startTime: 0, duration: 10 })
  const [isFFmpegReady, setIsFFmpegReady] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 初始化 FFmpeg
  const initializeFFmpeg = useCallback(async () => {
    if (ffmpegProcessor.isReady()) {
      setIsFFmpegReady(true)
      return
    }

    try {
      setCurrentTask('正在初始化 FFmpeg...')
      setIsProcessing(true)
      await ffmpegProcessor.initialize()
      setIsFFmpegReady(true)
      setCurrentTask('FFmpeg 初始化完成')
    } catch (error) {
      console.error('FFmpeg 初始化失败:', error)
      onError?.('FFmpeg 初始化失败，请刷新页面重试')
    } finally {
      setIsProcessing(false)
    }
  }, [onError])

  // 文件选择处理
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setSelectedFiles(files)
  }, [])

  // 视频转换
  const handleVideoConvert = useCallback(async () => {
    if (selectedFiles.length === 0) return

    try {
      setIsProcessing(true)
      setProgress(0)
      setCurrentTask('正在转换视频...')

      const file = selectedFiles[0]
      const result = await ffmpegProcessor.convertVideo(file, processingOptions.format!, processingOptions)
      
      const filename = `converted_${file.name.split('.')[0]}.${processingOptions.format}`
      onProcessComplete?.(result, filename)
      
      setCurrentTask('视频转换完成')
      setProgress(100)
    } catch (error) {
      console.error('视频转换失败:', error)
      onError?.('视频转换失败: ' + (error as Error).message)
    } finally {
      setIsProcessing(false)
    }
  }, [selectedFiles, processingOptions, onProcessComplete, onError])

  // 视频剪切
  const handleVideoTrim = useCallback(async () => {
    if (selectedFiles.length === 0) return

    try {
      setIsProcessing(true)
      setProgress(0)
      setCurrentTask('正在剪切视频...')

      const file = selectedFiles[0]
      const result = await ffmpegProcessor.trimVideo(
        file,
        trimOptions.startTime,
        trimOptions.duration,
        processingOptions.format
      )
      
      const filename = `trimmed_${file.name.split('.')[0]}.${processingOptions.format}`
      onProcessComplete?.(result, filename)
      
      setCurrentTask('视频剪切完成')
      setProgress(100)
    } catch (error) {
      console.error('视频剪切失败:', error)
      onError?.('视频剪切失败: ' + (error as Error).message)
    } finally {
      setIsProcessing(false)
    }
  }, [selectedFiles, trimOptions, processingOptions, onProcessComplete, onError])

  // 视频合并
  const handleVideoMerge = useCallback(async () => {
    if (selectedFiles.length < 2) return

    try {
      setIsProcessing(true)
      setProgress(0)
      setCurrentTask('正在合并视频...')

      const result = await ffmpegProcessor.mergeVideos(selectedFiles, processingOptions.format)
      
      const filename = `merged_video.${processingOptions.format}`
      onProcessComplete?.(result, filename)
      
      setCurrentTask('视频合并完成')
      setProgress(100)
    } catch (error) {
      console.error('视频合并失败:', error)
      onError?.('视频合并失败: ' + (error as Error).message)
    } finally {
      setIsProcessing(false)
    }
  }, [selectedFiles, processingOptions, onProcessComplete, onError])

  // 提取缩略图
  const handleExtractThumbnail = useCallback(async () => {
    if (selectedFiles.length === 0) return

    try {
      setIsProcessing(true)
      setProgress(0)
      setCurrentTask('正在提取缩略图...')

      const file = selectedFiles[0]
      const result = await ffmpegProcessor.extractThumbnail(
        file,
        trimOptions.startTime,
        processingOptions.width,
        processingOptions.height
      )
      
      const filename = `thumbnail_${file.name.split('.')[0]}.jpg`
      onProcessComplete?.(result, filename)
      
      setCurrentTask('缩略图提取完成')
      setProgress(100)
    } catch (error) {
      console.error('缩略图提取失败:', error)
      onError?.('缩略图提取失败: ' + (error as Error).message)
    } finally {
      setIsProcessing(false)
    }
  }, [selectedFiles, trimOptions, processingOptions, onProcessComplete, onError])

  // 执行处理
  const handleProcess = useCallback(async () => {
    if (!isFFmpegReady) {
      await initializeFFmpeg()
      return
    }

    switch (operation) {
      case 'convert':
        await handleVideoConvert()
        break
      case 'trim':
        await handleVideoTrim()
        break
      case 'merge':
        await handleVideoMerge()
        break
      case 'thumbnail':
        await handleExtractThumbnail()
        break
    }
  }, [operation, isFFmpegReady, initializeFFmpeg, handleVideoConvert, handleVideoTrim, handleVideoMerge, handleExtractThumbnail])

  // 下载结果文件
  const downloadFile = useCallback((data: Uint8Array, filename: string) => {
    const blob = new Blob([data])
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }, [])

  return (
    <div className={`video-processor ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Video className="w-5 h-5 text-blue-600" />
            <span>视频处理器</span>
            {isFFmpegReady && (
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <CheckCircle className="w-3 h-3 mr-1" />
                就绪
              </Badge>
            )}
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* FFmpeg 状态 */}
          {!isFFmpegReady && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                首次使用需要初始化 FFmpeg，这可能需要几秒钟时间。
                <Button
                  variant="link"
                  className="p-0 h-auto ml-2"
                  onClick={initializeFFmpeg}
                  disabled={isProcessing}
                >
                  立即初始化
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* 文件选择 */}
          <div>
            <Label className="text-sm font-medium mb-2 block">选择文件</Label>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={isProcessing}
              >
                <Upload className="w-4 h-4 mr-2" />
                选择文件
              </Button>
              <span className="text-sm text-gray-600">
                {selectedFiles.length > 0 
                  ? `已选择 ${selectedFiles.length} 个文件`
                  : '未选择文件'
                }
              </span>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*,audio/*"
              multiple={operation === 'merge'}
              onChange={handleFileSelect}
              className="hidden"
            />
            
            {/* 文件列表 */}
            {selectedFiles.length > 0 && (
              <div className="mt-3 space-y-2">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <FileVideo className="w-4 h-4 text-blue-500" />
                    <span className="flex-1 truncate">{file.name}</span>
                    <span className="text-gray-500">
                      {(file.size / 1024 / 1024).toFixed(1)} MB
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>

          <Separator />

          {/* 操作选择 */}
          <div>
            <Label className="text-sm font-medium mb-2 block">处理操作</Label>
            <Select value={operation} onValueChange={(value: any) => setOperation(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="convert">格式转换</SelectItem>
                <SelectItem value="trim">视频剪切</SelectItem>
                <SelectItem value="merge">视频合并</SelectItem>
                <SelectItem value="thumbnail">提取缩略图</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 处理选项 */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">处理选项</Label>
            
            {/* 基本选项 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-xs">输出格式</Label>
                <Select
                  value={processingOptions.format}
                  onValueChange={(value) => setProcessingOptions(prev => ({ ...prev, format: value }))}
                >
                  <SelectTrigger className="text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mp4">MP4</SelectItem>
                    <SelectItem value="avi">AVI</SelectItem>
                    <SelectItem value="mov">MOV</SelectItem>
                    <SelectItem value="webm">WebM</SelectItem>
                    <SelectItem value="mkv">MKV</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-xs">质量</Label>
                <Select
                  value={processingOptions.quality}
                  onValueChange={(value: any) => setProcessingOptions(prev => ({ ...prev, quality: value }))}
                >
                  <SelectTrigger className="text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">低质量</SelectItem>
                    <SelectItem value="medium">中等质量</SelectItem>
                    <SelectItem value="high">高质量</SelectItem>
                    <SelectItem value="ultra">超高质量</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 分辨率设置 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-xs">宽度</Label>
                <Input
                  type="number"
                  value={processingOptions.width}
                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, width: parseInt(e.target.value) }))}
                  className="text-sm"
                />
              </div>
              <div>
                <Label className="text-xs">高度</Label>
                <Input
                  type="number"
                  value={processingOptions.height}
                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, height: parseInt(e.target.value) }))}
                  className="text-sm"
                />
              </div>
            </div>

            {/* 剪切选项 */}
            {operation === 'trim' && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs">开始时间 (秒)</Label>
                  <Input
                    type="number"
                    value={trimOptions.startTime}
                    onChange={(e) => setTrimOptions(prev => ({ ...prev, startTime: parseFloat(e.target.value) }))}
                    className="text-sm"
                    step="0.1"
                  />
                </div>
                <div>
                  <Label className="text-xs">持续时间 (秒)</Label>
                  <Input
                    type="number"
                    value={trimOptions.duration}
                    onChange={(e) => setTrimOptions(prev => ({ ...prev, duration: parseFloat(e.target.value) }))}
                    className="text-sm"
                    step="0.1"
                  />
                </div>
              </div>
            )}

            {/* 合并提示 */}
            {operation === 'merge' && selectedFiles.length < 2 && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  视频合并需要选择至少2个视频文件
                </AlertDescription>
              </Alert>
            )}
          </div>

          <Separator />

          {/* 处理进度 */}
          {isProcessing && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">处理进度</span>
                <span className="text-sm text-gray-600">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" />
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>{currentTask}</span>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {selectedFiles.length > 0 && (
                <span>
                  总大小: {(selectedFiles.reduce((sum, file) => sum + file.size, 0) / 1024 / 1024).toFixed(1)} MB
                </span>
              )}
            </div>

            <Button
              onClick={handleProcess}
              disabled={
                isProcessing || 
                selectedFiles.length === 0 || 
                (operation === 'merge' && selectedFiles.length < 2)
              }
              className="bg-blue-500 hover:bg-blue-600"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  处理中...
                </>
              ) : !isFFmpegReady ? (
                <>
                  <Settings className="w-4 h-4 mr-2" />
                  初始化 FFmpeg
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  开始处理
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
