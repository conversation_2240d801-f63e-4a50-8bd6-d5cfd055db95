#!/usr/bin/env node

/**
 * Test script for VideoEditingStep smart cache implementation
 * Tests the combined video and image cache mechanism
 */

const BASE_URL = 'http://localhost:3001'

// Test data
const testData = {
  projectId: '4db5bf32-fdda-4bf0-ab45-f8e8315ef302',
  userId: '559f045d-5477-443c-92ee-93d03fff3b3c',
  shotIds: [
    '7268a0c0-d00f-470c-a7e6-beca3669474c',
    'eb005ede-1e86-44f9-8115-6638dcf2d2ec',
    '74b6048e-631b-4df5-9149-2f547c9837aa'
  ],
  shotNumbers: [1, 2, 3, 4, 5]
}

async function testVideoCacheAPI() {
  console.log('\n🎥 Testing Video Cache API...')
  
  try {
    const response = await fetch(`${BASE_URL}/api/video-records/cached`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectId: testData.projectId,
        userId: testData.userId,
        shotIds: testData.shotIds
      })
    })
    
    const data = await response.json()
    
    console.log(`📊 Video Cache Response Status: ${response.status}`)
    console.log(`📊 Video Cache Response:`, JSON.stringify(data, null, 2))
    
    if (response.ok) {
      console.log('✅ Video cache API working correctly')
      return data
    } else {
      console.log('❌ Video cache API failed')
      return null
    }
  } catch (error) {
    console.error('❌ Video cache API error:', error.message)
    return null
  }
}

async function testImageCacheAPI() {
  console.log('\n🖼️ Testing Image Cache API...')
  
  try {
    const response = await fetch(`${BASE_URL}/api/image-records/cached`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectId: testData.projectId,
        userId: testData.userId,
        shotNumbers: testData.shotNumbers
      })
    })
    
    const data = await response.json()
    
    console.log(`📊 Image Cache Response Status: ${response.status}`)
    console.log(`📊 Image Cache Response:`, JSON.stringify(data, null, 2))
    
    if (response.ok) {
      console.log('✅ Image cache API working correctly')
      return data
    } else {
      console.log('❌ Image cache API failed')
      return null
    }
  } catch (error) {
    console.error('❌ Image cache API error:', error.message)
    return null
  }
}

function simulateSmartCacheLogic(videoData, imageData) {
  console.log('\n🧠 Simulating Smart Cache Logic...')
  
  const mockShotsWithImages = [
    { shotId: testData.shotIds[0], shotNumber: 1, images: [{ url: 'mock-image-1.jpg', isPrimary: true }] },
    { shotId: testData.shotIds[1], shotNumber: 2, images: [{ url: 'mock-image-2.jpg', isPrimary: true }] },
    { shotId: testData.shotIds[2], shotNumber: 3, images: [{ url: 'mock-image-3.jpg', isPrimary: true }] }
  ]

  const smartClips = mockShotsWithImages.map((shot, index) => {
    // Check video cache
    let hasVideoCache = false
    let videoUrl = undefined
    let videoTaskStatus = undefined
    
    if (videoData?.tasks && shot.shotId) {
      const videoTasks = videoData.tasks[shot.shotId]
      if (videoTasks && videoTasks.length > 0) {
        const latestVideoTask = videoTasks[0]
        videoTaskStatus = latestVideoTask.status
        
        if (latestVideoTask.status === 'completed' && latestVideoTask.generated_videos) {
          hasVideoCache = true
          const firstVideo = latestVideoTask.generated_videos[0]
          if (firstVideo) {
            videoUrl = firstVideo.video_url
          }
        }
      }
    }

    // Check image cache
    let hasImageCache = false
    let thumbnailUrl = '/placeholder.svg'
    let imageTaskStatus = undefined
    
    if (imageData?.tasks && shot.shotNumber) {
      const imageTasks = imageData.tasks[shot.shotNumber]
      if (imageTasks && imageTasks.length > 0) {
        const latestImageTask = imageTasks[0]
        imageTaskStatus = latestImageTask.status
        
        if (latestImageTask.status === 'completed' && latestImageTask.generated_images) {
          hasImageCache = true
          const firstImage = latestImageTask.generated_images[0]
          if (firstImage) {
            thumbnailUrl = firstImage.cdn_url || firstImage.image_url
          }
        }
      }
    }

    // Fallback to original data
    if (!hasVideoCache && !hasImageCache) {
      const primaryImage = shot.images?.find(img => img.isPrimary) || shot.images?.[0]
      if (primaryImage) {
        thumbnailUrl = primaryImage.url
        hasImageCache = true
      }
    }

    // Determine cache source: prioritize video, then image
    let cacheSource = 'none'
    if (hasVideoCache) {
      cacheSource = 'video'
    } else if (hasImageCache) {
      cacheSource = 'image'
    }

    console.log(`🎬 Shot ${shot.shotNumber}:`, {
      shotId: shot.shotId,
      hasVideoCache,
      hasImageCache,
      cacheSource,
      videoUrl: videoUrl ? 'Has video' : 'No video',
      thumbnailUrl: thumbnailUrl !== '/placeholder.svg' ? 'Has thumbnail' : 'No thumbnail'
    })

    return {
      id: `smart-clip-${shot.shotId}`,
      name: `镜头 ${shot.shotNumber}`,
      hasVideoCache,
      hasImageCache,
      videoTaskStatus,
      imageTaskStatus,
      cacheSource,
      videoUrl,
      thumbnailUrl
    }
  })

  return smartClips
}

function analyzeCacheStrategy(smartClips) {
  console.log('\n📈 Cache Strategy Analysis...')
  
  const videoCount = smartClips.filter(c => c.cacheSource === 'video').length
  const imageCount = smartClips.filter(c => c.cacheSource === 'image').length
  const noCacheCount = smartClips.filter(c => c.cacheSource === 'none').length
  
  console.log(`📊 Cache Distribution:`)
  console.log(`  🎥 Video cache: ${videoCount} clips`)
  console.log(`  🖼️ Image cache: ${imageCount} clips`)
  console.log(`  📝 No cache: ${noCacheCount} clips`)
  
  const totalClips = smartClips.length
  const cacheHitRate = ((videoCount + imageCount) / totalClips * 100).toFixed(1)
  
  console.log(`📊 Cache hit rate: ${cacheHitRate}%`)
  
  if (videoCount > 0) {
    console.log('✅ Video cache priority working correctly')
  }
  
  if (imageCount > 0 && videoCount < totalClips) {
    console.log('✅ Image cache fallback working correctly')
  }
  
  return {
    videoCount,
    imageCount,
    noCacheCount,
    cacheHitRate: parseFloat(cacheHitRate)
  }
}

async function main() {
  console.log('🎯 VideoEditingStep Smart Cache Test')
  console.log('====================================')
  console.log(`🔗 Base URL: ${BASE_URL}`)
  console.log(`📋 Test Data:`, JSON.stringify(testData, null, 2))
  
  // Test video cache API
  const videoData = await testVideoCacheAPI()
  
  // Test image cache API
  const imageData = await testImageCacheAPI()
  
  // Simulate smart cache logic
  const smartClips = simulateSmartCacheLogic(videoData, imageData)
  
  // Analyze cache strategy
  const analysis = analyzeCacheStrategy(smartClips)
  
  console.log('\n🎉 Test Summary:')
  console.log(`✅ Video API: ${videoData ? 'Working' : 'Failed'}`)
  console.log(`✅ Image API: ${imageData ? 'Working' : 'Failed'}`)
  console.log(`✅ Smart Logic: ${smartClips.length} clips generated`)
  console.log(`✅ Cache Hit Rate: ${analysis.cacheHitRate}%`)
  
  if (analysis.cacheHitRate > 0) {
    console.log('🎉 Smart cache mechanism working correctly!')
  } else {
    console.log('⚠️ No cache data found - check if data exists in database')
  }
}

// Run the test
main().catch(console.error)
