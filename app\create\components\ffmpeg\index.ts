// FFmpeg 组件统一导出

export { FFmpegProcessor, ffmpegProcessor } from './FFmpegProcessor'
export { VideoProcessor } from './VideoProcessor'
export { VideoExporter } from './VideoExporter'

// 类型导出
export type { VideoProcessingOptions, AudioProcessingOptions, SubtitleOptions } from './FFmpegProcessor'

// FFmpeg 工具函数
export const ffmpegUtils = {
  // 支持的视频格式
  supportedVideoFormats: ['mp4', 'avi', 'mov', 'webm', 'mkv', 'flv', '3gp'],
  
  // 支持的音频格式
  supportedAudioFormats: ['mp3', 'wav', 'aac', 'ogg', 'flac', 'm4a'],
  
  // 支持的图片格式
  supportedImageFormats: ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'webp'],

  // 检查文件格式是否支持
  isVideoFormatSupported: (filename: string): boolean => {
    const extension = filename.toLowerCase().split('.').pop()
    return extension ? ffmpegUtils.supportedVideoFormats.includes(extension) : false
  },

  isAudioFormatSupported: (filename: string): boolean => {
    const extension = filename.toLowerCase().split('.').pop()
    return extension ? ffmpegUtils.supportedAudioFormats.includes(extension) : false
  },

  isImageFormatSupported: (filename: string): boolean => {
    const extension = filename.toLowerCase().split('.').pop()
    return extension ? ffmpegUtils.supportedImageFormats.includes(extension) : false
  },

  // 获取文件扩展名
  getFileExtension: (filename: string): string => {
    return filename.toLowerCase().split('.').pop() || ''
  },

  // 获取文件类型
  getFileType: (filename: string): 'video' | 'audio' | 'image' | 'unknown' => {
    if (ffmpegUtils.isVideoFormatSupported(filename)) return 'video'
    if (ffmpegUtils.isAudioFormatSupported(filename)) return 'audio'
    if (ffmpegUtils.isImageFormatSupported(filename)) return 'image'
    return 'unknown'
  },

  // 格式化文件大小
  formatFileSize: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`
  },

  // 估算处理时间
  estimateProcessingTime: (
    fileSizeBytes: number,
    operation: 'convert' | 'trim' | 'merge' | 'compress',
    quality: 'low' | 'medium' | 'high' | 'ultra'
  ): number => {
    // 基础处理时间（秒/MB）
    const baseTimePerMB = {
      convert: { low: 2, medium: 4, high: 8, ultra: 15 },
      trim: { low: 1, medium: 2, high: 3, ultra: 5 },
      merge: { low: 3, medium: 5, high: 10, ultra: 18 },
      compress: { low: 5, medium: 8, high: 15, ultra: 25 }
    }

    const fileSizeMB = fileSizeBytes / (1024 * 1024)
    const timePerMB = baseTimePerMB[operation][quality]
    
    return Math.max(5, Math.round(fileSizeMB * timePerMB)) // 最少5秒
  },

  // 生成 FFmpeg 命令参数
  generateFFmpegArgs: (
    inputFile: string,
    outputFile: string,
    options: {
      format?: string
      width?: number
      height?: number
      fps?: number
      bitrate?: string
      quality?: 'low' | 'medium' | 'high' | 'ultra'
      startTime?: number
      duration?: number
      audioCodec?: string
      videoCodec?: string
    } = {}
  ): string[] => {
    const args = ['-i', inputFile]

    // 时间裁剪
    if (options.startTime !== undefined) {
      args.push('-ss', options.startTime.toString())
    }
    if (options.duration !== undefined) {
      args.push('-t', options.duration.toString())
    }

    // 视频编码
    if (options.videoCodec) {
      args.push('-c:v', options.videoCodec)
    }

    // 音频编码
    if (options.audioCodec) {
      args.push('-c:a', options.audioCodec)
    }

    // 分辨率
    if (options.width && options.height) {
      args.push('-s', `${options.width}x${options.height}`)
    }

    // 帧率
    if (options.fps) {
      args.push('-r', options.fps.toString())
    }

    // 比特率
    if (options.bitrate) {
      args.push('-b:v', options.bitrate)
    }

    // 质量设置
    if (options.quality) {
      const qualityMap = {
        low: ['-crf', '28'],
        medium: ['-crf', '23'],
        high: ['-crf', '18'],
        ultra: ['-crf', '15']
      }
      args.push(...qualityMap[options.quality])
    }

    args.push(outputFile)
    return args
  },

  // 解析 FFmpeg 输出信息
  parseFFmpegOutput: (output: string): {
    duration?: number
    bitrate?: number
    fps?: number
    resolution?: { width: number; height: number }
    audioSampleRate?: number
    audioChannels?: number
  } => {
    const info: any = {}

    // 解析时长
    const durationMatch = output.match(/Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})/)
    if (durationMatch) {
      const hours = parseInt(durationMatch[1])
      const minutes = parseInt(durationMatch[2])
      const seconds = parseInt(durationMatch[3])
      const centiseconds = parseInt(durationMatch[4])
      info.duration = hours * 3600 + minutes * 60 + seconds + centiseconds / 100
    }

    // 解析比特率
    const bitrateMatch = output.match(/bitrate: (\d+) kb\/s/)
    if (bitrateMatch) {
      info.bitrate = parseInt(bitrateMatch[1])
    }

    // 解析分辨率和帧率
    const videoMatch = output.match(/Video: .+?, (\d+)x(\d+).+?, (\d+(?:\.\d+)?) fps/)
    if (videoMatch) {
      info.resolution = {
        width: parseInt(videoMatch[1]),
        height: parseInt(videoMatch[2])
      }
      info.fps = parseFloat(videoMatch[3])
    }

    // 解析音频信息
    const audioMatch = output.match(/Audio: .+?, (\d+) Hz, .+?, .+?, (\d+) kb\/s/)
    if (audioMatch) {
      info.audioSampleRate = parseInt(audioMatch[1])
    }

    const channelsMatch = output.match(/Audio: .+?, \d+ Hz, (\w+)/)
    if (channelsMatch) {
      const channelMap: { [key: string]: number } = {
        'mono': 1,
        'stereo': 2,
        '2.1': 3,
        '5.1': 6,
        '7.1': 8
      }
      info.audioChannels = channelMap[channelsMatch[1]] || 2
    }

    return info
  },

  // 验证处理选项
  validateProcessingOptions: (options: {
    width?: number
    height?: number
    fps?: number
    bitrate?: string
    quality?: string
  }): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    if (options.width && (options.width < 1 || options.width > 7680)) {
      errors.push('宽度必须在 1-7680 像素之间')
    }

    if (options.height && (options.height < 1 || options.height > 4320)) {
      errors.push('高度必须在 1-4320 像素之间')
    }

    if (options.fps && (options.fps < 1 || options.fps > 120)) {
      errors.push('帧率必须在 1-120 FPS 之间')
    }

    if (options.bitrate && !/^\d+[KMG]?$/.test(options.bitrate)) {
      errors.push('比特率格式无效，应为如 "1M", "500K" 的格式')
    }

    if (options.quality && !['low', 'medium', 'high', 'ultra'].includes(options.quality)) {
      errors.push('质量设置必须是 low, medium, high, ultra 之一')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  // 创建处理任务队列
  createProcessingQueue: () => {
    const queue: Array<{
      id: string
      operation: string
      input: any
      options: any
      status: 'pending' | 'processing' | 'completed' | 'failed'
      progress: number
      result?: any
      error?: string
    }> = []

    return {
      add: (operation: string, input: any, options: any = {}) => {
        const task = {
          id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          operation,
          input,
          options,
          status: 'pending' as const,
          progress: 0
        }
        queue.push(task)
        return task.id
      },

      remove: (id: string) => {
        const index = queue.findIndex(task => task.id === id)
        if (index !== -1) {
          queue.splice(index, 1)
        }
      },

      get: (id: string) => {
        return queue.find(task => task.id === id)
      },

      getAll: () => [...queue],

      updateStatus: (id: string, status: any, progress?: number, result?: any, error?: string) => {
        const task = queue.find(t => t.id === id)
        if (task) {
          task.status = status
          if (progress !== undefined) task.progress = progress
          if (result !== undefined) task.result = result
          if (error !== undefined) task.error = error
        }
      },

      clear: () => {
        queue.length = 0
      }
    }
  }
}
