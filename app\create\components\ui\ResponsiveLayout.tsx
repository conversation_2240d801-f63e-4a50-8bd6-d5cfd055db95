"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  Menu,
  X,
  Maximize2,
  Minimize2,
  Monitor,
  Tablet,
  Smartphone,
  Settings,
  Accessibility,
  Eye,
  EyeOff,
  Volume2,
  VolumeX,
  Contrast,
  Type,
  Palette
} from "lucide-react"

// 响应式断点
const breakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
  wide: 1920
}

// 设备类型检测
const getDeviceType = (width: number): 'mobile' | 'tablet' | 'desktop' | 'wide' => {
  if (width < breakpoints.mobile) return 'mobile'
  if (width < breakpoints.tablet) return 'tablet'
  if (width < breakpoints.desktop) return 'desktop'
  return 'wide'
}

interface ResponsiveLayoutProps {
  children: React.ReactNode
  leftPanel?: React.ReactNode
  rightPanel?: React.ReactNode
  topBar?: React.ReactNode
  bottomBar?: React.ReactNode
  className?: string
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  leftPanel,
  rightPanel,
  topBar,
  bottomBar,
  className = ''
}) => {
  const [windowSize, setWindowSize] = useState({ width: 1920, height: 1080 })
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop' | 'wide'>('desktop')
  const [isLeftPanelOpen, setIsLeftPanelOpen] = useState(false)
  const [isRightPanelOpen, setIsRightPanelOpen] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [accessibilitySettings, setAccessibilitySettings] = useState({
    highContrast: false,
    largeText: false,
    reducedMotion: false,
    screenReader: false,
    keyboardNavigation: true
  })

  // 窗口大小监听
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      setWindowSize({ width, height })
      setDeviceType(getDeviceType(width))
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 全屏切换
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }, [])

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  // 键盘导航支持
  useEffect(() => {
    if (!accessibilitySettings.keyboardNavigation) return

    const handleKeyDown = (e: KeyboardEvent) => {
      // ESC 键关闭面板
      if (e.key === 'Escape') {
        setIsLeftPanelOpen(false)
        setIsRightPanelOpen(false)
      }

      // Tab 键导航增强
      if (e.key === 'Tab') {
        // 确保焦点可见
        document.body.classList.add('keyboard-navigation')
      }

      // 快捷键支持
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '1':
            e.preventDefault()
            setIsLeftPanelOpen(!isLeftPanelOpen)
            break
          case '2':
            e.preventDefault()
            setIsRightPanelOpen(!isRightPanelOpen)
            break
          case 'f':
            e.preventDefault()
            toggleFullscreen()
            break
        }
      }
    }

    const handleMouseDown = () => {
      document.body.classList.remove('keyboard-navigation')
    }

    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('mousedown', handleMouseDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('mousedown', handleMouseDown)
    }
  }, [accessibilitySettings.keyboardNavigation, isLeftPanelOpen, isRightPanelOpen, toggleFullscreen])

  // 应用无障碍设置
  useEffect(() => {
    const root = document.documentElement

    // 高对比度
    if (accessibilitySettings.highContrast) {
      root.classList.add('high-contrast')
    } else {
      root.classList.remove('high-contrast')
    }

    // 大字体
    if (accessibilitySettings.largeText) {
      root.classList.add('large-text')
    } else {
      root.classList.remove('large-text')
    }

    // 减少动画
    if (accessibilitySettings.reducedMotion) {
      root.classList.add('reduced-motion')
    } else {
      root.classList.remove('reduced-motion')
    }

    // 屏幕阅读器支持
    if (accessibilitySettings.screenReader) {
      root.setAttribute('aria-live', 'polite')
    } else {
      root.removeAttribute('aria-live')
    }
  }, [accessibilitySettings])

  // 移动端布局
  if (deviceType === 'mobile') {
    return (
      <div className={`responsive-layout mobile ${className}`}>
        {/* 移动端顶部栏 */}
        <div className="flex items-center justify-between p-4 bg-white border-b">
          <Sheet open={isLeftPanelOpen} onOpenChange={setIsLeftPanelOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm">
                <Menu className="w-4 h-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80">
              <ScrollArea className="h-full">
                {leftPanel}
              </ScrollArea>
            </SheetContent>
          </Sheet>

          <div className="flex-1 text-center">
            {topBar}
          </div>

          <Sheet open={isRightPanelOpen} onOpenChange={setIsRightPanelOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-80">
              <ScrollArea className="h-full">
                {rightPanel}
              </ScrollArea>
            </SheetContent>
          </Sheet>
        </div>

        {/* 主内容区域 */}
        <div className="flex-1 overflow-hidden">
          {children}
        </div>

        {/* 移动端底部栏 */}
        {bottomBar && (
          <div className="bg-white border-t p-2">
            {bottomBar}
          </div>
        )}
      </div>
    )
  }

  // 平板端布局
  if (deviceType === 'tablet') {
    return (
      <div className={`responsive-layout tablet ${className}`}>
        {/* 顶部栏 */}
        {topBar && (
          <div className="bg-white border-b p-4">
            {topBar}
          </div>
        )}

        <div className="flex flex-1 min-h-0">
          {/* 左侧面板 */}
          {leftPanel && (
            <div className={`bg-white border-r transition-all duration-300 ${
              isLeftPanelOpen ? 'w-80' : 'w-16'
            }`}>
              <div className="p-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsLeftPanelOpen(!isLeftPanelOpen)}
                  className="w-full"
                >
                  {isLeftPanelOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
                </Button>
              </div>
              {isLeftPanelOpen && (
                <ScrollArea className="h-full px-2">
                  {leftPanel}
                </ScrollArea>
              )}
            </div>
          )}

          {/* 主内容区域 */}
          <div className="flex-1 overflow-hidden">
            {children}
          </div>

          {/* 右侧面板 */}
          {rightPanel && (
            <div className={`bg-white border-l transition-all duration-300 ${
              isRightPanelOpen ? 'w-80' : 'w-16'
            }`}>
              <div className="p-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsRightPanelOpen(!isRightPanelOpen)}
                  className="w-full"
                >
                  {isRightPanelOpen ? <X className="w-4 h-4" /> : <Settings className="w-4 h-4" />}
                </Button>
              </div>
              {isRightPanelOpen && (
                <ScrollArea className="h-full px-2">
                  {rightPanel}
                </ScrollArea>
              )}
            </div>
          )}
        </div>

        {/* 底部栏 */}
        {bottomBar && (
          <div className="bg-white border-t p-4">
            {bottomBar}
          </div>
        )}
      </div>
    )
  }

  // 桌面端布局
  return (
    <div className={`responsive-layout desktop ${className}`}>
      {/* 顶部栏 */}
      {topBar && (
        <div className="bg-white border-b p-4 flex items-center justify-between">
          <div className="flex-1">
            {topBar}
          </div>
          
          {/* 布局控制 */}
          <div className="flex items-center space-x-2 ml-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsLeftPanelOpen(!isLeftPanelOpen)}
              title="切换左侧面板 (Ctrl+1)"
            >
              <Menu className="w-4 h-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsRightPanelOpen(!isRightPanelOpen)}
              title="切换右侧面板 (Ctrl+2)"
            >
              <Settings className="w-4 h-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullscreen}
              title="全屏模式 (Ctrl+F)"
            >
              {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </Button>

            {/* 无障碍设置 */}
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm" title="无障碍设置">
                  <Accessibility className="w-4 h-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-96">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">无障碍设置</h3>
                    
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Contrast className="w-4 h-4" />
                          <span className="text-sm">高对比度</span>
                        </div>
                        <Button
                          variant={accessibilitySettings.highContrast ? "default" : "outline"}
                          size="sm"
                          onClick={() => setAccessibilitySettings(prev => ({
                            ...prev,
                            highContrast: !prev.highContrast
                          }))}
                        >
                          {accessibilitySettings.highContrast ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                        </Button>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Type className="w-4 h-4" />
                          <span className="text-sm">大字体</span>
                        </div>
                        <Button
                          variant={accessibilitySettings.largeText ? "default" : "outline"}
                          size="sm"
                          onClick={() => setAccessibilitySettings(prev => ({
                            ...prev,
                            largeText: !prev.largeText
                          }))}
                        >
                          {accessibilitySettings.largeText ? "开" : "关"}
                        </Button>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Palette className="w-4 h-4" />
                          <span className="text-sm">减少动画</span>
                        </div>
                        <Button
                          variant={accessibilitySettings.reducedMotion ? "default" : "outline"}
                          size="sm"
                          onClick={() => setAccessibilitySettings(prev => ({
                            ...prev,
                            reducedMotion: !prev.reducedMotion
                          }))}
                        >
                          {accessibilitySettings.reducedMotion ? "开" : "关"}
                        </Button>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Volume2 className="w-4 h-4" />
                          <span className="text-sm">屏幕阅读器</span>
                        </div>
                        <Button
                          variant={accessibilitySettings.screenReader ? "default" : "outline"}
                          size="sm"
                          onClick={() => setAccessibilitySettings(prev => ({
                            ...prev,
                            screenReader: !prev.screenReader
                          }))}
                        >
                          {accessibilitySettings.screenReader ? "开" : "关"}
                        </Button>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="text-sm font-semibold mb-2">设备信息</h4>
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        {deviceType === 'mobile' && <Smartphone className="w-4 h-4" />}
                        {deviceType === 'tablet' && <Tablet className="w-4 h-4" />}
                        {(deviceType === 'desktop' || deviceType === 'wide') && <Monitor className="w-4 h-4" />}
                        <span>设备类型: {deviceType}</span>
                      </div>
                      <div>屏幕尺寸: {windowSize.width} × {windowSize.height}</div>
                      <div>像素比: {window.devicePixelRatio}</div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="text-sm font-semibold mb-2">快捷键</h4>
                    <div className="space-y-1 text-xs text-gray-600">
                      <div>Ctrl+1: 切换左侧面板</div>
                      <div>Ctrl+2: 切换右侧面板</div>
                      <div>Ctrl+F: 全屏模式</div>
                      <div>ESC: 关闭面板</div>
                      <div>Tab: 键盘导航</div>
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      )}

      <div className="flex flex-1 min-h-0">
        {/* 左侧面板 */}
        {leftPanel && isLeftPanelOpen && (
          <div className="w-80 bg-white border-r">
            <ScrollArea className="h-full">
              {leftPanel}
            </ScrollArea>
          </div>
        )}

        {/* 主内容区域 */}
        <div className="flex-1 overflow-hidden">
          {children}
        </div>

        {/* 右侧面板 */}
        {rightPanel && isRightPanelOpen && (
          <div className="w-80 bg-white border-l">
            <ScrollArea className="h-full">
              {rightPanel}
            </ScrollArea>
          </div>
        )}
      </div>

      {/* 底部栏 */}
      {bottomBar && (
        <div className="bg-white border-t p-4">
          {bottomBar}
        </div>
      )}

      {/* 设备类型指示器 */}
      <div className="fixed bottom-4 left-4 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-50">
        {deviceType} ({windowSize.width}×{windowSize.height})
      </div>
    </div>
  )
}
